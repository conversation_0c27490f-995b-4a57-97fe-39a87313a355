{"info": {"_postman_id": "b1c2d3e4-f5g6-7890-abcd-ef1234567890", "name": "Active User Tests", "description": "Tests for the active user check", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Login (Active User)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"email\": \"{{ACTIVE_USER_EMAIL}}\",\n    \"password\": \"{{ACTIVE_USER_PASSWORD}}\"\n}"}, "url": {"raw": "{{BASE_URL}}/api/v1/auth/login", "host": ["{{BASE_URL}}"], "path": ["api", "v1", "auth", "login"]}, "description": "Login with an active user (should succeed)"}, "response": []}, {"name": "Login (Inactive User)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"email\": \"{{INACTIVE_USER_EMAIL}}\",\n    \"password\": \"{{INACTIVE_USER_PASSWORD}}\"\n}"}, "url": {"raw": "{{BASE_URL}}/api/v1/auth/login", "host": ["{{BASE_URL}}"], "path": ["api", "v1", "auth", "login"]}, "description": "Login with an inactive user (should fail with 401 Unauthorized)"}, "response": []}, {"name": "Access Protected Route (Active User)", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{ACTIVE_USER_TOKEN}}", "type": "text"}], "url": {"raw": "{{BASE_URL}}/api/v1/auth/me", "host": ["{{BASE_URL}}"], "path": ["api", "v1", "auth", "me"]}, "description": "Access a protected route with an active user's token (should succeed)"}, "response": []}, {"name": "Access Protected Route (Inactive User)", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{INACTIVE_USER_TOKEN}}", "type": "text"}], "url": {"raw": "{{BASE_URL}}/api/v1/auth/me", "host": ["{{BASE_URL}}"], "path": ["api", "v1", "auth", "me"]}, "description": "Access a protected route with an inactive user's token (should fail with 401 Unauthorized)"}, "response": []}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": ["// Test for active user login (should succeed)", "if (pm.info.requestName === 'Login (Active User)') {", "    pm.test(\"Status code is 200\", function () {", "        pm.response.to.have.status(200);", "    });", "    ", "    pm.test(\"Response has success status\", function () {", "        var jsonData = pm.response.json();", "        pm.expect(jsonData.status).to.eql(\"success\");", "    });", "    ", "    pm.test(\"Response has token\", function () {", "        var jsonData = pm.response.json();", "        pm.expect(jsonData.token).to.exist;", "        pm.environment.set(\"ACTIVE_USER_TOKEN\", jsonData.token);", "    });", "}", "", "// Test for inactive user login (should fail)", "if (pm.info.requestName === 'Login (Inactive User)') {", "    pm.test(\"Status code is 401 Unauthorized\", function () {", "        pm.response.to.have.status(401);", "    });", "    ", "    pm.test(\"Response has error status\", function () {", "        var jsonData = pm.response.json();", "        pm.expect(jsonData.status).to.eql(\"fail\");", "    });", "    ", "    pm.test(\"Error message mentions deactivated account\", function () {", "        var jsonData = pm.response.json();", "        pm.expect(jsonData.message).to.include(\"deactivated\");", "    });", "}", "", "// Test for active user accessing protected route (should succeed)", "if (pm.info.requestName === 'Access Protected Route (Active User)') {", "    pm.test(\"Status code is 200\", function () {", "        pm.response.to.have.status(200);", "    });", "    ", "    pm.test(\"Response has success status\", function () {", "        var jsonData = pm.response.json();", "        pm.expect(jsonData.status).to.eql(\"success\");", "    });", "}", "", "// Test for inactive user accessing protected route (should fail)", "if (pm.info.requestName === 'Access Protected Route (Inactive User)') {", "    pm.test(\"Status code is 401 Unauthorized\", function () {", "        pm.response.to.have.status(401);", "    });", "    ", "    pm.test(\"Response has error status\", function () {", "        var jsonData = pm.response.json();", "        pm.expect(jsonData.status).to.eql(\"fail\");", "    });", "    ", "    pm.test(\"Error message mentions deactivated account\", function () {", "        var jsonData = pm.response.json();", "        pm.expect(jsonData.message).to.include(\"deactivated\");", "    });", "}"]}}], "variable": [{"key": "BASE_URL", "value": "http://localhost:5000", "type": "string"}, {"key": "ACTIVE_USER_EMAIL", "value": "", "type": "string", "description": "Email of an active user"}, {"key": "ACTIVE_USER_PASSWORD", "value": "", "type": "string", "description": "Password of an active user"}, {"key": "INACTIVE_USER_EMAIL", "value": "", "type": "string", "description": "Email of an inactive user"}, {"key": "INACTIVE_USER_PASSWORD", "value": "", "type": "string", "description": "Password of an inactive user"}, {"key": "ACTIVE_USER_TOKEN", "value": "", "type": "string", "description": "JWT token for an active user"}, {"key": "INACTIVE_USER_TOKEN", "value": "", "type": "string", "description": "JWT token for an inactive user"}]}