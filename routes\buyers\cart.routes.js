const express = require('express');
const cartController = require('../../controllers/buyers/cart.controller');


const router = express.Router();


// Cart routes
router.get('/', cartController.getCart);
router.delete('/', cartController.clearCart);

// Cart items - general
router.post('/products', cartController.addProductToCart);
router.post('/bales', cartController.addBaleToCart);
router.patch('/items/:id', cartController.updateCartItem);

// Product-specific routes
router.patch('/products/:productId/:variationId', cartController.updateProductQuantity);
router.delete('/products/:productId/:variationId', cartController.removeProduct);

// Bale-specific routes
router.patch('/bales/:baleId/:variationId', cartController.updateBaleQuantity);
router.delete('/bales/:id/:variationId', cartController.removeBaleFromCart);

// Coupon
// router.post('/apply-coupon', cartController.applyCoupon);
// router.delete('/remove-coupon', cartController.removeCoupon);

module.exports = router;



