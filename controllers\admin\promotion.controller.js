const Promotion = require('../../models/promotion.model');
const Product = require('../../models/product.model');
const { Creator } = require('../../models/user.model');
const AppError = require('../../utils/appError');
const catchAsync = require('../../utils/catchAsync');

/**
 * Get all promotions
 * @route GET /api/v1/admin/promotions
 * @access Private (Admin only)
 */
exports.getAllPromotions = catchAsync(async (req, res, next) => {
  // Build query
  const queryObj = { ...req.query };
  const excludedFields = ['page', 'sort', 'limit', 'fields', 'search'];
  excludedFields.forEach(el => delete queryObj[el]);

  // Advanced filtering
  let queryStr = JSON.stringify(queryObj);
  queryStr = queryStr.replace(/\b(gte|gt|lte|lt)\b/g, match => `$${match}`);
  
  let query = Promotion.find(JSON.parse(queryStr));

  // Search functionality
  if (req.query.search) {
    const searchRegex = new RegExp(req.query.search, 'i');
    query = query.find({
      $or: [
        { name: searchRegex },
        { description: searchRegex },
        { code: searchRegex }
      ]
    });
  }

  // Filter by active status if specified
  if (req.query.isActive) {
    query = query.find({ isActive: req.query.isActive === 'true' });
  }

  // Filter by type if specified
  if (req.query.type) {
    query = query.find({ type: req.query.type });
  }

  // Count total before applying pagination
  const total = await Promotion.countDocuments(query);

  // Sorting
  if (req.query.sort) {
    const sortBy = req.query.sort.split(',').join(' ');
    query = query.sort(sortBy);
  } else {
    query = query.sort('-createdAt');
  }

  // Field limiting
  if (req.query.fields) {
    const fields = req.query.fields.split(',').join(' ');
    query = query.select(fields);
  } else {
    query = query.select('-__v');
  }

  // Pagination
  const page = req.query.page * 1 || 1;
  const limit = req.query.limit * 1 || 20;
  const skip = (page - 1) * limit;

  query = query.skip(skip).limit(limit);

  // Execute query
  const promotions = await query;

  res.status(200).json({
    status: 'success',
    results: promotions.length,
    total,
    page,
    limit,
    data: {
      promotions
    }
  });
});

/**
 * Get promotion by ID
 * @route GET /api/v1/admin/promotions/:id
 * @access Private (Admin only)
 */
exports.getPromotion = catchAsync(async (req, res, next) => {
  const promotion = await Promotion.findById(req.params.id);

  if (!promotion) {
    return next(new AppError('No promotion found with that ID', 404));
  }

  res.status(200).json({
    status: 'success',
    data: {
      promotion
    }
  });
});

/**
 * Create a new promotion
 * @route POST /api/v1/admin/promotions
 * @access Private (Admin only)
 */
exports.createPromotion = catchAsync(async (req, res, next) => {
  // Validate required fields
  const requiredFields = ['name', 'type', 'startDate', 'endDate'];
  for (const field of requiredFields) {
    if (!req.body[field]) {
      return next(new AppError(`Please provide ${field}`, 400));
    }
  }

  // Validate dates
  const startDate = new Date(req.body.startDate);
  const endDate = new Date(req.body.endDate);

  if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
    return next(new AppError('Invalid dates', 400));
  }

  if (startDate >= endDate) {
    return next(new AppError('End date must be after start date', 400));
  }

  // Validate type-specific fields
  if (req.body.type === 'percentage') {
    if (!req.body.percentage || req.body.percentage <= 0 || req.body.percentage > 100) {
      return next(new AppError('Please provide a valid percentage between 0 and 100', 400));
    }
  } else if (req.body.type === 'fixed') {
    if (!req.body.amount || req.body.amount <= 0) {
      return next(new AppError('Please provide a valid amount greater than 0', 400));
    }
  } else {
    return next(new AppError('Please provide a valid type (percentage or fixed)', 400));
  }

  // Generate code if not provided
  if (!req.body.code) {
    req.body.code = `PROMO-${Math.random().toString(36).substring(2, 8).toUpperCase()}`;
  }

  // Create the promotion
  const newPromotion = await Promotion.create(req.body);

  res.status(201).json({
    status: 'success',
    data: {
      promotion: newPromotion
    }
  });
});

/**
 * Update promotion by ID
 * @route PATCH /api/v1/admin/promotions/:id
 * @access Private (Admin only)
 */
exports.updatePromotion = catchAsync(async (req, res, next) => {
  // Find promotion
  const promotion = await Promotion.findById(req.params.id);

  if (!promotion) {
    return next(new AppError('No promotion found with that ID', 404));
  }

  // Validate dates if provided
  if (req.body.startDate || req.body.endDate) {
    const startDate = new Date(req.body.startDate || promotion.startDate);
    const endDate = new Date(req.body.endDate || promotion.endDate);

    if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
      return next(new AppError('Invalid dates', 400));
    }

    if (startDate >= endDate) {
      return next(new AppError('End date must be after start date', 400));
    }
  }

  // Validate type-specific fields
  if (req.body.type === 'percentage') {
    if (req.body.percentage && (req.body.percentage <= 0 || req.body.percentage > 100)) {
      return next(new AppError('Please provide a valid percentage between 0 and 100', 400));
    }
  } else if (req.body.type === 'fixed') {
    if (req.body.amount && req.body.amount <= 0) {
      return next(new AppError('Please provide a valid amount greater than 0', 400));
    }
  } else if (req.body.type) {
    return next(new AppError('Please provide a valid type (percentage or fixed)', 400));
  }

  // Create a filtered body to prevent unwanted fields
  const filteredBody = {};
  const allowedFields = ['name', 'description', 'type', 'percentage', 'amount', 'code', 'startDate', 'endDate', 'isActive', 'minPurchase', 'maxDiscount', 'usageLimit', 'perUserLimit'];

  Object.keys(req.body).forEach(key => {
    if (allowedFields.includes(key)) {
      filteredBody[key] = req.body[key];
    }
  });

  // Update the promotion
  const updatedPromotion = await Promotion.findByIdAndUpdate(
    req.params.id,
    filteredBody,
    {
      new: true,
      runValidators: true
    }
  );

  res.status(200).json({
    status: 'success',
    data: {
      promotion: updatedPromotion
    }
  });
});

/**
 * Delete promotion by ID
 * @route DELETE /api/v1/admin/promotions/:id
 * @access Private (Admin only)
 */
exports.deletePromotion = catchAsync(async (req, res, next) => {
  // Find promotion
  const promotion = await Promotion.findById(req.params.id);

  if (!promotion) {
    return next(new AppError('No promotion found with that ID', 404));
  }

  // Delete the promotion
  await Promotion.findByIdAndDelete(req.params.id);

  res.status(204).json({
    status: 'success',
    data: null
  });
});

/**
 * Update participant status in promotion
 * @route PATCH /api/v1/admin/promotions/:id/participants/:participantId
 * @access Private (Admin only)
 */
exports.updateParticipantStatus = catchAsync(async (req, res, next) => {
  // Validate request body
  if (!req.body.status || !['pending', 'approved', 'rejected'].includes(req.body.status)) {
    return next(new AppError('Please provide a valid status (pending, approved, rejected)', 400));
  }

  // Find promotion
  const promotion = await Promotion.findById(req.params.id);

  if (!promotion) {
    return next(new AppError('No promotion found with that ID', 404));
  }

  // Find participant
  const participantIndex = promotion.participants.findIndex(
    p => p._id.toString() === req.params.participantId
  );

  if (participantIndex === -1) {
    return next(new AppError('No participant found with that ID', 404));
  }

  // Update participant status
  promotion.participants[participantIndex].status = req.body.status;

  // Add notes if provided
  if (req.body.notes) {
    promotion.participants[participantIndex].notes = req.body.notes;
  }

  // Save the updated promotion
  await promotion.save();

  res.status(200).json({
    status: 'success',
    data: {
      participant: promotion.participants[participantIndex]
    }
  });
});

/**
 * Get promotion statistics
 * @route GET /api/v1/admin/promotions/stats
 * @access Private (Admin only)
 */
exports.getPromotionStats = catchAsync(async (req, res, next) => {
  const stats = await Promotion.aggregate([
    {
      $group: {
        _id: '$type',
        count: { $sum: 1 },
        avgDiscount: {
          $avg: {
            $cond: [
              { $eq: ['$type', 'percentage'] },
              '$percentage',
              '$amount'
            ]
          }
        }
      }
    },
    {
      $sort: { count: -1 }
    }
  ]);

  const totalPromotions = await Promotion.countDocuments();
  const activePromotions = await Promotion.countDocuments({ isActive: true });
  const inactivePromotions = await Promotion.countDocuments({ isActive: false });
  const currentPromotions = await Promotion.countDocuments({
    startDate: { $lte: new Date() },
    endDate: { $gte: new Date() }
  });

  res.status(200).json({
    status: 'success',
    data: {
      stats,
      summary: {
        total: totalPromotions,
        active: activePromotions,
        inactive: inactivePromotions,
        current: currentPromotions
      }
    }
  });
});
