const Product = require('../models/product.model');
const { Creator } = require('../models/user.model');
const Category = require('../models/category.model');
const AppError = require('../utils/appError');
const catchAsync = require('../utils/catchAsync');

/**
 * Search products and bales
 * @route GET /api/v1/search
 * @access Public
 */
exports.search = catchAsync(async (req, res, next) => {
  // Get search query
  const query = req.query.q;
  if (!query) {
    return next(new AppError('Please provide a search query', 400));
  }

  // Get search type (default to 'all')
  const type = req.query.type || 'all';
  
  // Get search filters
  const filters = {};
  
  // Add category filter if provided
  if (req.query.category) {
    filters.category = req.query.category;
  }
  
  // Add price range filter if provided
  if (req.query.minPrice || req.query.maxPrice) {
    filters.basePrice = {};
    if (req.query.minPrice) {
      filters.basePrice.$gte = parseFloat(req.query.minPrice);
    }
    if (req.query.maxPrice) {
      filters.basePrice.$lte = parseFloat(req.query.maxPrice);
    }
  }
  
  // Add creator filter if provided
  if (req.query.creator) {
    filters.creator = req.query.creator;
  }
  
  // Add gender filter if provided
  if (req.query.gender) {
    filters.gender = req.query.gender;
  }
  
  // Add brand filter for products if provided
  if (req.query.brand) {
    filters.brand = req.query.brand;
  }
  
  // Add country filter for bales if provided
  const baleFilters = { ...filters };
  if (req.query.country) {
    baleFilters.country = req.query.country;
  }
  
  // Add condition filter for bales if provided
  if (req.query.condition) {
    baleFilters.condition = req.query.condition;
  }
  
  // Create search regex
  const searchRegex = new RegExp(query, 'i');
  
  // Set pagination parameters
  const page = parseInt(req.query.page) || 1;
  const limit = parseInt(req.query.limit) || 20;
  const skip = (page - 1) * limit;
  
  // Set sort parameter (default to newest)
  let sortBy = '-createdAt';
  if (req.query.sort) {
    switch (req.query.sort) {
      case 'price-asc':
        sortBy = 'basePrice';
        break;
      case 'price-desc':
        sortBy = '-basePrice';
        break;
      case 'name-asc':
        sortBy = 'name';
        break;
      case 'name-desc':
        sortBy = '-name';
        break;
      case 'rating-desc':
        sortBy = '-ratingsAverage';
        break;
      case 'discount-desc':
        // Will handle discount sorting after fetching items
        sortBy = '-createdAt'; // Default sort for now
        break;
      case 'ending-soon':
        // Will handle ending soon sorting after fetching items
        sortBy = '-createdAt'; // Default sort for now
        break;
      default:
        sortBy = '-createdAt';
    }
  }
  
  // Initialize results
  let products = [];
  let bales = [];
  let creators = [];
  let total = 0;
  
  // Search products
  if (type === 'all' || type === 'products') {
    const productQuery = {
      $or: [
        { name: searchRegex },
        { description: searchRegex },
        { 'specifications.mainMaterial': searchRegex },
        { brand: searchRegex },
        { tags: searchRegex }
      ],
      status: 'active',
      ...filters
    };
    
    products = await Product.find(productQuery)
      .sort(sortBy)
      .skip(skip)
      .limit(type === 'all' ? Math.floor(limit / 2) : limit)
      .select('name brand basePrice images ratingsAverage gender variations');
    
    if (type === 'products') {
      total = await Product.countDocuments(productQuery);
    }
  }
  
  // Search bales
  if (type === 'all' || type === 'bales') {
    const baleQuery = {
      $or: [
        { name: searchRegex },
        { description: searchRegex },
        { country: searchRegex },
        { tags: searchRegex }
      ],
      status: 'active',
      ...baleFilters
    };
    
    bales = await Bale.find(baleQuery)
      .sort(sortBy)
      .skip(skip)
      .limit(type === 'all' ? Math.floor(limit / 2) : limit)
      .select('name basePrice images ratingsAverage country condition variations');
    
    if (type === 'bales') {
      total = await Bale.countDocuments(baleQuery);
    }
  }
  
  // Search creators
  if (type === 'all' || type === 'creators') {
    const creatorQuery = {
      $or: [
        { name: searchRegex },
        { 'shopInfo.name': searchRegex },
        { 'businessInfo.businessName': searchRegex }
      ],
      verificationStatus: 'verified',
      onboardingStatus: 'completed'
    };
    
    creators = await Creator.find(creatorQuery)
      .sort('-metrics.totalProducts')
      .skip(skip)
      .limit(type === 'all' ? Math.floor(limit / 3) : limit)
      .select('name photo shopInfo metrics');
    
    if (type === 'creators') {
      total = await Creator.countDocuments(creatorQuery);
    }
  }
  
  // Calculate total for 'all' type
  if (type === 'all') {
    const productCount = await Product.countDocuments({
      $or: [
        { name: searchRegex },
        { description: searchRegex },
        { 'specifications.mainMaterial': searchRegex },
        { brand: searchRegex },
        { tags: searchRegex }
      ],
      status: 'active',
      ...filters
    });
    
    const baleCount = await Bale.countDocuments({
      $or: [
        { name: searchRegex },
        { description: searchRegex },
        { country: searchRegex },
        { tags: searchRegex }
      ],
      status: 'active',
      ...baleFilters
    });
    
    const creatorCount = await Creator.countDocuments({
      $or: [
        { name: searchRegex },
        { 'shopInfo.name': searchRegex },
        { 'businessInfo.businessName': searchRegex }
      ],
      verificationStatus: 'verified',
      onboardingStatus: 'completed'
    });
    
    total = productCount + baleCount + creatorCount;
  }
  
  // Process products to add type and sale information
  const now = new Date();
  const processedProducts = products.map(product => {
    const productObj = product.toObject();
    
    // Calculate discount and time left information
    let maxDiscountPercentage = 0;
    let saleEndDate = null;
    let hoursLeft = 0;
    
    if (product.variations && product.variations.length > 0) {
      // Find variations with active sales
      const salesVariations = product.variations.filter(v =>
        v.salePrice && v.saleStartDate && v.saleEndDate &&
        now >= v.saleStartDate && now <= v.saleEndDate
      );
      
      // Calculate max discount percentage
      for (const variation of salesVariations) {
        const discountPercentage = ((variation.price - variation.salePrice) / variation.price) * 100;
        maxDiscountPercentage = Math.max(maxDiscountPercentage, discountPercentage);
      }
      
      // Find the earliest end date
      if (salesVariations.length > 0) {
        saleEndDate = new Date(Math.min(...salesVariations.map(v => v.saleEndDate.getTime())));
        hoursLeft = Math.ceil((saleEndDate - now) / (1000 * 60 * 60));
      }
    }
    
    // Return simplified product object for card display
    return {
      _id: productObj._id,
      type: 'product',
      name: productObj.name,
      images: productObj.images && productObj.images.length > 0 ? [productObj.images] : [],
      basePrice: productObj.basePrice,
      discountPercentage: maxDiscountPercentage > 0 ? Math.round(maxDiscountPercentage) : 0,
      isOnSale: maxDiscountPercentage > 0,
      isEndingSoon: maxDiscountPercentage > 0 && hoursLeft <= 48,
      ratingsAverage: productObj.ratingsAverage || 0,
      brand: productObj.brand
    };
  });
  
  // Process bales to add type and sale information
  const processedBales = bales.map(bale => {
    const baleObj = bale.toObject();
    
    // Calculate discount and time left information
    let maxDiscountPercentage = 0;
    let saleEndDate = null;
    let hoursLeft = 0;
    
    if (bale.variations && bale.variations.length > 0) {
      // Find variations with active sales
      const salesVariations = bale.variations.filter(v =>
        v.salePrice && v.saleStartDate && v.saleEndDate &&
        now >= v.saleStartDate && now <= v.saleEndDate
      );
      
      // Calculate max discount percentage
      for (const variation of salesVariations) {
        const discountPercentage = ((variation.price - variation.salePrice) / variation.price) * 100;
        maxDiscountPercentage = Math.max(maxDiscountPercentage, discountPercentage);
      }
      
      // Find the earliest end date
      if (salesVariations.length > 0) {
        saleEndDate = new Date(Math.min(...salesVariations.map(v => v.saleEndDate.getTime())));
        // Calculate hours left
        hoursLeft = Math.ceil((saleEndDate - now) / (1000 * 60 * 60));
      }
    }
    
    // Return simplified bale object for card display
    return {
      _id: baleObj._id,
      type: 'bale',
      name: baleObj.name,
      images: baleObj.images && baleObj.images.length > 0 ? [baleObj.images] : [],
      basePrice: baleObj.basePrice,
      discountPercentage: maxDiscountPercentage > 0 ? Math.round(maxDiscountPercentage) : 0,
      isOnSale: maxDiscountPercentage > 0,
      isEndingSoon: maxDiscountPercentage > 0 && hoursLeft <= 48,
      ratingsAverage: baleObj.ratingsAverage || 0,
      country: baleObj.country,
      condition: baleObj.condition
    };
  });
  
  // Process creators to add type
  const processedCreators = creators.map(creator => {
    const creatorObj = creator.toObject();
    creatorObj.type = 'creator';
    return creatorObj;
  });
  
  // Apply special sorting if needed
  let allItems = [...processedProducts, ...processedBales];
  
  if (req.query.sort === 'discount-desc') {
    allItems.sort((a, b) => {
      const discountA = a.discountPercentage || 0;
      const discountB = b.discountPercentage || 0;
      return discountB - discountA;
    });
  } else if (req.query.sort === 'ending-soon') {
    // First filter items on sale
    const saleItems = allItems.filter(item => item.isOnSale);
    const nonSaleItems = allItems.filter(item => !item.isOnSale);
    
    // Sort sale items by hours left
    saleItems.sort((a, b) => a.hoursLeft - b.hoursLeft);
    
    // Combine sorted sale items with non-sale items
    allItems = [...saleItems, ...nonSaleItems];
  }
  
  // Apply pagination if special sorting was used
  if (['discount-desc', 'ending-soon'].includes(req.query.sort)) {
    const startIndex = skip;
    const endIndex = skip + limit;
    
    if (type === 'all') {
      // For 'all' type, we need to handle products and bales separately
      processedProducts = allItems
        .filter(item => item.type === 'product')
        .slice(0, Math.floor(limit / 2));
      
      processedBales = allItems
        .filter(item => item.type === 'bale')
        .slice(0, Math.floor(limit / 2));
    } else if (type === 'products') {
      processedProducts = allItems
        .filter(item => item.type === 'product')
        .slice(startIndex, endIndex);
    } else if (type === 'bales') {
      processedBales = allItems
        .filter(item => item.type === 'bale')
        .slice(startIndex, endIndex);
    }
  }
  
  res.status(200).json({
    status: 'success',
    results: processedProducts.length + processedBales.length + processedCreators.length,
    total,
    page,
    limit,
    data: {
      products: processedProducts,
      bales: processedBales,
      creators: processedCreators
    }
  });
});




