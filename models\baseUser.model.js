const mongoose = require('mongoose');
const validator = require('validator');
const bcrypt = require('bcryptjs');
const crypto = require('crypto');

// Create a base schema with common fields for all user types
const baseOptions = {
  discriminatorKey: 'userType',
  collection: 'users',
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
};

const baseUserSchema = new mongoose.Schema(
  {
    name: {
      type: String,
      required: [true, 'Please tell us your name!']
    },
    email: {
      type: String,
      required: [true, 'Please provide your email'],
      unique: true,
      lowercase: true,
      validate: [validator.isEmail, 'Please provide a valid email']
    },
    photo: {
      type: String,
    },
    role: {
      type: String,
      enum: ['buyer', 'creator', 'admin'],
      required: [true, 'User must have a role']
    },
    // Added socialProvider field to track users who signed up with social auth
    socialProvider: {
      type: String,
      enum: ['google', 'facebook', null],
      default: null
    },
    password: {
      type: String,
      select: false
    },
    passwordChangedAt: Date,
    passwordResetToken: String,
    passwordResetExpires: Date,
    emailVerified: {
      type: Boolean,
      default: false
    },
    emailVerificationToken: String,
    emailVerificationExpires: Date,
    active: {
      type: Boolean,
      default: true,
      select: false
    }
  },
  baseOptions
);

// Virtual populate for user's orders
baseUserSchema.virtual('orders', {
  ref: 'Order',
  foreignField: 'user',
  localField: '_id'
});

// Virtual populate for user's notifications
baseUserSchema.virtual('notifications', {
  ref: 'Notification',
  foreignField: 'recipient',
  localField: '_id'
});

// Method to get unread notifications count
baseUserSchema.methods.getUnreadNotificationsCount = async function() {
  try {
    // Dynamically require the model to avoid circular dependency issues
    const Notification = require('./notification.model');
    return await Notification.countDocuments({ recipient: this._id, read: false });
  } catch (error) {
    console.error('Error getting unread notifications count:', error);
    return 0;
  }
};

// Method to get notification preferences
baseUserSchema.methods.getNotificationPreferences = async function() {
  try {
    // Dynamically require the model to avoid circular dependency issues
    const NotificationPreference = require('./notificationPreference.model');
    return await NotificationPreference.getForUser(this._id);
  } catch (error) {
    console.error('Error getting notification preferences:', error);
    return null;
  }
};

// Document middleware: runs before .save() and .create()
baseUserSchema.pre('save', async function(next) {
  // Skip for social auth users or if password wasn't modified
  if (this.socialProvider || !this.isModified('password')) return next();

  // Only hash the password if it exists (for non-social auth users)
  if (this.password) {
    // Hash the password with cost of 12
    this.password = await bcrypt.hash(this.password, 12);
  }

  next();
});

baseUserSchema.pre('save', function(next) {
  // Skip if password wasn't modified
  if (!this.isModified('password')) return next();

  // Skip setting passwordChangedAt for new users (during registration)
  // This prevents the "password recently changed" error for new users
  if (this.isNew) return next();

  // Set passwordChangedAt to current time minus 1 second
  // This ensures the token is created after the password has been changed
  this.passwordChangedAt = Date.now() - 1000;
  next();
});


baseUserSchema.methods.correctPassword = async function(
  candidatePassword,
  userPassword
) {
  // Social auth users don't have a password to compare
  if (this.socialProvider) {
    return false; // Social auth users should use their social provider to login
  }
  if (!candidatePassword || !userPassword) return false;

  return await bcrypt.compare(candidatePassword, userPassword);
};

baseUserSchema.methods.changedPasswordAfter = function(JWTTimestamp) {
  if (this.passwordChangedAt) {
    const changedTimestamp = parseInt(
      this.passwordChangedAt.getTime() / 1000,
      10
    );

    return JWTTimestamp < changedTimestamp;
  }

  // False means NOT changed
  return false;
};

baseUserSchema.methods.createPasswordResetToken = function() {
  const resetToken = crypto.randomBytes(32).toString('hex');

  this.passwordResetToken = crypto
    .createHash('sha256')
    .update(resetToken)
    .digest('hex');

  this.passwordResetExpires = Date.now() + 10 * 60 * 1000;

  return resetToken;
};

baseUserSchema.methods.resetPassword = async function(newPassword) {
  // Set new password (will be hashed by pre-save middleware)
  this.password = newPassword;
  this.markModified('password'); // <-- This is the key!

  // Clear reset token fields
  this.passwordResetToken = undefined;
  this.passwordResetExpires = undefined;
};

// Method to create email verification token
baseUserSchema.methods.createEmailVerificationToken = function() {
  const verificationToken = crypto.randomBytes(32).toString('hex');

  this.emailVerificationToken = crypto
    .createHash('sha256')
    .update(verificationToken)
    .digest('hex');

  // Token expires in 24 hours
  this.emailVerificationExpires = Date.now() + 24 * 60 * 60 * 1000;

  return verificationToken;
};

// Method to verify email
baseUserSchema.methods.verifyEmail = function() {
  this.emailVerified = true;
  this.emailVerificationToken = undefined;
  this.emailVerificationExpires = undefined;
};

const BaseUser = mongoose.model('User', baseUserSchema);

module.exports = BaseUser;


