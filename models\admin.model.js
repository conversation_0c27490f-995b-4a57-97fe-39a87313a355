const mongoose = require('mongoose');
const BaseUser = require('./baseUser.model');

const adminSchema = new mongoose.Schema({
  // Admin-specific fields
  adminLevel: {
    type: String,
    enum: ['junior', 'senior', 'super'],
    default: 'junior'
  },
  department: {
    type: String,
    enum: ['customer_support', 'content_moderation', 'finance', 'operations', 'technical', 'marketing', 'executive'],
    default: 'customer_support'
  },
  permissions: {
    manageUsers: {
      type: Boolean,
      default: false
    },
    manageCreators: {
      type: Boolean,
      default: false
    },
    manageProducts: {
      type: Boolean,
      default: false
    },
    manageBales: {
      type: Boolean,
      default: false
    },
    manageOrders: {
      type: Boolean,
      default: false
    },
    managePayouts: {
      type: Boolean,
      default: false
    },
    managePromotions: {
      type: Boolean,
      default: false
    },
    manageCategories: {
      type: Boolean,
      default: false
    },
    viewReports: {
      type: Boolean,
      default: false
    },
    manageSettings: {
      type: Boolean,
      default: false
    },
    manageAdmins: {
      type: Boolean,
      default: false
    }
  },
  employeeId: {
    type: String,
    unique: true,
    sparse: true
  },
  lastLogin: Date,
  // Future features (not implemented in MVP)
  loginHistory: [
    {
      timestamp: Date,
      ipAddress: String,
      device: String,
      browser: String
    }
  ]
});

// Set default role to 'admin'
adminSchema.pre('save', function(next) {
  if (!this.role) {
    this.role = 'admin';
  }
  next();
});

// Set permissions based on admin level
adminSchema.pre('save', function(next) {
  if (this.isModified('adminLevel')) {
    this.setPermissions();
  }
  next();
});

// Function to map permissions based on admin level
adminSchema.methods.setPermissions = function() {
  const permissions = this.permissions;

  if (this.adminLevel === 'super') {
    // Super admin has all permissions
    Object.keys(permissions).forEach(key => {
      permissions[key] = true;
    });
  } else if (this.adminLevel === 'senior') {
    // Senior admin has most permissions except managing admins and settings
    Object.keys(permissions).forEach(key => {
      permissions[key] = key !== 'manageAdmins' && key !== 'manageSettings';
    });
  } else {
    // Junior admin has limited permissions based on department
    this.setJuniorPermissions();
  }
};

// Function to set permissions for junior admins based on their department
adminSchema.methods.setJuniorPermissions = function() {
  const permissions = this.permissions;

  switch (this.department) {
    case 'customer_support':
      permissions.manageUsers = true;
      permissions.manageOrders = true;
      break;
    case 'content_moderation':
      permissions.manageProducts = true;
      permissions.manageBales = true;
      permissions.manageCategories = true;
      break;
    case 'finance':
      permissions.managePayouts = true;
      permissions.viewReports = true;
      break;
    case 'operations':
      permissions.manageOrders = true;
      permissions.managePromotions = true;
      permissions.viewReports = true;
      break;
    case 'marketing':
      permissions.managePromotions = true;
      permissions.viewReports = true;
      break;
    default:
      break;
  }
};


// Method to log login (for future implementation)
adminSchema.methods.logLogin = function(ipAddress, device, browser) {
  this.lastLogin = Date.now();

  this.loginHistory.push({
    timestamp: Date.now(),
    ipAddress,
    device,
    browser
  });

  // Limit login history to 100 entries
  if (this.loginHistory.length > 100) {
    this.loginHistory = this.loginHistory.slice(-100);
  }

  return this.save();
};

// Create the Admin model as a discriminator of BaseUser
const Admin = BaseUser.discriminator('Admin', adminSchema);

module.exports = Admin;

