# Admin Backend Architecture

This document outlines the admin-specific functionality, models, and endpoints for the Flashy API.

## Models

### Admin Model

```javascript
{
  // Inherits from BaseUser
  name: String,
  email: String,
  password: String,
  photo: String,
  role: 'admin',
  active: Boolean,

  // Admin-specific fields
  adminLevel: {
    type: String,
    enum: ['junior', 'senior', 'super'],
    default: 'junior'
  },
  department: {
    type: String,
    enum: ['customer_support', 'content_moderation', 'finance', 'operations', 'technical', 'marketing', 'executive'],
    default: 'customer_support'
  },
  permissions: {
    manageUsers: {
      type: Boolean,
      default: false
    },
    manageCreators: {
      type: Boolean,
      default: false
    },
    manageProducts: {
      type: Boolean,
      default: false
    },
    manageBales: {
      type: Boolean,
      default: false
    },
    manageOrders: {
      type: Boolean,
      default: false
    },
    managePayouts: {
      type: Boolean,
      default: false
    },
    managePromotions: {
      type: Boolean,
      default: false
    },
    manageCategories: {
      type: Boolean,
      default: false
    },
    viewReports: {
      type: Boolean,
      default: false
    },
    manageSettings: {
      type: <PERSON>olean,
      default: false
    },
    manageAdmins: {
      type: Boolean,
      default: false
    }
  },
  employeeId: {
    type: String,
    unique: true,
    sparse: true
  },
  lastLogin: Date,
  loginHistory: [
    {
      timestamp: Date,
      ipAddress: String,
      device: String,
      browser: String
    }
  ],
  notificationPreferences: {
    newCreators: {
      type: Boolean,
      default: true
    },
    creatorVerifications: {
      type: Boolean,
      default: true
    },
    highValueOrders: {
      type: Boolean,
      default: true
    },
    disputes: {
      type: Boolean,
      default: true
    },
    systemAlerts: {
      type: Boolean,
      default: true
    },
    email: {
      type: Boolean,
      default: true
    },
    push: {
      type: Boolean,
      default: true
    }
  }
}
```

### FeeConfig Model

```javascript
{
  platformFee: {
    percentage: Number
  },
  processingFee: {
    percentage: Number
  },
  isActive: Boolean,
  createdBy: {
    type: ObjectId,
    ref: 'User'
  },
  effectiveDate: Date
}
```

### ShippingFee Model

```javascript
{
  country: String,
  region: String,
  city: String,
  standardFee: Number,
  expressFee: Number,
  weightFactors: [
    {
      minWeight: Number,
      maxWeight: Number,
      multiplier: Number
    }
  ],
  isActive: Boolean,
  createdBy: {
    type: ObjectId,
    ref: 'User'
  },
  updatedBy: {
    type: ObjectId,
    ref: 'User'
  }
}
```

### AdminLog Model

```javascript
{
  admin: {
    type: ObjectId,
    ref: 'User'
  },
  action: String,
  details: {
    type: Object
  },
  resourceType: String, // 'product', 'bale', 'creator', 'buyer', 'order', etc.
  resourceId: ObjectId,
  ipAddress: String,
  userAgent: String,
  createdAt: Date
}
```

## Endpoints

### Dashboard Management

| Endpoint | Method | Description | Query Parameters/Request Body | Response |
|----------|--------|-------------|--------------------------|----------|
| `/api/v1/admin/dashboard` | GET | Get admin dashboard | `period=['7d','30d','90d','all']` | Complete dashboard with all key metrics |
| `/api/v1/admin/dashboard/users` | GET | Get user statistics | `period=['7d','30d','90d','all']` | User statistics and growth |
| `/api/v1/admin/dashboard/products` | GET | Get product statistics | `period=['7d','30d','90d','all']` | Product statistics and trends |
| `/api/v1/admin/dashboard/orders` | GET | Get order statistics | `period=['7d','30d','90d','all']` | Order statistics and trends |
| `/api/v1/admin/dashboard/revenue` | GET | Get revenue statistics | `period=['7d','30d','90d','all']` | Revenue statistics and charts |
| `/api/v1/admin/dashboard/creators` | GET | Get creator statistics | `period=['7d','30d','90d','all']` | Creator statistics and performance |
| `/api/v1/admin/dashboard/top-performers` | GET | Get top performers | `type=['creators','products','bales'], limit` | Top performing items |
| `/api/v1/admin/dashboard/alerts` | GET | Get system alerts | `priority=['high','medium','low']` | System alerts requiring attention |
| `/api/v1/admin/dashboard/activity-log` | GET | Get admin activity log | `page, limit, adminId, action` | Admin activity history |

### Admin Management

| Endpoint | Method | Description | Query Parameters | Response |
|----------|--------|-------------|------------------|----------|
| `/api/v1/admin/admins` | GET | Get all admins | `page, limit, sort, search, adminLevel` | Admins array |
| `/api/v1/admin/admins/:id` | GET | Get admin details | - | Admin details |
| `/api/v1/admin/admins/:id` | PATCH | Update admin details | Admin fields | Updated admin |
| `/api/v1/admin/admins/:id/permissions` | PATCH | Update admin permissions | Permission fields | Updated admin |
| `/api/v1/admin/admins/:id/notification-preferences` | GET | Get admin notification preferences | - | Notification preferences |
| `/api/v1/admin/admins/:id/notification-preferences` | PATCH | Update admin notification preferences | Preference fields | Updated admin |
| `/api/v1/admin/admins/:id` | DELETE | Delete admin | - | Success message |
| `/api/v1/admin/profile` | GET | Get own admin profile | - | Admin details |
| `/api/v1/admin/profile` | PATCH | Update own admin profile | Admin fields | Updated admin |
| `/api/v1/admin/profile/notification-preferences` | GET | Get own notification preferences | - | Notification preferences |
| `/api/v1/admin/profile/notification-preferences` | PATCH | Update own notification preferences | Preference fields | Updated admin |
| `/api/v1/admin/logs` | GET | Get admin activity logs | `page, limit, sort, adminId, action, resourceType` | Logs array |
| `/api/v1/admin/logs/:id` | DELETE | Delete admin log | - | Success message |

### Creator Management

> **Note:** Admin users can manage all aspects of creators, including verification, profile details, products/bales, and financial information. The system enforces proper validation and permission checks for all operations.


#### Creator Listing and Statistics

| Endpoint | Method | Description | Query Parameters/Request Body | Response |
|----------|--------|-------------|--------------------------|----------|
| `/api/v1/admin/creators` | GET | Get all creators | `page, limit, sort=['newest','oldest','name_asc','name_desc'], search, status=['active','inactive'], verificationStatus=['unverified','pending','verified','rejected'], onboardingStatus=['pending','completed']` | Creators array with pagination |
| `/api/v1/admin/creators/verification-stats` | GET | Get verification statistics | - | Stats with counts by verification status |
| `/api/v1/admin/creators/onboarding-stats` | GET | Get onboarding statistics | - | Stats with completion rates for each step |
| `/api/v1/admin/creators/:id` | GET | Get creator details | - | Creator details with profile information |
| `/api/v1/admin/creators/:id` | DELETE | Delete creator | - | Success message (only if no associated data) |

*done
#### Creator Profile Management

> **Note:** Admin users can update creator profile information, reset passwords, and manage metrics. The profile includes basic information, business details, shop information, and payment preferences. Changes are logged for audit purposes.

| Endpoint | Method | Description | Query Parameters/Request Body | Response |
|----------|--------|-------------|--------------------------|----------|
| `/api/v1/admin/creators/:id/details` | PATCH | Update creator details | `{ name, email, phone, gender, dateOfBirth, active }` | Updated creator |
| `/api/v1/admin/creators/:id/reset-password` | POST | Reset creator password | `{ sendEmail=true/false }` | Success message with temporary password |
| `/api/v1/admin/creators/:id/metrics` | PATCH | Update creator metrics | `{ averageRating, qualityScore, shippingSpeed }` | Updated creator with new metrics |
| `/api/v1/admin/creators/:id/metrics` | GET | Get creator metrics | - | Creator performance metrics |
| `/api/v1/admin/creators/:id/commission` | PATCH | Update commission rate | `{ commissionRate }` | Updated creator with new commission rate |
| `/api/v1/admin/creators/:id/commission` | GET | Get commission rate | - | Creator commission rate |
| `/api/v1/admin/creators/:id/shop-info` | GET | Get shop information | - | Shop details including name, description, logo |
| `/api/v1/admin/creators/:id/shop-info` | PATCH | Update shop information | `{ shopName, description, logo, contactEmail, contactPhone }` | Updated shop information |
| `/api/v1/admin/creators/:id/payment-info` | GET | Get payment information | - | Payment details including bank account, mobile money |
| `/api/v1/admin/creators/:id/payment-info` | PATCH | Update payment information | `{ bankName, accountNumber, accountName, mobileMoneyProvider, mobileMoneyNumber }` | Updated payment information |
| `/api/v1/admin/creators/:id/shipping-info` | GET | Get shipping information | - | Shipping details including rates, locations, methods |
| `/api/v1/admin/creators/:id/shipping-info` | PATCH | Update shipping information | `{ defaultShippingRate, expressShippingMultiplier, shippingLocations, shippingMethods }` | Updated shipping information |
| `/api/v1/admin/creators/:id/onboarding-progress` | GET | Get onboarding progress | - | Progress status for each onboarding step |
| `/api/v1/admin/creators/:id/notification-preferences` | GET | Get notification preferences | - | Preferences data |
| `/api/v1/admin/creators/:id/notification-preferences` | PATCH | Update notification preferences | Preference fields | Updated creator |
| `/api/v1/admin/creators/:id/seller-profile` | GET | Get seller profile | - | Seller profile data |
| `/api/v1/admin/creators/:id/seller-profile` | PATCH | Update seller profile | Seller profile fields | Updated creator |
| `/api/v1/admin/creators/:id/payout-preferences` | GET | Get payout preferences | - | Payout preferences data |
| `/api/v1/admin/creators/:id/payout-preferences` | PATCH | Update payout preferences | Payout preference fields | Updated creator |
| `/api/v1/admin/creators/:id/dashboard` | GET | Get creator dashboard statistics | - | Dashboard data |

*done
#### Creator Verification Management

> **Note:** Admin users are responsible for verifying creators through a two-part process: 1) Verifying the creator's identity and business documents, and 2) Updating the creator's overall verification status. Document verification involves reviewing business registration, ID documents, and address proof, while creator verification is the overall approval status that determines if a creator can sell on the platform.

| Endpoint | Method | Description | Query Parameters/Request Body | Response |
|----------|--------|-------------|--------------------------|----------|
| `/api/v1/admin/creators/:id/verification` | GET | Get creator verification status | - | Current verification status with history |
| `/api/v1/admin/creators/:id/verification` | PATCH | Update creator verification status | `{ status=['unverified','pending','verified','rejected'], notes }` | Updated creator with new verification status |
| `/api/v1/admin/creators/:id/verification-documents` | GET | Get submitted verification documents | - | Array of documents with status |
| `/api/v1/admin/creators/:id/verify-creator` | PATCH | Verify creator information | `{ isVerified=true/false, verificationNotes }` | Updated verification status |

*done
#### Creator Content Management

> **Note:** Admin users can view and manage all content created by creators, including products, bales, and reviews. This section provides endpoints for accessing and moderating creator content.

| Endpoint | Method | Description | Query Parameters/Request Body | Response |
|----------|--------|-------------|--------------------------|----------|
| `/api/v1/admin/creators/:id/products` | GET | Get creator products | `page, limit, status=['draft','pending','active','inactive','rejected'], sort=['newest','oldest','price_low','price_high'], search` | Products array with pagination |
| `/api/v1/admin/creators/:id/bales` | GET | Get creator bales | `page, limit, status=['draft','pending','active','inactive','rejected'], sort=['newest','oldest','price_low','price_high'], search` | Bales array with pagination |
| `/api/v1/admin/creators/:id/reviews` | GET | Get creator reviews | `page, limit, sort=['newest','oldest','highest','lowest'], rating, verified, hidden` | Reviews array with pagination |

#### Creator Financial Management

> **Note:** Admin users can view and manage creator financial information, including orders, earnings, and payouts. This section provides endpoints for monitoring and managing creator finances.

| Endpoint | Method | Description | Query Parameters/Request Body | Response |
|----------|--------|-------------|--------------------------|----------|
| `/api/v1/admin/creators/:id/orders` | GET | Get creator orders | `page, limit, status=['pending','processing','shipped','delivered','cancelled','refunded'], sort=['newest','oldest']` | Orders array with pagination |
| `/api/v1/admin/creators/:id/earnings` | GET | Get creator earnings | `period=['7d','30d','90d','all']` | Earnings data with chart information |

#### Creator Statistics

> **Note:** These endpoints provide aggregated statistics about creators across the platform, helping admins monitor overall platform health and creator performance.

| Endpoint | Method | Description | Query Parameters/Request Body | Response |
|----------|--------|-------------|--------------------------|----------|
| `/api/v1/admin/creators/verification-stats` | GET | Get verification stats | - | Stats with counts by verification status |
| `/api/v1/admin/creators/onboarding-stats` | GET | Get onboarding stats | - | Stats with completion rates for each step |

### Buyer Management

| Endpoint | Method | Description | Query Parameters | Response |
|----------|--------|-------------|------------------|----------|
| `/api/v1/admin/buyers` | GET | Get all buyers | `page, limit, sort, search` | Buyers array |
| `/api/v1/admin/buyers/:id` | GET | Get buyer details | - | Buyer details |
| `/api/v1/admin/buyers/:id/details` | PATCH | Update buyer details | Buyer fields | Updated buyer |
| `/api/v1/admin/buyers/:id/status` | PATCH | Update buyer status | `{ active }` | Updated buyer |
| `/api/v1/admin/buyers/:id/preferences` | PATCH | Update buyer preferences | Preference fields | Updated buyer |
| `/api/v1/admin/buyers/:id/reset-password` | POST | Reset buyer password | `{ sendEmail }` | Success message |
| `/api/v1/admin/buyers/:id` | DELETE | Delete buyer | - | Success message |

### Product Management

> **Note:** The main products endpoint supports comprehensive filtering options that eliminate the need for separate endpoints for different product statuses, featured products, or products with sales/promotions. Use the appropriate query parameters to filter products as needed.

| Endpoint | Method | Description | Query Parameters | Response |
|----------|--------|-------------|------------------|----------|
| `/api/v1/admin/products` | GET | Get all products | `page, limit, sort, status=['draft','pending','active','inactive','rejected'], category, creator, featured=true/false, onSale=true/false, hasPromotion=true/false, search, gender, material, style, fit, minPrice, maxPrice` | Products array |
| `/api/v1/admin/products/new-arrivals` | GET | Get recently added products | `page, limit, sort, days` | Products array |
| `/api/v1/admin/products/by-category/:categoryId` | GET | Get products by category | `page, limit, sort` | Products array |
| `/api/v1/admin/products/:id` | GET | Get product details | `populate` | Product details |
| `/api/v1/admin/products/:id` | PATCH | Update product | Product fields | Updated product |
| `/api/v1/admin/products/:id/basic-info` | PATCH | Update basic product info | `{ name, brand, description, highlights, gender, basePrice, category, relatedCategories, tags }` | Updated product |
| `/api/v1/admin/products/:id/specifications` | PATCH | Update product specifications | `{ mainMaterial, dressStyle, pantType, skirtType, mensPantSize, fitType, pattern, closure, neckline, sleeveLength, waistline, hemline }` | Updated product |
| `/api/v1/admin/products/:id/seo` | PATCH | Update product SEO | `{ metaTitle, metaDescription, keywords }` | Updated product |
| `/api/v1/admin/products/:id` | DELETE | Delete product | - | Success message |
| `/api/v1/admin/products/:id/status` | PATCH | Update product status | `{ status, rejectionReason, adminNotes }` | Updated product |
| `/api/v1/admin/products/:id/featured` | PATCH | Toggle featured status | `{ featured }` | Updated product |
| `/api/v1/admin/products/:id/variations/:variationId/sale` | PATCH | Set variation sale | `{ salePrice, saleStartDate, saleEndDate }` | Updated variation |
| `/api/v1/admin/products/:id/variations` | GET | Get product variations | - | Variations array |
| `/api/v1/admin/products/:id/variations/:variationId` | PATCH | Update variation | Variation fields | Updated variation |
| `/api/v1/admin/products/:id/images` | GET | Get product images | - | Images array |
| `/api/v1/admin/products/:id/images` | POST | Add product images | Form data with images | Updated product |
| `/api/v1/admin/products/:id/images/:imageId` | DELETE | Delete product image | - | Updated product |
| `/api/v1/admin/products/:id/reviews` | GET | Get product reviews | `page, limit, sort, rating` | Reviews array |
| `/api/v1/admin/products/:id/reviews/:reviewId` | PATCH | Update review visibility | `{ hidden }` | Updated review |
| `/api/v1/admin/products/:id/promotions` | GET | Get product promotions | - | Promotions array |
| `/api/v1/admin/products/:id/promotions/:promotionId` | PATCH | Update promotion | Promotion fields | Updated promotion |
| `/api/v1/admin/products/:id/inventory` | GET | Get inventory details | - | Inventory data |
| `/api/v1/admin/products/:id/inventory/restock` | POST | Restock product | `{ variationId, quantity, notes }` | Updated inventory |
| `/api/v1/admin/products/:id/inventory/adjust` | POST | Adjust inventory | `{ variationId, quantity, reason }` | Updated inventory |
| `/api/v1/admin/products/:id/sales-history` | GET | Get sales history | `period` | Sales history |
| `/api/v1/admin/products/batch-status` | PATCH | Update multiple products | `{ productIds, status }` | Success message |
| `/api/v1/admin/products/batch-feature` | PATCH | Feature multiple products | `{ productIds, featured }` | Success message |
| `/api/v1/admin/products/batch-promotion` | POST | Add multiple products to promotion | `{ productIds, promotionId, discountValue, discountType, promoStock, startDate, endDate }` | Success message |
| `/api/v1/admin/products/stats` | GET | Get product statistics | `period` | Product stats |
| `/api/v1/admin/products/sales-stats` | GET | Get product sales statistics | `period` | Sales stats |
| `/api/v1/admin/products/category-stats` | GET | Get product category statistics | - | Category stats |
| `/api/v1/admin/products/low-stock` | GET | Get low stock products | `page, limit, threshold` | Products array |
| `/api/v1/admin/products/out-of-stock` | GET | Get out of stock products | `page, limit` | Products array |

### Bale Management

| Endpoint | Method | Description | Query Parameters | Response |
|----------|--------|-------------|------------------|----------|
| `/api/v1/admin/bales` | GET | Get all bales | `page, limit, sort, search, status` | Bales array |
| `/api/v1/admin/bales/:id` | GET | Get bale details | - | Bale details |
| `/api/v1/admin/bales/:id` | PATCH | Update bale | Bale fields | Updated bale |
| `/api/v1/admin/bales/:id` | DELETE | Delete bale | - | Success message |
| `/api/v1/admin/bales/:id/status` | PATCH | Update bale status | `{ status, rejectionReason }` | Updated bale |
| `/api/v1/admin/bales/:id/reviews` | GET | Get bale reviews | `page, limit` | Reviews array |
| `/api/v1/admin/bales/stats` | GET | Get bale statistics | - | Bale stats |

### Order Management

| Endpoint | Method | Description | Query Parameters | Response |
|----------|--------|-------------|------------------|----------|
| `/api/v1/admin/orders` | GET | Get all orders | `page, limit, sort, search, status` | Orders array |
| `/api/v1/admin/orders/:id` | GET | Get order details | - | Order details |
| `/api/v1/admin/orders/:id/status` | PATCH | Update order status | `{ status, note }` | Updated order |
| `/api/v1/admin/orders/:id/tracking` | PATCH | Add tracking information | `{ trackingNumber, carrier, estimatedDelivery }` | Updated order |
| `/api/v1/admin/orders/:id/notes` | PATCH | Add order notes | `{ notes }` | Updated order |
| `/api/v1/admin/orders/stats` | GET | Get order statistics | - | Order stats |

### Review Management Endpoints

| Endpoint | Method | Description | Query Parameters/Request Body | Response |
|----------|--------|-------------|--------------------------|----------|
| `/api/v1/admin/reviews` | GET | Get all reviews | `page, limit, sort, search, product, bale, creator, rating, verified, hidden` | Reviews array |
| `/api/v1/admin/reviews/:id` | GET | Get review details | - | Review details |
| `/api/v1/admin/reviews/:id/toggle-hidden` | PATCH | Toggle review visibility | `{ hidden, reason }` | Updated review |
| `/api/v1/admin/reviews/:id/verify` | PATCH | Verify a review | `{ verified }` | Updated review |
| `/api/v1/admin/reviews/:id/delete` | DELETE | Delete a review | `{ reason }` | Success message |
| `/api/v1/admin/reviews/stats` | GET | Get review statistics | `period` | Review stats |
| `/api/v1/admin/reviews/reported` | GET | Get reported reviews | `page, limit, sort` | Reviews array |
| `/api/v1/admin/reviews/low-rating` | GET | Get low rating reviews | `rating, page, limit` | Reviews array |
| `/api/v1/admin/reviews/high-rating` | GET | Get high rating reviews | `rating, page, limit` | Reviews array |
| `/api/v1/admin/reviews/recent` | GET | Get recent reviews | `days, page, limit` | Reviews array |

### Promotion Management Endpoints

| Endpoint | Method | Description | Query Parameters/Request Body | Response |
|----------|--------|-------------|--------------------------|----------|
| `/api/v1/admin/promotions` | GET | Get all promotions | `page, limit, sort, active, category` | Promotions array |
| `/api/v1/admin/promotions` | POST | Create a promotion | `{ name, code, type, startDate, endDate, description, criteria, banners }` | Created promotion |
| `/api/v1/admin/promotions/:id` | GET | Get promotion details | - | Promotion details |
| `/api/v1/admin/promotions/:id` | PATCH | Update promotion | `{ name, code, type, startDate, endDate, description, criteria, banners, isActive }` | Updated promotion |
| `/api/v1/admin/promotions/:id` | DELETE | Delete promotion | - | Success message |
| `/api/v1/admin/promotions/:id/participants` | GET | Get promotion participants | `page, limit, status` | Participants array |
| `/api/v1/admin/promotions/:id/participants/:creatorId` | PATCH | Update participant status | `{ status, notes }` | Updated promotion |
| `/api/v1/admin/promotions/:id/participants/:creatorId/products` | GET | Get participant products | `page, limit` | Products array |
| `/api/v1/admin/promotions/:id/participants/:creatorId/products/:productId` | PATCH | Update product in promotion | `{ discountValue, discountType, promoStock }` | Updated promotion |
| `/api/v1/admin/promotions/:id/participants/:creatorId/products/:productId` | DELETE | Remove product from promotion | - | Updated promotion |
| `/api/v1/admin/promotions/active` | GET | Get active promotions | `page, limit, sort` | Promotions array |
| `/api/v1/admin/promotions/upcoming` | GET | Get upcoming promotions | `page, limit, sort` | Promotions array |
| `/api/v1/admin/promotions/expired` | GET | Get expired promotions | `page, limit, sort` | Promotions array |
| `/api/v1/admin/promotions/stats` | GET | Get promotion statistics | `period` | Promotion stats |

### Order Management

> **Note:** Admins have full access to all orders and can manage the entire order lifecycle, including updating status, tracking information, and processing exchanges.

| Endpoint | Method | Description | Query Parameters/Request Body | Response |
|----------|--------|-------------|--------------------------|----------|
| `/api/v1/admin/orders` | GET | Get all orders | `page, limit, status, startDate, endDate, isPaid, isDelivered, creator, buyer, search, sort` | Orders array |
| `/api/v1/admin/orders/:id` | GET | Get order details | `populate` | Order details |
| `/api/v1/admin/orders/:id/status` | PATCH | Update order status | `{ status, note }` | Updated order |
| `/api/v1/admin/orders/:id/tracking` | PATCH | Update order tracking | `{ trackingNumber, carrier, estimatedDelivery }` | Updated order |
| `/api/v1/admin/orders/:id/mark-paid` | PATCH | Mark order as paid | `{ paymentResult }` | Updated order |
| `/api/v1/admin/orders/:id/items/:itemId/status` | PATCH | Update item status | `{ status, note }` | Updated item |
| `/api/v1/admin/orders/:id/items/:itemId/tracking` | PATCH | Update item tracking | `{ trackingNumber, carrier, estimatedDelivery }` | Updated item |
| `/api/v1/admin/orders/:id/exchange` | POST | Process order exchange | `{ oldItemId, newItemId, newVariationId, reason }` | Updated order |
| `/api/v1/admin/orders/stats` | GET | Get order statistics | `period` | Order stats |
| `/api/v1/admin/orders/revenue` | GET | Get order revenue | `period` | Revenue data |
| `/api/v1/admin/orders/by-creator/:creatorId` | GET | Get creator's orders | `page, limit, status` | Orders array |
| `/api/v1/admin/orders/by-buyer/:buyerId` | GET | Get buyer's orders | `page, limit, status` | Orders array |

### Notification Management

| Endpoint | Method | Description | Query Parameters/Request Body | Response |
|----------|--------|-------------|--------------------------|----------|
| `/api/v1/admin/notifications` | GET | Get admin notifications | `page, limit, read, type, priority, populate` | Notifications array |
| `/api/v1/admin/notifications` | POST | Create notification | `{ recipient, type, title, message, data, priority, deliveryChannels }` | Created notification |
| `/api/v1/admin/notifications/unread-count` | GET | Get unread count | - | Unread count |
| `/api/v1/admin/notifications/:id/read` | PATCH | Mark as read | - | Updated notification |
| `/api/v1/admin/notifications/mark-all-read` | PATCH | Mark all as read | - | Count of updated notifications |
| `/api/v1/admin/notifications/:id` | DELETE | Delete notification | - | Success message |
| `/api/v1/admin/notifications/preferences` | GET | Get notification preferences | - | Preferences object |
| `/api/v1/admin/notifications/preferences` | PATCH | Update preferences | `{ channels, types, quietHours }` | Updated preferences |
| `/api/v1/admin/notifications/bulk` | POST | Send bulk notifications | `{ recipients, type, title, message, data, priority }` | Success message |
| `/api/v1/admin/notifications/broadcast` | POST | Broadcast to all users | `{ userType, type, title, message, data, priority }` | Success message |
| `/api/v1/admin/notifications/templates` | GET | Get notification templates | `page, limit` | Templates array |
| `/api/v1/admin/notifications/templates` | POST | Create notification template | `{ name, type, title, message, data }` | Created template |
| `/api/v1/admin/notifications/templates/:id` | PATCH | Update notification template | Template fields | Updated template |
| `/api/v1/admin/notifications/templates/:id` | DELETE | Delete notification template | - | Success message |
| `/api/v1/admin/notifications/templates/:id/send` | POST | Send from template | `{ recipients, data }` | Success message |

### Fee Management

| Endpoint | Method | Description | Request Body | Response |
|----------|--------|-------------|--------------|----------|
| `/api/v1/admin/fees/config/current` | GET | Get current fee config | - | Fee config |
| `/api/v1/admin/fees/config` | GET | Get all fee configs | `page, limit` | Fee configs array |
| `/api/v1/admin/fees/config` | POST | Create fee config | Fee config data | Created fee config |
| `/api/v1/admin/fees/shipping` | GET | Get shipping fees | `country, region, city` | Shipping fees |
| `/api/v1/admin/fees/shipping` | POST | Create shipping fee | Shipping fee data | Created shipping fee |
| `/api/v1/admin/fees/shipping/:id` | PATCH | Update shipping fee | Shipping fee fields | Updated shipping fee |
| `/api/v1/admin/fees/shipping/:id` | DELETE | Delete shipping fee | - | Success message |

### Exchange Management

| Endpoint | Method | Description | Query Parameters | Response |
|----------|--------|-------------|------------------|----------|
| `/api/v1/admin/exchange/products` | GET | Get products for exchange | `search, creator` | Products array |
| `/api/v1/admin/exchange/products/:productId/variations` | GET | Get product variations | - | Variations array |
| `/api/v1/admin/exchange/bales` | GET | Get bales for exchange | `search, creator` | Bales array |
| `/api/v1/admin/exchange/bales/:baleId/variations` | GET | Get bale variations | - | Variations array |

### Reports

| Endpoint | Method | Description | Query Parameters | Response |
|----------|--------|-------------|------------------|----------|
| `/api/v1/admin/reports/revenue` | GET | Get platform revenue | `period` | Revenue data |
| `/api/v1/admin/reports/revenue/breakdown` | GET | Get revenue breakdown | `period` | Revenue breakdown |
| `/api/v1/admin/reports/creators/earnings` | GET | Get creator earnings | `period, top` | Earnings data |
| `/api/v1/admin/reports/shipping` | GET | Get shipping statistics | `period` | Shipping stats |
