{"info": {"name": "Wishlist API Tests", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Get Wishlist", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{AUTH_TOKEN}}", "type": "text"}], "url": {"raw": "{{BASE_URL}}/api/v1/wishlist", "host": ["{{BASE_URL}}"], "path": ["api", "v1", "wishlist"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function() {", "  pm.response.to.have.status(200);", "});", "", "pm.test('Response has correct structure', function() {", "  const jsonData = pm.response.json();", "  pm.expect(jsonData.status).to.equal('success');", "  pm.expect(jsonData.data).to.have.property('wishlist');", "  pm.expect(jsonData.data.wishlist).to.have.property('user');", "  pm.expect(jsonData.data.wishlist).to.have.property('products');", "  pm.expect(jsonData.data.wishlist).to.have.property('bales');", "});"], "type": "text/javascript"}}]}, {"name": "Add Product to Wishlist", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{AUTH_TOKEN}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"productId\": \"{{PRODUCT_ID}}\",\n  \"variationId\": \"{{VARIATION_ID}}\",\n  \"quantity\": 1\n}"}, "url": {"raw": "{{BASE_URL}}/api/v1/wishlist/products", "host": ["{{BASE_URL}}"], "path": ["api", "v1", "wishlist", "products"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function() {", "  pm.response.to.have.status(200);", "});", "", "pm.test('Response has correct structure', function() {", "  const jsonData = pm.response.json();", "  pm.expect(jsonData.status).to.equal('success');", "  pm.expect(jsonData.message).to.equal('Product added to wishlist');", "  pm.expect(jsonData.data).to.have.property('wishlist');", "});", "", "pm.test('Product was added to wishlist', function() {", "  const jsonData = pm.response.json();", "  const productId = pm.variables.get('PRODUCT_ID');", "  const variationId = pm.variables.get('VARIATION_ID');", "  ", "  const productExists = jsonData.data.wishlist.products.some(item => {", "    return item.product._id === productId && item.variationId === variationId;", "  });", "  ", "  pm.expect(productExists).to.be.true;", "});"], "type": "text/javascript"}}]}, {"name": "<PERSON>d <PERSON> to Wishlist", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{AUTH_TOKEN}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"baleId\": \"{{BALE_ID}}\",\n  \"variationId\": \"{{VARIATION_ID}}\",\n  \"quantity\": 1\n}"}, "url": {"raw": "{{BASE_URL}}/api/v1/wishlist/bales", "host": ["{{BASE_URL}}"], "path": ["api", "v1", "wishlist", "bales"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function() {", "  pm.response.to.have.status(200);", "});", "", "pm.test('Response has correct structure', function() {", "  const jsonData = pm.response.json();", "  pm.expect(jsonData.status).to.equal('success');", "  pm.expect(jsonData.message).to.equal('<PERSON><PERSON> added to wishlist');", "  pm.expect(jsonData.data).to.have.property('wishlist');", "});", "", "pm.test('<PERSON><PERSON> was added to wishlist', function() {", "  const jsonData = pm.response.json();", "  const baleId = pm.variables.get('BALE_ID');", "  const variationId = pm.variables.get('VARIATION_ID');", "  ", "  const baleExists = jsonData.data.wishlist.bales.some(item => {", "    return item.bale._id === baleId && item.variationId === variationId;", "  });", "  ", "  pm.expect(baleExists).to.be.true;", "});"], "type": "text/javascript"}}]}, {"name": "Remove Product from Wishlist", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{AUTH_TOKEN}}", "type": "text"}], "url": {"raw": "{{BASE_URL}}/api/v1/wishlist/products/{{PRODUCT_ID}}?variationId={{VARIATION_ID}}", "host": ["{{BASE_URL}}"], "path": ["api", "v1", "wishlist", "products", "{{PRODUCT_ID}}"], "query": [{"key": "variationId", "value": "{{VARIATION_ID}}"}]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function() {", "  pm.response.to.have.status(200);", "});", "", "pm.test('Response has correct structure', function() {", "  const jsonData = pm.response.json();", "  pm.expect(jsonData.status).to.equal('success');", "  pm.expect(jsonData.message).to.equal('Product removed from wishlist');", "  pm.expect(jsonData.data).to.have.property('wishlist');", "});", "", "pm.test('Product was removed from wishlist', function() {", "  const jsonData = pm.response.json();", "  const productId = pm.variables.get('PRODUCT_ID');", "  const variationId = pm.variables.get('VARIATION_ID');", "  ", "  const productExists = jsonData.data.wishlist.products.some(item => {", "    return item.product._id === productId && item.variationId === variationId;", "  });", "  ", "  pm.expect(productExists).to.be.false;", "});"], "type": "text/javascript"}}]}, {"name": "<PERSON><PERSON><PERSON> from Wishlist", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{AUTH_TOKEN}}", "type": "text"}], "url": {"raw": "{{BASE_URL}}/api/v1/wishlist/bales/{{BALE_ID}}?variationId={{VARIATION_ID}}", "host": ["{{BASE_URL}}"], "path": ["api", "v1", "wishlist", "bales", "{{BALE_ID}}"], "query": [{"key": "variationId", "value": "{{VARIATION_ID}}"}]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function() {", "  pm.response.to.have.status(200);", "});", "", "pm.test('Response has correct structure', function() {", "  const jsonData = pm.response.json();", "  pm.expect(jsonData.status).to.equal('success');", "  pm.expect(jsonData.message).to.equal('<PERSON><PERSON> removed from wishlist');", "  pm.expect(jsonData.data).to.have.property('wishlist');", "});", "", "pm.test('<PERSON><PERSON> was removed from wishlist', function() {", "  const jsonData = pm.response.json();", "  const baleId = pm.variables.get('BALE_ID');", "  const variationId = pm.variables.get('VARIATION_ID');", "  ", "  const baleExists = jsonData.data.wishlist.bales.some(item => {", "    return item.bale._id === baleId && item.variationId === variationId;", "  });", "  ", "  pm.expect(baleExists).to.be.false;", "});"], "type": "text/javascript"}}]}, {"name": "Update Product Quantity", "request": {"method": "PATCH", "header": [{"key": "Authorization", "value": "Bearer {{AUTH_TOKEN}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"variationId\": \"{{VARIATION_ID}}\",\n  \"quantity\": 3\n}"}, "url": {"raw": "{{BASE_URL}}/api/v1/wishlist/products/{{PRODUCT_ID}}", "host": ["{{BASE_URL}}"], "path": ["api", "v1", "wishlist", "products", "{{PRODUCT_ID}}"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function() {", "  pm.response.to.have.status(200);", "});", "", "pm.test('Response has correct structure', function() {", "  const jsonData = pm.response.json();", "  pm.expect(jsonData.status).to.equal('success');", "  pm.expect(jsonData.message).to.equal('Product quantity updated');", "  pm.expect(jsonData.data).to.have.property('wishlist');", "});", "", "pm.test('Product quantity was updated', function() {", "  const jsonData = pm.response.json();", "  const productId = pm.variables.get('PRODUCT_ID');", "  const variationId = pm.variables.get('VARIATION_ID');", "  ", "  const product = jsonData.data.wishlist.products.find(item => {", "    return item.product._id === productId && item.variationId === variationId;", "  });", "  ", "  pm.expect(product).to.not.be.undefined;", "  pm.expect(product.quantity).to.equal(3);", "});"], "type": "text/javascript"}}]}, {"name": "Update Bale Quantity", "request": {"method": "PATCH", "header": [{"key": "Authorization", "value": "Bearer {{AUTH_TOKEN}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"variationId\": \"{{VARIATION_ID}}\",\n  \"quantity\": 2\n}"}, "url": {"raw": "{{BASE_URL}}/api/v1/wishlist/bales/{{BALE_ID}}", "host": ["{{BASE_URL}}"], "path": ["api", "v1", "wishlist", "bales", "{{BALE_ID}}"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function() {", "  pm.response.to.have.status(200);", "});", "", "pm.test('Response has correct structure', function() {", "  const jsonData = pm.response.json();", "  pm.expect(jsonData.status).to.equal('success');", "  pm.expect(jsonData.message).to.equal('Bale quantity updated');", "  pm.expect(jsonData.data).to.have.property('wishlist');", "});", "", "pm.test('Bale quantity was updated', function() {", "  const jsonData = pm.response.json();", "  const baleId = pm.variables.get('BALE_ID');", "  const variationId = pm.variables.get('VARIATION_ID');", "  ", "  const bale = jsonData.data.wishlist.bales.find(item => {", "    return item.bale._id === baleId && item.variationId === variationId;", "  });", "  ", "  pm.expect(bale).to.not.be.undefined;", "  pm.expect(bale.quantity).to.equal(2);", "});"], "type": "text/javascript"}}]}, {"name": "Move to Cart", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{AUTH_TOKEN}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"id\": \"{{PRODUCT_ID}}\",\n  \"type\": \"product\",\n  \"variationId\": \"{{VARIATION_ID}}\"\n}"}, "url": {"raw": "{{BASE_URL}}/api/v1/wishlist/move-to-cart", "host": ["{{BASE_URL}}"], "path": ["api", "v1", "wishlist", "move-to-cart"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function() {", "  pm.response.to.have.status(200);", "});", "", "pm.test('Response has correct structure', function() {", "  const jsonData = pm.response.json();", "  pm.expect(jsonData.status).to.equal('success');", "  pm.expect(jsonData.message).to.equal('Item moved to cart');", "  pm.expect(jsonData.data).to.have.property('wishlist');", "  pm.expect(jsonData.data).to.have.property('cart');", "});", "", "pm.test('Item was removed from wishlist', function() {", "  const jsonData = pm.response.json();", "  const productId = pm.variables.get('PRODUCT_ID');", "  const variationId = pm.variables.get('VARIATION_ID');", "  ", "  const productExists = jsonData.data.wishlist.products.some(item => {", "    return item.product._id === productId && item.variationId === variationId;", "  });", "  ", "  pm.expect(productExists).to.be.false;", "});", "", "pm.test('Item was added to cart', function() {", "  const jsonData = pm.response.json();", "  const productId = pm.variables.get('PRODUCT_ID');", "  const variationId = pm.variables.get('VARIATION_ID');", "  ", "  const itemExists = jsonData.data.cart.items.some(item => {", "    return item.product === productId && item.variationId === variationId;", "  });", "  ", "  pm.expect(itemExists).to.be.true;", "});"], "type": "text/javascript"}}]}, {"name": "Clear Wishlist", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{AUTH_TOKEN}}", "type": "text"}], "url": {"raw": "{{BASE_URL}}/api/v1/wishlist", "host": ["{{BASE_URL}}"], "path": ["api", "v1", "wishlist"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function() {", "  pm.response.to.have.status(200);", "});", "", "pm.test('Response has correct structure', function() {", "  const jsonData = pm.response.json();", "  pm.expect(jsonData.status).to.equal('success');", "  pm.expect(jsonData.message).to.equal('Wishlist cleared');", "  pm.expect(jsonData.data).to.have.property('wishlist');", "});", "", "pm.test('Wishlist was cleared', function() {", "  const jsonData = pm.response.json();", "  pm.expect(jsonData.data.wishlist.products).to.be.an('array').that.is.empty;", "  pm.expect(jsonData.data.wishlist.bales).to.be.an('array').that.is.empty;", "});"], "type": "text/javascript"}}]}]}