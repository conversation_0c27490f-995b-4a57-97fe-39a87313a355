const mongoose = require('mongoose');
const slugify = require('slugify');

// Define the variation schema
const variationSchema = new mongoose.Schema(
  {
    // For product variations
    color: {
      type: String,
      required: function() {
        return this.parent().type === 'product';
      }
    },
    size: {
      type: String,
      required: function() {
        return this.parent().type === 'product';
      }
    },
    // For bale variations
    identifier: {
      type: String,
      required: function() {
        return this.parent().type === 'bale';
      }
    },
    quantity: {
      type: Number,
      required: [true, 'A variation must have a quantity'],
      min: [0, 'Quantity cannot be negative'],
      default: 0
    },
    quantityHistory: {
      type: [{
        quantity: {
          type: Number,
          required: true
        },
        changeAmount: {
          type: Number,
          required: true
        },
        changeType: {
          type: String,
          enum: ['initial', 'purchase', 'restock', 'adjustment', 'return'],
          required: true
        },
        reason: String,
        timestamp: {
          type: Date,
          default: Date.now
        },
        orderId: {
          type: mongoose.Schema.ObjectId,
          ref: 'Order'
        },
        updatedBy: {
          type: mongoose.Schema.ObjectId,
          ref: 'User'
        }
      }],
      default: []
    },
    // Track key inventory metrics separately for quick access
    inventoryMetrics: {
      lastRestocked: {
        date: Date,
        amount: Number,
        by: {
          type: mongoose.Schema.ObjectId,
          ref: 'User'
        }
      },
      lastPurchased: {
        date: Date,
        amount: Number,
        orderId: {
          type: mongoose.Schema.ObjectId,
          ref: 'Order'
        }
      },
      stockAlertSent: {
        lowStock: {
          type: Boolean,
          default: false
        },
        outOfStock: {
          type: Boolean,
          default: false
        },
        lastSent: Date
      }
    },
    lowStockThreshold: {
      type: Number,
      default: 3
    },
    price: {
      type: Number,
      required: [true, 'A variation must have a price']
    },
    salePrice: {
      type: Number,
      validate: {
        validator: function(val) {
          return val < this.price;
        },
        message: 'Sale price ({VALUE}) should be below regular price'
      }
    },
    saleStartDate: {
      type: Date
    },
    saleEndDate: {
      type: Date,
      validate: {
        validator: function(val) {
          return !this.saleStartDate || val > this.saleStartDate;
        },
        message: 'Sale end date must be after sale start date'
      }
    }
  },
  {
    toJSON: { virtuals: true },
    toObject: { virtuals: true }
  }
);


// Add a virtual property to check if the variation is on sale
variationSchema.virtual('onSale').get(function() {
  if (!this.salePrice || !this.saleStartDate || !this.saleEndDate) {
    return false;
  }

  const now = new Date();
  return now >= this.saleStartDate && now <= this.saleEndDate;
});

// Add a virtual property to check if the variation has active promotions
variationSchema.virtual('hasActivePromotion').get(function() {
  // This relies on the parent product's hasActivePromotion virtual
  return this.parent().hasActivePromotion;
});

// Add a virtual property to get the promotional price
variationSchema.virtual('promoPrice').get(function() {
  if (!this.hasActivePromotion) {
    return null;
  }

  const bestPromo = this.parent().bestPromotion;
  if (!bestPromo) {
    return null;
  }

  if (bestPromo.discountType === 'percentage') {
    return this.price * (1 - bestPromo.discountValue / 100);
  } else { // fixed discount
    return Math.max(0, this.price - bestPromo.discountValue);
  }
});

// Add a virtual property to get the current price (sale price, regular price, or global price)
variationSchema.virtual('currentPrice').get(function() {
  // First check for promotional price (promotions take priority)
  if (this.hasActivePromotion) {
    return this.promoPrice;
  }

  // Then check for sale price
  if (this.onSale) {
    return this.salePrice;
  }

  // Default to regular price
  return this.price;
});

// Add a virtual property to get the discount percentage
variationSchema.virtual('discountPercentage').get(function() {
  if (this.hasActivePromotion) {
    return Math.round(((this.price - this.promoPrice) / this.price) * 100);
  }

  if (this.onSale) {
    return Math.round(((this.price - this.salePrice) / this.price) * 100);
  }

  return 0;
});

// Add a virtual property to check if the variation has any discount (promo or sale)
variationSchema.virtual('hasDiscount').get(function() {
  return this.hasActivePromotion || this.onSale;
});

// Add a virtual property to get the discount source
variationSchema.virtual('discountSource').get(function() {
  if (this.hasActivePromotion) {
    return 'promotion';
  }

  if (this.onSale) {
    return 'sale';
  }

  return null;
});

// Add a virtual property to get the discount end date
variationSchema.virtual('discountEndDate').get(function() {
  if (this.hasActivePromotion) {
    const bestPromo = this.parent().bestPromotion;
    return bestPromo ? bestPromo.endDate : null;
  }

  if (this.onSale) {
    return this.saleEndDate;
  }

  return null;
});

// Add a virtual property to check if the variation is low on stock
variationSchema.virtual('isLowStock').get(function() {
  return this.quantity > 0 && this.quantity <= this.lowStockThreshold;
});

// Add a virtual property to check if the variation is out of stock
variationSchema.virtual('isOutOfStock').get(function() {
  return this.quantity === 0;
});

// Add a virtual property to get the last quantity change
variationSchema.virtual('lastQuantityChange').get(function() {
  if (!this.quantityHistory || this.quantityHistory.length === 0) {
    return null;
  }

  // Sort by timestamp descending and get the most recent entry
  const sortedHistory = [...this.quantityHistory].sort((a, b) =>
    new Date(b.timestamp) - new Date(a.timestamp)
  );

  return sortedHistory[0];
});

// Add a virtual property to get the last restock date and amount
variationSchema.virtual('lastRestockInfo').get(function() {
  if (!this.quantityHistory || this.quantityHistory.length === 0) {
    return null;
  }

  // Filter for restock events and sort by timestamp descending
  const restockEvents = this.quantityHistory
    .filter(entry => entry.changeType === 'restock')
    .sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));

  if (restockEvents.length === 0) {
    return null;
  }

  return {
    date: restockEvents[0].timestamp,
    amount: restockEvents[0].changeAmount
  };
});

// Method to record a quantity change with detailed information
variationSchema.methods.recordQuantityChange = function(options) {
  const {
    newQuantity,
    changeAmount,
    changeType = 'adjustment',
    reason = '',
    orderId = null,
    updatedBy = null
  } = options;

  // Mark that we're explicitly recording this change to avoid duplication in pre-save
  this._quantityChangeRecorded = true;

  // Store previous quantity for the pre-save middleware
  this._previousQuantity = this.quantity;

  // Update the quantity
  this.quantity = newQuantity;

  // Add to history (limited to 10 entries)
  const historyEntry = {
    quantity: newQuantity,
    changeAmount: changeAmount,
    changeType: changeType,
    reason: reason,
    timestamp: new Date(),
    orderId: orderId,
    updatedBy: updatedBy
  };

  this.quantityHistory.push(historyEntry);

  // Update inventory metrics based on change type
  if (changeType === 'restock') {
    this.set('inventoryMetrics.lastRestocked', {
      date: new Date(),
      amount: changeAmount,
      by: updatedBy
    });
    this.lastRestocked = new Date();
  } else if (changeType === 'purchase') {
    this.set('inventoryMetrics.lastPurchased', {
      date: new Date(),
      amount: Math.abs(changeAmount),
      orderId: orderId
    });
  }

  return this;
};



variationSchema.pre('save', async function(next) {
  // Skip expensive operations during initial creation (when document is new)
  if (this.isNew) {
    return next();
  }

  // Skip if no quantity modification
  if (!this.isModified('quantity')) {
    return next();
  }

  const previousQuantity = this._previousQuantity || 0;
  const currentQuantity = this.quantity;

  // --- 1. Limit quantityHistory to 10 entries ---
  if (this.quantityHistory && this.quantityHistory.length > 10) {
    this.quantityHistory = this.quantityHistory.slice(-10);
  }

  // --- 2. Update lastRestocked if stock increased ---
  if (currentQuantity > previousQuantity) {
    this.lastRestocked = new Date();
  }

  // --- 3. Push to quantityHistory (fallback if not explicitly recorded) ---
  if (!this._quantityChangeRecorded) {
    const changeAmount = currentQuantity - previousQuantity;
    const changeType =
      changeAmount > 0 ? 'restock' :
      changeAmount < 0 ? 'adjustment' : 'initial';

    this.quantityHistory.push({
      quantity: currentQuantity,
      changeAmount,
      changeType,
      timestamp: new Date()
    });

    // Ensure only last 10 are kept
    if (this.quantityHistory.length > 10) {
      this.quantityHistory = this.quantityHistory.slice(-10);
    }
  }

  // --- 4. Handle stock notifications (non-blocking for performance) ---
  if (!this._skipNotifications) {
    const alertFlags = this.inventoryMetrics?.stockAlertSent || {};
    const product = this.parent();

    // Update alert flags synchronously
    if (currentQuantity > 0 && currentQuantity <= this.lowStockThreshold &&
        previousQuantity > this.lowStockThreshold && !alertFlags.lowStock) {
      this.set('inventoryMetrics.stockAlertSent.lowStock', true);
      this.set('inventoryMetrics.stockAlertSent.lastSent', new Date());
    }

    if (currentQuantity === 0 && previousQuantity > 0 && !alertFlags.outOfStock) {
      this.set('inventoryMetrics.stockAlertSent.outOfStock', true);
      this.set('inventoryMetrics.stockAlertSent.lastSent', new Date());
    }

    if (currentQuantity > this.lowStockThreshold &&
       (alertFlags.lowStock || alertFlags.outOfStock)) {
      this.set('inventoryMetrics.stockAlertSent.lowStock', false);
      this.set('inventoryMetrics.stockAlertSent.outOfStock', false);
    }

    // Send notifications asynchronously (non-blocking)
    setImmediate(async () => {
      try {
        if (product?.creator) {
          const NotificationService = require('../services/notification.service');

          if (currentQuantity > 0 && currentQuantity <= this.lowStockThreshold &&
              previousQuantity > this.lowStockThreshold && !alertFlags.lowStock) {
            NotificationService.createProductNotification({
              product,
              type: 'low_stock',
              recipient: product.creator
            }).catch(err => console.error('Error sending low stock notification:', err));
          }

          if (currentQuantity === 0 && previousQuantity > 0 && !alertFlags.outOfStock) {
            NotificationService.createProductNotification({
              product,
              type: 'out_of_stock',
              recipient: product.creator
            }).catch(err => console.error('Error sending out of stock notification:', err));
          }
        }
      } catch (err) {
        console.error('Error in deferred stock notification:', err);
      }
    });
  }

  next();
});




// Define the specification schema
const specificationSchema = new mongoose.Schema(
  {
    mainMaterial: String,
    dressStyle: String,
    pantType: String,
    skirtType: String,
    mensPantSize: String,
    fitType: {
      type: String,
      enum: ['Slim', 'Regular', 'Loose', 'Oversized', 'Tailored', 'Skinny', 'Straight', 'Relaxed']
    },
    pattern: String,
    closure: String,
    neckline: String,
    sleeveLength: String,
    waistline: String,
    hemline: String
  },
  {
    _id: false
  }
);

const productSchema = new mongoose.Schema(
  {
    type: {
      type: String,
      enum: ['product', 'bale'],
      default: 'product'
    },
    name: {
      type: String,
      required: [true, 'A product must have a name'],
      trim: true,
      maxlength: [1000, 'A product name must have less or equal than 100 characters']
    },
    brand: {
      type: String,
      required: function() {
        return this.type === 'product';
      }
    },
    slug: String,
    description: {
      type: String,
      required: [true, 'A product must have a description'],
      trim: true
    },
    highlights: [String],
    gender: {
      type: String,
      enum: ['Male', 'Female', 'Unisex'],
      required: function() {
        return this.type === 'product';
      }
    },
    basePrice: {
      type: Number,
      required: [true, 'A product must have a base price']
    },
    images: [String],
    category: {
      type: mongoose.Schema.ObjectId,
      ref: 'Category',
      required: function() {
        return this.type === 'product';
      }
    },
    relatedCategories: [
      {
        type: mongoose.Schema.ObjectId,
        ref: 'Category'
      }
    ],
    tags: [String],
    creator: {
      type: mongoose.Schema.ObjectId,
      ref: 'User',
      required: [true, 'A product must belong to a creator']
    },
    sold: {
      type: Number,
      default: 0
    },
    ratingsAverage: {
      type: Number,
      default: 0,
      min: [0, 'Rating must be above 0.0'],
      max: [5, 'Rating must be below 5.0'],
      set: val => Math.round(val * 10) / 10
    },
    ratingsQuantity: {
      type: Number,
      default: 0
    },
    status: {
      type: String,
      enum: ['draft', 'pending', 'active', 'inactive'],
      default: 'draft'
    },
    featured: {
      type: Boolean,
      default: false
    },
    specifications: specificationSchema,
    // Bale-specific fields
    country: {
      type: String,
      required: function() {
        return this.type === 'bale';
      }
    },
    totalItems: {
      type: Number,
      required: function() {
        return this.type === 'bale';
      },
      min: [1, 'Total items must be at least 1']
    },
    weight: {
      type: Number,
      required: function() {
        return this.type === 'bale';
      }
    },
    dimensions: {
      length: Number,
      width: Number,
      height: Number
    },
    condition: {
      type: String,
      enum: ['New', 'Like New', 'Excellent', 'Good', 'Fair'],
      default: 'Good'
    },
    variations: [variationSchema],
    seo: {
      metaTitle: String,
      metaDescription: String,
      keywords: [String]
    },
    promotions: [
      {
        promotion: {
          type: mongoose.Schema.ObjectId,
          ref: 'Promotion',
          required: true
        },
        discountValue: {
          type: Number,
          required: true,
          min: [0, 'Discount value cannot be negative']
        },
        discountType: {
          type: String,
          enum: ['percentage', 'fixed'],
          required: true
        },
        promoStock: {
          type: Number,
          required: true,
          min: [0, 'Promotional stock cannot be negative']
        },
        startDate: {
          type: Date,
          required: true
        },
        endDate: {
          type: Date,
          required: true
        },
        isActive: {
          type: Boolean,
          default: true
        },
        addedAt: {
          type: Date,
          default: Date.now
        }
      }
    ]
  },
  {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true }
  }
);

// Indexes
productSchema.index({ basePrice: 1, ratingsAverage: -1 });
productSchema.index({ slug: 1 });
productSchema.index({ creator: 1 });
productSchema.index({ category: 1 });
productSchema.index({ brand: 1 });
productSchema.index({ gender: 1 });
productSchema.index({ type: 1 }); // For filtering by type
productSchema.index({ 'variations.color': 1 });
productSchema.index({ 'variations.size': 1 });
productSchema.index({ 'variations.identifier': 1 }); // For bale variations
productSchema.index({ country: 1 }); // For bale filtering
productSchema.index({ condition: 1 }); // For bale filtering
productSchema.index({ name: 'text', description: 'text' }); // For text search
productSchema.index({ createdAt: -1 }); // For sorting by date
productSchema.index({ status: 1 }); // For filtering by status

// Virtual populate for reviews
productSchema.virtual('reviews', {
  ref: 'Review',
  foreignField: 'product',
  localField: '_id'
});

// Virtual property to get total stock across all variations
productSchema.virtual('totalStock').get(function() {
  if (!this.variations || this.variations.length === 0) {
    return 0;
  }

  return this.variations.reduce((total, variation) => total + variation.quantity, 0);
});

// Virtual property to get available colors (for products)
productSchema.virtual('availableColors').get(function() {
  if (this.type !== 'product' || !this.variations || this.variations.length === 0) {
    return [];
  }

  const colors = new Set();
  this.variations.forEach(variation => {
    if (variation.quantity > 0 && variation.color) {
      colors.add(variation.color);
    }
  });

  return Array.from(colors);
});

// Virtual property to get available sizes (for products)
productSchema.virtual('availableSizes').get(function() {
  if (this.type !== 'product' || !this.variations || this.variations.length === 0) {
    return [];
  }

  const sizes = new Set();
  this.variations.forEach(variation => {
    if (variation.quantity > 0 && variation.size) {
      sizes.add(variation.size);
    }
  });

  return Array.from(sizes);
});

// Virtual property to get available identifiers (for bales)
productSchema.virtual('availableIdentifiers').get(function() {
  if (this.type !== 'bale' || !this.variations || this.variations.length === 0) {
    return [];
  }

  const identifiers = new Set();
  this.variations.forEach(variation => {
    if (variation.quantity > 0 && variation.identifier) {
      identifiers.add(variation.identifier);
    }
  });

  return Array.from(identifiers);
});

// Virtual property to get the normal (non-discounted) minimum price across all variations
productSchema.virtual('normalMinPrice').get(function() {
  if (!this.variations || this.variations.length === 0) {
    return this.basePrice;
  }

  const prices = this.variations.map(variation => variation.price);
  return Math.min(...prices);
});

// Virtual property to get the normal (non-discounted) maximum price across all variations
productSchema.virtual('normalMaxPrice').get(function() {
  if (!this.variations || this.variations.length === 0) {
    return this.basePrice;
  }

  const prices = this.variations.map(variation => variation.price);
  return Math.max(...prices);
});

// Virtual property to get the discounted minimum price across all variations
productSchema.virtual('discountedMinPrice').get(function() {
  if (!this.variations || this.variations.length === 0) {
    return this.basePrice;
  }

  const prices = this.variations.map(variation => variation.currentPrice);
  return Math.min(...prices);
});

// Virtual property to get the discounted maximum price across all variations
productSchema.virtual('discountedMaxPrice').get(function() {
  if (!this.variations || this.variations.length === 0) {
    return this.basePrice;
  }

  const prices = this.variations.map(variation => variation.currentPrice);
  return Math.max(...prices);
});

// Virtual property to check if any variation has a discount
productSchema.virtual('hasAnyDiscount').get(function() {
  if (!this.variations || this.variations.length === 0) {
    return false;
  }

  return this.variations.some(variation =>
    variation.hasActivePromotion || variation.onSale
  );
});

// Virtual property to get the maximum discount percentage across all variations
productSchema.virtual('maxDiscountPercentage').get(function() {
  if (!this.variations || this.variations.length === 0) {
    return 0;
  }

  let maxPercentage = 0;

  for (const variation of this.variations) {
    let discountPercentage = 0;

    if (variation.hasActivePromotion) {
      // Calculate promotion discount percentage
      discountPercentage = Math.round(((variation.price - variation.promoPrice) / variation.price) * 100);
    } else if (variation.onSale) {
      // Calculate sale discount percentage
      discountPercentage = Math.round(((variation.price - variation.salePrice) / variation.price) * 100);
    }

    if (discountPercentage > maxPercentage) {
      maxPercentage = discountPercentage;
    }
  }

  return maxPercentage;
});

// Virtual property to get formatted price range for display
productSchema.virtual('formattedPriceRange').get(function() {
  if (this.hasAnyDiscount) {
    return {
      original: {
        min: this.normalMinPrice,
        max: this.normalMaxPrice,
        isSinglePrice: this.normalMinPrice === this.normalMaxPrice
      },
      discounted: {
        min: this.discountedMinPrice,
        max: this.discountedMaxPrice,
        isSinglePrice: this.discountedMinPrice === this.discountedMaxPrice
      },
      discountPercentage: this.maxDiscountPercentage
    };
  } else {
    return {
      original: {
        min: this.normalMinPrice,
        max: this.normalMaxPrice,
        isSinglePrice: this.normalMinPrice === this.normalMaxPrice
      },
      discounted: null,
      discountPercentage: 0
    };
  }
});

// Virtual property to get the earliest discount end date
productSchema.virtual('discountEndDate').get(function() {
  if (!this.hasAnyDiscount) {
    return null;
  }

  // Get variations with active discounts (either promotion or sale)
  const discountedVariations = this.variations.filter(v => v.hasDiscount);

  // Extract end dates from each variation's active discount
  const endDates = discountedVariations.map(v => {
    // For each variation, get the end date of the discount that's actually being used
    // (the one that determines the currentPrice)
    if (v.hasActivePromotion && v.discountSource === 'promotion') {
      // If promotion is the active discount source, use the promotion end date
      const bestPromo = this.promoPrice;
      return bestPromo ? bestPromo.endDate : null;
    } else if (v.onSale && v.discountSource === 'sale') {
      // If sale is the active discount source, use the sale end date
      return v.saleEndDate;
    }
    return null;
  });

  // Filter out null dates
  const validDates = endDates.filter(date => date !== null);

  if (validDates.length === 0) {
    return null;
  }

  // Return the earliest end date (the first discount that will expire)
  return new Date(Math.min(...validDates.map(d => d.getTime())));
});

// Virtual property to get the discount type (promotion or sale)
productSchema.virtual('discountType').get(function() {
  if (!this.hasAnyDiscount) {
    return null;
  }

  // Prioritize promotion over sale if both exist
  if (this.variations.some(v => v.hasActivePromotion)) {
    return 'promotion';
  }

  return 'sale';
});

// Virtual property to check if the product has any active promotions
productSchema.virtual('hasActivePromotion').get(function() {
  if (!this.promotions || this.promotions.length === 0) {
    return false;
  }

  const now = new Date();
  return this.promotions.some(promo =>
    promo.isActive &&
    now >= promo.startDate &&
    now <= promo.endDate &&
    promo.promoStock > 0
  );
});

// Virtual property to get the best active promotion
productSchema.virtual('bestPromotion').get(function() {
  if (!this.hasActivePromotion) {
    return null;
  }

  const now = new Date();
  const activePromotions = this.promotions.filter(promo =>
    promo.isActive &&
    now >= promo.startDate &&
    now <= promo.endDate &&
    promo.promoStock > 0
  );

  if (activePromotions.length === 0) {
    return null;
  }

  // Return the promotion with the highest discount value
  return activePromotions.reduce((best, current) => {
    // For percentage discounts, higher percentage is better
    // For fixed discounts, higher amount is better
    if (best.discountType === current.discountType) {
      return current.discountValue > best.discountValue ? current : best;
    }

    // If different types, calculate effective discount on a sample price (e.g., 100)
    const samplePrice = 100;
    const bestDiscount = best.discountType === 'percentage'
      ? samplePrice * (best.discountValue / 100)
      : best.discountValue;

    const currentDiscount = current.discountType === 'percentage'
      ? samplePrice * (current.discountValue / 100)
      : current.discountValue;

    return currentDiscount > bestDiscount ? current : best;
  }, activePromotions[0]);
});

// Import image cleanup utilities
const { createImageCleanupMiddleware, executeImageCleanup } = require('../utils/cloudinaryCleanup');

// Document middleware: runs before .save() and .create()
productSchema.pre('save', function(next) {
  this.slug = slugify(this.name, { lower: true });
  next();
});

// Image cleanup middleware - runs before save to track image changes
productSchema.pre('save', createImageCleanupMiddleware(['images', 'variations.images']));

// Post-save middleware to execute image cleanup
productSchema.post('save', executeImageCleanup);

// Pre-remove middleware to cleanup all images when product is deleted
productSchema.pre('remove', async function(next) {
  try {
    const { cleanupEntityImages } = require('../utils/cloudinaryCleanup');

    // Collect all image fields including variation images
    const imageFields = ['images'];

    // Add variation images to cleanup
    if (this.variations && this.variations.length > 0) {
      this.variations.forEach((variation, index) => {
        if (variation.images && variation.images.length > 0) {
          imageFields.push(`variations.${index}.images`);
        }
      });
    }

    // Execute cleanup
    const result = await cleanupEntityImages(this, imageFields);
    console.log(`Cleaned up ${result.deleted} images for deleted product ${this._id}`);

    next();
  } catch (error) {
    console.error('Error cleaning up product images:', error);
    // Don't fail the deletion due to cleanup issues
    next();
  }
});

// Pre-deleteOne middleware for findByIdAndDelete operations
productSchema.pre('deleteOne', { document: true, query: false }, async function(next) {
  try {
    const { cleanupEntityImages } = require('../utils/cloudinaryCleanup');

    // Collect all image fields including variation images
    const imageFields = ['images'];

    // Add variation images to cleanup
    if (this.variations && this.variations.length > 0) {
      this.variations.forEach((variation, index) => {
        if (variation.images && variation.images.length > 0) {
          imageFields.push(`variations.${index}.images`);
        }
      });
    }

    // Execute cleanup
    const result = await cleanupEntityImages(this, imageFields);
    console.log(`Cleaned up ${result.deleted} images for deleted product ${this._id}`);

    next();
  } catch (error) {
    console.error('Error cleaning up product images:', error);
    // Don't fail the deletion due to cleanup issues
    next();
  }
});

// Compound indexes for filtering
productSchema.index({ 'variations.color': 1, 'variations.size': 1 }); // For filtering by color and size (products)
productSchema.index({ type: 1, 'variations.identifier': 1 }); // For filtering bale variations


// Static method for fast product creation without auto-population
productSchema.statics.createFast = function(productData) {
  // Create product without triggering query middleware
  const product = new this(productData);
  return product.save();
};

const Product = mongoose.model('Product', productSchema);

module.exports = Product;