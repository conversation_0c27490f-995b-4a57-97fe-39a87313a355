# Creator Products API - Frontend Integration Guide

## Overview
This guide provides comprehensive documentation for integrating the creator products backend APIs with your frontend application. All endpoints require creator authentication.

## Base URL
```
https://everyfash-api.onrender.com/api/v1/creators/products
```

## Authentication
All requests require a Bearer token in the Authorization header:
```javascript
headers: {
  'Authorization': `Bearer ${creatorToken}`,
  'Content-Type': 'application/json'
}
```

## Important: Data Format Requirements

### FormData with File Uploads
When uploading files (images), you must use `FormData` and follow these rules:

1. **Image Field Names:**
   - Product images: `productImages` (not `images`)
   - Variation images: `productImages` (same field)

2. **Array Data Format:**
   - **Complex arrays** (variations, specifications): Send as **JSON strings**
   - **Simple arrays** (highlights, tags): Can use JSON strings OR array notation (`field[]`)

3. **Examples:**
```javascript
// ✅ CORRECT - JSON string format
formData.append('variations', JSON.stringify([
  { color: 'Blue', size: 'M', quantity: 10, price: 25.99 }
]));
formData.append('highlights', JSON.stringify(['100% Cotton', 'Machine Washable']));

// ✅ CORRECT - Array notation (simple arrays only)
highlights.forEach(item => formData.append('highlights[]', item));
tags.forEach(item => formData.append('tags[]', item));

// ❌ INCORRECT - Don't append arrays directly
formData.append('variations', variations); // This won't work
```

### JSON Requests (No File Uploads)
For endpoints without file uploads, send regular JSON:
```javascript
headers: {
  'Authorization': `Bearer ${token}`,
  'Content-Type': 'application/json'
},
body: JSON.stringify({
  highlights: ['100% Cotton', 'Machine Washable'],
  tags: ['casual', 'cotton']
})
```

## Core Product Management

### 1. Get All Products
**Endpoint:** `GET /api/v1/creators/products`

**Query Parameters:**
- `page` (number): Page number (default: 1)
- `limit` (number): Items per page (default: 20)
- `sort` (string): Sort field (default: '-createdAt')
- `search` (string): Search in name, description, brand
- `status` (string): Filter by status ('draft', 'pending', 'active', 'inactive', 'rejected')
- `fields` (string): Comma-separated fields to include

**Example Request:**
```javascript
const getMyProducts = async (filters = {}) => {
  const queryParams = new URLSearchParams({
    page: filters.page || 1,
    limit: filters.limit || 20,
    sort: filters.sort || '-createdAt',
    ...(filters.search && { search: filters.search }),
    ...(filters.status && { status: filters.status })
  });

  const response = await fetch(`/api/v1/creators/products?${queryParams}`, {
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    }
  });

  return await response.json();
};
```

**Response:**
```json
{
  "status": "success",
  "results": 15,
  "total": 45,
  "page": 1,
  "limit": 20,
  "data": {
    "products": [
      {
        "_id": "64f7b1234567890123456789",
        "name": "Premium Cotton T-Shirt",
        "brand": "MyBrand",
        "description": "High-quality cotton t-shirt...",
        "basePrice": 25.99,
        "status": "active",
        "images": ["https://res.cloudinary.com/..."],
        "category": {
          "_id": "64f7b1234567890123456788",
          "name": "Clothing > T-Shirts",
          "description": "T-Shirts category"
        },
        "variations": [
          {
            "_id": "64f7b1234567890123456787",
            "color": "Blue",
            "size": "M",
            "quantity": 50,
            "price": 25.99,
            "images": ["https://res.cloudinary.com/..."]
          }
        ],
        "createdAt": "2024-01-15T10:30:00.000Z",
        "updatedAt": "2024-01-15T10:30:00.000Z"
      }
    ]
  }
}
```

### 2. Get Single Product
**Endpoint:** `GET /api/v1/creators/products/:id`

**Example Request:**
```javascript
const getMyProduct = async (productId) => {
  const response = await fetch(`/api/v1/creators/products/${productId}`, {
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    }
  });

  return await response.json();
};
```

### 3. Create Product
**Endpoint:** `POST /api/v1/creators/products`

**Content-Type:** `multipart/form-data` (for file uploads)

**Required Fields:**
- `name` (string): Product name
- `brand` (string): Brand name
- `description` (string): Product description
- `basePrice` (number): Base price
- `category` (ObjectId): Category ID
- `gender` (string): 'Men', 'Women', 'Unisex', 'Boys', 'Girls'
- `images` (files): Product images
- `variations` (array): At least one variation

**Example Request:**
```javascript
const createProduct = async (productData, images) => {
  const formData = new FormData();

  // Add basic fields
  formData.append('name', productData.name);
  formData.append('brand', productData.brand);
  formData.append('description', productData.description);
  formData.append('basePrice', productData.basePrice);
  formData.append('category', productData.category);
  formData.append('gender', productData.gender);

  // Add images (field name must be 'productImages' for the middleware)
  images.forEach((image) => {
    formData.append('productImages', image);
  });

  // IMPORTANT: Arrays must be sent as JSON strings
  formData.append('variations', JSON.stringify(productData.variations));

  // Add optional fields as JSON strings
  if (productData.highlights) {
    formData.append('highlights', JSON.stringify(productData.highlights));
  }

  if (productData.tags) {
    formData.append('tags', JSON.stringify(productData.tags));
  }

  if (productData.specifications) {
    formData.append('specifications', JSON.stringify(productData.specifications));
  }

  const response = await fetch('/api/v1/creators/products', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${token}`
      // Don't set Content-Type for FormData
    },
    body: formData
  });

  return await response.json();
};

// Alternative approach using array notation (for simple arrays only)
const createProductAlternative = async (productData, images) => {
  const formData = new FormData();

  // Add basic fields
  formData.append('name', productData.name);
  formData.append('brand', productData.brand);
  formData.append('description', productData.description);
  formData.append('basePrice', productData.basePrice);
  formData.append('category', productData.category);
  formData.append('gender', productData.gender);

  // Add images
  images.forEach((image) => {
    formData.append('productImages', image);
  });

  // For complex objects like variations, use JSON strings
  formData.append('variations', JSON.stringify(productData.variations));
  formData.append('specifications', JSON.stringify(productData.specifications));

  // For simple arrays, you can use array notation
  productData.highlights?.forEach(highlight => {
    formData.append('highlights[]', highlight);
  });

  productData.tags?.forEach(tag => {
    formData.append('tags[]', tag);
  });

  const response = await fetch('/api/v1/creators/products', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${token}`
    },
    body: formData
  });

  return await response.json();
};
```

### Real-World Example with Proper Formatting

```javascript
// Complete example showing correct data formatting
const createProductExample = async () => {
  const productData = {
    name: "Premium Cotton T-Shirt",
    brand: "MyBrand",
    description: "High-quality cotton t-shirt perfect for casual wear",
    basePrice: 25.99,
    category: "64f7b1234567890123456788",
    gender: "Unisex",
    highlights: ["100% Cotton", "Machine Washable", "Comfortable Fit"],
    tags: ["casual", "cotton", "comfortable"],
    specifications: {
      material: "100% Cotton",
      careInstructions: "Machine wash cold",
      origin: "Ghana",
      occasion: ["Casual", "Daily Wear"],
      season: ["Spring", "Summer", "Fall"]
    },
    variations: [
      {
        color: "Blue",
        size: "S",
        quantity: 25,
        price: 25.99,
        sku: "TSHIRT-BLUE-S"
      },
      {
        color: "Blue",
        size: "M",
        quantity: 50,
        price: 25.99,
        sku: "TSHIRT-BLUE-M"
      }
    ]
  };

  const images = [imageFile1, imageFile2]; // File objects from input

  const formData = new FormData();

  // Basic fields
  formData.append('name', productData.name);
  formData.append('brand', productData.brand);
  formData.append('description', productData.description);
  formData.append('basePrice', productData.basePrice);
  formData.append('category', productData.category);
  formData.append('gender', productData.gender);

  // Images - MUST use 'productImages' field name
  images.forEach(image => {
    formData.append('productImages', image);
  });

  // Complex arrays/objects - MUST be JSON strings
  formData.append('variations', JSON.stringify(productData.variations));
  formData.append('specifications', JSON.stringify(productData.specifications));
  formData.append('highlights', JSON.stringify(productData.highlights));
  formData.append('tags', JSON.stringify(productData.tags));

  const response = await fetch('/api/v1/creators/products', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${token}`
      // No Content-Type header for FormData
    },
    body: formData
  });

  return await response.json();
};
```

**Example Product Data:**
```javascript
const productData = {
  name: "Premium Cotton T-Shirt",
  brand: "MyBrand",
  description: "High-quality cotton t-shirt perfect for casual wear",
  basePrice: 25.99,
  category: "64f7b1234567890123456788",
  gender: "Unisex",
  highlights: ["100% Cotton", "Machine Washable", "Comfortable Fit"],
  tags: ["casual", "cotton", "comfortable"],
  specifications: {
    material: "100% Cotton",
    careInstructions: "Machine wash cold",
    origin: "Ghana",
    occasion: ["Casual", "Daily Wear"],
    season: ["Spring", "Summer", "Fall"]
  },
  variations: [
    {
      color: "Blue",
      size: "S",
      quantity: 25,
      price: 25.99,
      sku: "TSHIRT-BLUE-S"
    },
    {
      color: "Blue",
      size: "M", 
      quantity: 50,
      price: 25.99,
      sku: "TSHIRT-BLUE-M"
    },
    {
      color: "Red",
      size: "M",
      quantity: 30,
      price: 27.99,
      sku: "TSHIRT-RED-M"
    }
  ]
};
```

### 4. Update Product
**Endpoint:** `PATCH /api/v1/creators/products/:id`

**Note:** For active products, only variations can be updated.

**Example Request:**
```javascript
const updateProduct = async (productId, updateData, newImages = []) => {
  const formData = new FormData();

  // Add update fields with proper formatting
  Object.keys(updateData).forEach(key => {
    if (typeof updateData[key] === 'object' && !Array.isArray(updateData[key])) {
      // Objects like specifications
      formData.append(key, JSON.stringify(updateData[key]));
    } else if (Array.isArray(updateData[key])) {
      // Arrays like variations, highlights, tags - MUST be JSON strings
      formData.append(key, JSON.stringify(updateData[key]));
    } else {
      // Simple values
      formData.append(key, updateData[key]);
    }
  });

  // Add new images if any (field name must be 'productImages')
  newImages.forEach((image) => {
    formData.append('productImages', image);
  });

  const response = await fetch(`/api/v1/creators/products/${productId}`, {
    method: 'PATCH',
    headers: {
      'Authorization': `Bearer ${token}`
    },
    body: formData
  });

  return await response.json();
};
```

### 5. Delete Product
**Endpoint:** `DELETE /api/v1/creators/products/:id`

**Example Request:**
```javascript
const deleteProduct = async (productId) => {
  const response = await fetch(`/api/v1/creators/products/${productId}`, {
    method: 'DELETE',
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    }
  });

  return await response.json();
};
```

## Granular Product Updates

### 1. Update Basic Info
**Endpoint:** `PATCH /api/v1/creators/products/:id/basic-info`

**Example Request:**
```javascript
const updateBasicInfo = async (productId, basicInfo) => {
  const response = await fetch(`/api/v1/creators/products/${productId}/basic-info`, {
    method: 'PATCH',
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify(basicInfo)
  });

  return await response.json();
};

// Usage
await updateBasicInfo('64f7b1234567890123456789', {
  name: "Updated Product Name",
  brand: "Updated Brand",
  description: "Updated description",
  basePrice: 29.99
});
```

### 2. Update Specifications
**Endpoint:** `PATCH /api/v1/creators/products/:id/specifications`

**Example Request:**
```javascript
const updateSpecifications = async (productId, specifications) => {
  const response = await fetch(`/api/v1/creators/products/${productId}/specifications`, {
    method: 'PATCH',
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({ specifications })
  });

  return await response.json();
};

// Usage
await updateSpecifications('64f7b1234567890123456789', {
  material: "100% Organic Cotton",
  careInstructions: "Hand wash only",
  origin: "Ghana",
  occasion: ["Casual", "Formal"],
  season: ["All Season"]
});
```

### 3. Update SEO
**Endpoint:** `PATCH /api/v1/creators/products/:id/seo`

**Example Request:**
```javascript
const updateSEO = async (productId, seoData) => {
  const response = await fetch(`/api/v1/creators/products/${productId}/seo`, {
    method: 'PATCH',
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify(seoData)
  });

  return await response.json();
};

// Usage
await updateSEO('64f7b1234567890123456789', {
  highlights: ["Premium Quality", "Eco-Friendly", "Comfortable"],
  tags: ["cotton", "casual", "eco-friendly", "comfortable"]
});

## Variation Management

### 1. Get Product Variations
**Endpoint:** `GET /api/v1/creators/products/:id/variations`

**Example Request:**
```javascript
const getVariations = async (productId) => {
  const response = await fetch(`/api/v1/creators/products/${productId}/variations`, {
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    }
  });

  return await response.json();
};
```

### 2. Add Variation
**Endpoint:** `POST /api/v1/creators/products/:id/variations`

**Content-Type:** `multipart/form-data`

**Example Request:**
```javascript
const addVariation = async (productId, variationData, images = []) => {
  const formData = new FormData();

  // Add variation fields
  formData.append('color', variationData.color);
  formData.append('size', variationData.size);
  formData.append('quantity', variationData.quantity);
  formData.append('price', variationData.price);
  formData.append('sku', variationData.sku);

  // Add optional fields
  if (variationData.salePrice) {
    formData.append('salePrice', variationData.salePrice);
    formData.append('saleStartDate', variationData.saleStartDate);
    formData.append('saleEndDate', variationData.saleEndDate);
  }

  // Add variation images (field name must be 'productImages')
  images.forEach((image) => {
    formData.append('productImages', image);
  });

  const response = await fetch(`/api/v1/creators/products/${productId}/variations`, {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${token}`
    },
    body: formData
  });

  return await response.json();
};

// Usage
await addVariation('64f7b1234567890123456789', {
  color: "Green",
  size: "L",
  quantity: 40,
  price: 27.99,
  sku: "TSHIRT-GREEN-L"
}, [imageFile1, imageFile2]);
```

### 3. Update Variation
**Endpoint:** `PATCH /api/v1/creators/products/:id/variations/:variationId`

**Example Request:**
```javascript
const updateVariation = async (productId, variationId, updateData, newImages = []) => {
  const formData = new FormData();

  // Add update fields
  Object.keys(updateData).forEach(key => {
    formData.append(key, updateData[key]);
  });

  // Add new images if any (field name must be 'productImages')
  newImages.forEach((image) => {
    formData.append('productImages', image);
  });

  const response = await fetch(`/api/v1/creators/products/${productId}/variations/${variationId}`, {
    method: 'PATCH',
    headers: {
      'Authorization': `Bearer ${token}`
    },
    body: formData
  });

  return await response.json();
};

// Usage
await updateVariation('64f7b1234567890123456789', '64f7b1234567890123456787', {
  quantity: 60,
  price: 24.99
});
```

### 4. Delete Variation
**Endpoint:** `DELETE /api/v1/creators/products/:id/variations/:variationId`

**Example Request:**
```javascript
const deleteVariation = async (productId, variationId) => {
  const response = await fetch(`/api/v1/creators/products/${productId}/variations/${variationId}`, {
    method: 'DELETE',
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    }
  });

  return await response.json();
};
```

## Analytics & Statistics

### 1. Get Product Counts for Filtering
**Endpoint:** `GET /api/v1/creators/products/counts`

**Description:** Get product counts by status and stock levels for filter UI

**Example Request:**
```javascript
const getProductCounts = async () => {
  const response = await fetch('/api/v1/creators/products/counts', {
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    }
  });

  return await response.json();
};
```

**Response:**
```json
{
  "status": "success",
  "data": {
    "all": 45,
    "approved": 32,
    "pending": 8,
    "rejected": 2,
    "lowStock": 5,
    "outOfStock": 3
  }
}
```

**Filter Definitions:**
- **`all`** - Total number of products
- **`approved`** - Products with status 'active' (approved by admin)
- **`pending`** - Products awaiting admin approval
- **`rejected`** - Products rejected by admin
- **`lowStock`** - Products with total stock ≤ 10 units
- **`outOfStock`** - Products with 0 stock across all variations

### 2. Get Product Statistics
**Endpoint:** `GET /api/v1/creators/products/stats`

**Example Request:**
```javascript
const getProductStats = async () => {
  const response = await fetch('/api/v1/creators/products/stats', {
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    }
  });

  return await response.json();
};
```

**Response:**
```json
{
  "status": "success",
  "data": {
    "totalProducts": 45,
    "activeProducts": 32,
    "pendingProducts": 8,
    "draftProducts": 3,
    "rejectedProducts": 2,
    "totalRevenue": 15420.50,
    "totalOrders": 234,
    "averageRating": 4.6,
    "topSellingProducts": [
      {
        "_id": "64f7b1234567890123456789",
        "name": "Premium Cotton T-Shirt",
        "totalSales": 156,
        "revenue": 4056.44
      }
    ]
  }
}
```

### 2. Get Product Reviews
**Endpoint:** `GET /api/v1/creators/products/:id/reviews`

**Query Parameters:**
- `page` (number): Page number
- `limit` (number): Items per page
- `rating` (number): Filter by rating (1-5)

**Example Request:**
```javascript
const getProductReviews = async (productId, filters = {}) => {
  const queryParams = new URLSearchParams({
    page: filters.page || 1,
    limit: filters.limit || 10,
    ...(filters.rating && { rating: filters.rating })
  });

  const response = await fetch(`/api/v1/creators/products/${productId}/reviews?${queryParams}`, {
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    }
  });

  return await response.json();
};
```

### 3. Get Product Sales
**Endpoint:** `GET /api/v1/creators/products/:id/sales`

**Query Parameters:**
- `startDate` (date): Start date for sales data
- `endDate` (date): End date for sales data
- `period` (string): 'daily', 'weekly', 'monthly'

**Example Request:**
```javascript
const getProductSales = async (productId, filters = {}) => {
  const queryParams = new URLSearchParams({
    ...(filters.startDate && { startDate: filters.startDate }),
    ...(filters.endDate && { endDate: filters.endDate }),
    ...(filters.period && { period: filters.period })
  });

  const response = await fetch(`/api/v1/creators/products/${productId}/sales?${queryParams}`, {
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    }
  });

  return await response.json();
};
```

## Promotion Management

### 1. Get Product Promotions
**Endpoint:** `GET /api/v1/creators/products/:id/promotions`

**Example Request:**
```javascript
const getProductPromotions = async (productId) => {
  const response = await fetch(`/api/v1/creators/products/${productId}/promotions`, {
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    }
  });

  return await response.json();
};
```

### 2. Join Product Promotion
**Endpoint:** `POST /api/v1/creators/products/:id/promotions`

**Example Request:**
```javascript
const joinPromotion = async (productId, promotionData) => {
  const response = await fetch(`/api/v1/creators/products/${productId}/promotions`, {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify(promotionData)
  });

  return await response.json();
};

// Usage
await joinPromotion('64f7b1234567890123456789', {
  promotionId: '64f7b1234567890123456786',
  discountType: 'percentage',
  discountValue: 20,
  promoStock: 50
});
```

### 3. Update Product Promotion
**Endpoint:** `PATCH /api/v1/creators/products/:id/promotions/:promotionId`

**Example Request:**
```javascript
const updateProductPromotion = async (productId, promotionId, updateData) => {
  const response = await fetch(`/api/v1/creators/products/${productId}/promotions/${promotionId}`, {
    method: 'PATCH',
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify(updateData)
  });

  return await response.json();
};

// Usage
await updateProductPromotion('64f7b1234567890123456789', '64f7b1234567890123456786', {
  discountValue: 25,
  promoStock: 75
});
```

### 4. Leave Product Promotion
**Endpoint:** `DELETE /api/v1/creators/products/:id/promotions/:promotionId`

**Example Request:**
```javascript
const leavePromotion = async (productId, promotionId) => {
  const response = await fetch(`/api/v1/creators/products/${productId}/promotions/${promotionId}`, {
    method: 'DELETE',
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    }
  });

  return await response.json();
};
```

## Error Handling

### Common Error Responses

**Validation Error (400):**
```json
{
  "status": "fail",
  "message": "Please provide product name"
}
```

**Not Found Error (404):**
```json
{
  "status": "fail",
  "message": "No product found with that ID"
}
```

**Unauthorized Error (401):**
```json
{
  "status": "fail",
  "message": "You are not logged in! Please log in to get access."
}
```

**Forbidden Error (403):**
```json
{
  "status": "fail",
  "message": "Your account must be verified before you can perform this action."
}
```

### Error Handling Implementation

```javascript
const handleApiError = (error) => {
  if (error.response) {
    // Server responded with error status
    const { status, data } = error.response;

    switch (status) {
      case 400:
        return { type: 'validation', message: data.message };
      case 401:
        return { type: 'auth', message: 'Please log in again' };
      case 403:
        return { type: 'permission', message: data.message };
      case 404:
        return { type: 'notFound', message: 'Resource not found' };
      case 500:
        return { type: 'server', message: 'Server error. Please try again.' };
      default:
        return { type: 'unknown', message: data.message || 'An error occurred' };
    }
  } else if (error.request) {
    // Network error
    return { type: 'network', message: 'Network error. Please check your connection.' };
  } else {
    // Other error
    return { type: 'unknown', message: 'An unexpected error occurred' };
  }
};

// Usage in API calls
const createProductWithErrorHandling = async (productData, images) => {
  try {
    const result = await createProduct(productData, images);
    return { success: true, data: result };
  } catch (error) {
    const errorInfo = handleApiError(error);
    return { success: false, error: errorInfo };
  }
};
```

## React Hook Examples

### Custom Hook for Product Management

```javascript
import { useState, useEffect } from 'react';

const useProducts = () => {
  const [products, setProducts] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 20,
    total: 0
  });

  const fetchProducts = async (filters = {}) => {
    setLoading(true);
    setError(null);

    try {
      const result = await getMyProducts(filters);
      if (result.status === 'success') {
        setProducts(result.data.products);
        setPagination({
          page: result.page,
          limit: result.limit,
          total: result.total
        });
      }
    } catch (err) {
      setError(handleApiError(err));
    } finally {
      setLoading(false);
    }
  };

  const createNewProduct = async (productData, images) => {
    setLoading(true);
    setError(null);

    try {
      const result = await createProduct(productData, images);
      if (result.status === 'success') {
        // Refresh products list
        await fetchProducts();
        return { success: true, data: result.data };
      }
    } catch (err) {
      const errorInfo = handleApiError(err);
      setError(errorInfo);
      return { success: false, error: errorInfo };
    } finally {
      setLoading(false);
    }
  };

  const updateExistingProduct = async (productId, updateData, newImages = []) => {
    setLoading(true);
    setError(null);

    try {
      const result = await updateProduct(productId, updateData, newImages);
      if (result.status === 'success') {
        // Update local state
        setProducts(prev => prev.map(p =>
          p._id === productId ? result.data.product : p
        ));
        return { success: true, data: result.data };
      }
    } catch (err) {
      const errorInfo = handleApiError(err);
      setError(errorInfo);
      return { success: false, error: errorInfo };
    } finally {
      setLoading(false);
    }
  };

  const removeProduct = async (productId) => {
    setLoading(true);
    setError(null);

    try {
      const result = await deleteProduct(productId);
      if (result.status === 'success') {
        // Remove from local state
        setProducts(prev => prev.filter(p => p._id !== productId));
        return { success: true };
      }
    } catch (err) {
      const errorInfo = handleApiError(err);
      setError(errorInfo);
      return { success: false, error: errorInfo };
    } finally {
      setLoading(false);
    }
  };

  return {
    products,
    loading,
    error,
    pagination,
    fetchProducts,
    createNewProduct,
    updateExistingProduct,
    removeProduct
  };
};

export default useProducts;
```

## Component Examples

### Fixed Product Filter Component with Counts

```jsx
import React, { useEffect, useState } from 'react';

const ProductFilter = ({ onFilterChange, activeFilter = 'all' }) => {
  const [counts, setCounts] = useState({
    all: 0,
    approved: 0,
    pending: 0,
    rejected: 0,
    lowStock: 0,
    outOfStock: 0
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    fetchProductCounts();
  }, []);

  const fetchProductCounts = async () => {
    try {
      setLoading(true);
      setError(null);

      const token = localStorage.getItem('creatorToken'); // Adjust based on your auth implementation
      const response = await fetch(`${process.env.REACT_APP_API_URL}/api/v1/creators/products/counts`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();

      if (data.status === 'success') {
        setCounts(data.data);
      } else {
        throw new Error(data.message || 'Failed to fetch counts');
      }
    } catch (error) {
      console.error('Error fetching product counts:', error);
      setError(error.message);
    } finally {
      setLoading(false);
    }
  };

  const filterOptions = [
    { key: 'all', label: 'All Products', count: counts.all },
    { key: 'approved', label: 'Approved', count: counts.approved },
    { key: 'pending', label: 'Pending', count: counts.pending },
    { key: 'rejected', label: 'Rejected', count: counts.rejected },
    { key: 'lowStock', label: 'Low Stock', count: counts.lowStock },
    { key: 'outOfStock', label: 'Out of Stock', count: counts.outOfStock }
  ];

  const handleFilterClick = (filterKey) => {
    // Map filter keys to API parameters
    const filterMap = {
      all: {},
      approved: { status: 'active' },
      pending: { status: 'pending' },
      rejected: { status: 'rejected' },
      lowStock: { stockLevel: 'low' },
      outOfStock: { stockLevel: 'out' }
    };

    onFilterChange(filterMap[filterKey] || {}, filterKey);
  };

  if (loading) {
    return <div className="filter-loading">Loading filters...</div>;
  }

  if (error) {
    return (
      <div className="filter-error">
        <p>Error loading filters: {error}</p>
        <button onClick={fetchProductCounts}>Retry</button>
      </div>
    );
  }

  return (
    <div className="product-filters">
      <h3>Filter Products</h3>
      <div className="filter-buttons">
        {filterOptions.map((filter) => (
          <button
            key={filter.key}
            className={`filter-btn ${activeFilter === filter.key ? 'active' : ''}`}
            onClick={() => handleFilterClick(filter.key)}
          >
            <span className="filter-label">{filter.label}</span>
            <span className="filter-count">({filter.count})</span>
          </button>
        ))}
      </div>
    </div>
  );
};

### Product List Component

```jsx
import React, { useEffect, useState } from 'react';
import useProducts from './hooks/useProducts';
import ProductFilter from './ProductFilter';

const ProductList = () => {
  const {
    products,
    loading,
    error,
    pagination,
    fetchProducts,
    removeProduct
  } = useProducts();

  const [filters, setFilters] = useState({
    page: 1,
    limit: 20,
    status: '',
    search: '',
    stockLevel: ''
  });
  const [activeFilter, setActiveFilter] = useState('all');

  useEffect(() => {
    fetchProducts(filters);
  }, [filters]);

  const handleFilterChange = (newFilters) => {
    setFilters(prev => ({
      ...prev,
      ...newFilters,
      page: 1
    }));

    // Update active filter for UI
    if (newFilters.status === 'active') setActiveFilter('approved');
    else if (newFilters.status === 'pending') setActiveFilter('pending');
    else if (newFilters.status === 'rejected') setActiveFilter('rejected');
    else if (newFilters.stockLevel === 'low') setActiveFilter('lowStock');
    else if (newFilters.stockLevel === 'out') setActiveFilter('outOfStock');
    else setActiveFilter('all');
  };

  const handleSearch = (search) => {
    setFilters(prev => ({ ...prev, search, page: 1 }));
  };

  const handlePageChange = (page) => {
    setFilters(prev => ({ ...prev, page }));
  };

  const handleDeleteProduct = async (productId) => {
    if (window.confirm('Are you sure you want to delete this product?')) {
      const result = await removeProduct(productId);
      if (result.success) {
        alert('Product deleted successfully');
        // Refresh counts after deletion
        window.location.reload(); // Or implement a more elegant refresh
      } else {
        alert(`Error: ${result.error.message}`);
      }
    }
  };

  if (loading) return <div>Loading products...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <div className="product-list">
      {/* Filter Component */}
      <ProductFilter
        onFilterChange={handleFilterChange}
        activeFilter={activeFilter}
      />

      {/* Search */}
      <div className="search-section">
        <input
          type="text"
          placeholder="Search products..."
          value={filters.search}
          onChange={(e) => handleSearch(e.target.value)}
          className="search-input"
        />
      </div>

      {/* Products Grid */}
      <div className="products-grid">
        {products.map(product => (
          <div key={product._id} className="product-card">
            <img
              src={product.images[0]}
              alt={product.name}
              className="product-image"
            />
            <h3>{product.name}</h3>
            <p>{product.brand}</p>
            <p className="price">${product.basePrice}</p>
            <span className={`status ${product.status}`}>
              {product.status}
            </span>

            <div className="actions">
              <button onClick={() => window.location.href = `/products/${product._id}/edit`}>
                Edit
              </button>
              <button
                onClick={() => handleDeleteProduct(product._id)}
                className="delete-btn"
              >
                Delete
              </button>
            </div>
          </div>
        ))}
      </div>

      {/* Pagination */}
      <div className="pagination">
        <button
          disabled={pagination.page === 1}
          onClick={() => handlePageChange(pagination.page - 1)}
        >
          Previous
        </button>

        <span>
          Page {pagination.page} of {Math.ceil(pagination.total / pagination.limit)}
        </span>

        <button
          disabled={pagination.page >= Math.ceil(pagination.total / pagination.limit)}
          onClick={() => handlePageChange(pagination.page + 1)}
        >
          Next
        </button>
      </div>
    </div>
  );
};

export default ProductList;
```

### Product Form Component

```jsx
import React, { useState } from 'react';
import useProducts from './hooks/useProducts';

const ProductForm = ({ productId = null, initialData = null }) => {
  const { createNewProduct, updateExistingProduct, loading } = useProducts();

  const [formData, setFormData] = useState({
    name: initialData?.name || '',
    brand: initialData?.brand || '',
    description: initialData?.description || '',
    basePrice: initialData?.basePrice || '',
    category: initialData?.category?._id || '',
    gender: initialData?.gender || 'Unisex',
    highlights: initialData?.highlights || [''],
    tags: initialData?.tags || [''],
    specifications: initialData?.specifications || {
      material: '',
      careInstructions: '',
      origin: 'Ghana',
      occasion: [''],
      season: ['']
    },
    variations: initialData?.variations || [{
      color: '',
      size: '',
      quantity: '',
      price: '',
      sku: ''
    }]
  });

  const [images, setImages] = useState([]);
  const [errors, setErrors] = useState({});

  const handleInputChange = (field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: null }));
    }
  };

  const handleArrayChange = (field, index, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: prev[field].map((item, i) => i === index ? value : item)
    }));
  };

  const addArrayItem = (field) => {
    setFormData(prev => ({
      ...prev,
      [field]: [...prev[field], '']
    }));
  };

  const removeArrayItem = (field, index) => {
    setFormData(prev => ({
      ...prev,
      [field]: prev[field].filter((_, i) => i !== index)
    }));
  };

  const handleVariationChange = (index, field, value) => {
    setFormData(prev => ({
      ...prev,
      variations: prev.variations.map((variation, i) =>
        i === index ? { ...variation, [field]: value } : variation
      )
    }));
  };

  const addVariation = () => {
    setFormData(prev => ({
      ...prev,
      variations: [...prev.variations, {
        color: '',
        size: '',
        quantity: '',
        price: '',
        sku: ''
      }]
    }));
  };

  const removeVariation = (index) => {
    if (formData.variations.length > 1) {
      setFormData(prev => ({
        ...prev,
        variations: prev.variations.filter((_, i) => i !== index)
      }));
    }
  };

  const handleImageChange = (e) => {
    const files = Array.from(e.target.files);
    setImages(files);
  };

  const validateForm = () => {
    const newErrors = {};

    if (!formData.name.trim()) newErrors.name = 'Product name is required';
    if (!formData.brand.trim()) newErrors.brand = 'Brand is required';
    if (!formData.description.trim()) newErrors.description = 'Description is required';
    if (!formData.basePrice || formData.basePrice <= 0) newErrors.basePrice = 'Valid base price is required';
    if (!formData.category) newErrors.category = 'Category is required';
    if (!productId && images.length === 0) newErrors.images = 'At least one image is required';

    // Validate variations
    formData.variations.forEach((variation, index) => {
      if (!variation.color.trim()) newErrors[`variation_${index}_color`] = 'Color is required';
      if (!variation.size.trim()) newErrors[`variation_${index}_size`] = 'Size is required';
      if (!variation.quantity || variation.quantity < 0) newErrors[`variation_${index}_quantity`] = 'Valid quantity is required';
      if (!variation.price || variation.price <= 0) newErrors[`variation_${index}_price`] = 'Valid price is required';
    });

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!validateForm()) return;

    try {
      let result;
      if (productId) {
        result = await updateExistingProduct(productId, formData, images);
      } else {
        result = await createNewProduct(formData, images);
      }

      if (result.success) {
        alert(`Product ${productId ? 'updated' : 'created'} successfully!`);
        if (!productId) {
          // Reset form for new product
          setFormData({
            name: '',
            brand: '',
            description: '',
            basePrice: '',
            category: '',
            gender: 'Unisex',
            highlights: [''],
            tags: [''],
            specifications: {
              material: '',
              careInstructions: '',
              origin: 'Ghana',
              occasion: [''],
              season: ['']
            },
            variations: [{
              color: '',
              size: '',
              quantity: '',
              price: '',
              sku: ''
            }]
          });
          setImages([]);
        }
      } else {
        alert(`Error: ${result.error.message}`);
      }
    } catch (error) {
      alert('An unexpected error occurred');
    }
  };

  return (
    <form onSubmit={handleSubmit} className="product-form">
      {/* Basic Information */}
      <section className="form-section">
        <h3>Basic Information</h3>

        <div className="form-group">
          <label>Product Name *</label>
          <input
            type="text"
            value={formData.name}
            onChange={(e) => handleInputChange('name', e.target.value)}
            className={errors.name ? 'error' : ''}
          />
          {errors.name && <span className="error-text">{errors.name}</span>}
        </div>

        <div className="form-group">
          <label>Brand *</label>
          <input
            type="text"
            value={formData.brand}
            onChange={(e) => handleInputChange('brand', e.target.value)}
            className={errors.brand ? 'error' : ''}
          />
          {errors.brand && <span className="error-text">{errors.brand}</span>}
        </div>

        <div className="form-group">
          <label>Description *</label>
          <textarea
            value={formData.description}
            onChange={(e) => handleInputChange('description', e.target.value)}
            className={errors.description ? 'error' : ''}
            rows={4}
          />
          {errors.description && <span className="error-text">{errors.description}</span>}
        </div>

        <div className="form-row">
          <div className="form-group">
            <label>Base Price *</label>
            <input
              type="number"
              step="0.01"
              value={formData.basePrice}
              onChange={(e) => handleInputChange('basePrice', parseFloat(e.target.value))}
              className={errors.basePrice ? 'error' : ''}
            />
            {errors.basePrice && <span className="error-text">{errors.basePrice}</span>}
          </div>

          <div className="form-group">
            <label>Gender *</label>
            <select
              value={formData.gender}
              onChange={(e) => handleInputChange('gender', e.target.value)}
            >
              <option value="Men">Men</option>
              <option value="Women">Women</option>
              <option value="Unisex">Unisex</option>
              <option value="Boys">Boys</option>
              <option value="Girls">Girls</option>
            </select>
          </div>
        </div>

        <div className="form-group">
          <label>Product Images {!productId && '*'}</label>
          <input
            type="file"
            multiple
            accept="image/*"
            onChange={handleImageChange}
            className={errors.images ? 'error' : ''}
          />
          {errors.images && <span className="error-text">{errors.images}</span>}
          {images.length > 0 && (
            <div className="image-preview">
              {images.map((image, index) => (
                <div key={index} className="preview-item">
                  <img
                    src={URL.createObjectURL(image)}
                    alt={`Preview ${index + 1}`}
                    width="100"
                    height="100"
                  />
                  <span>{image.name}</span>
                </div>
              ))}
            </div>
          )}
        </div>
      </section>

      {/* Variations */}
      <section className="form-section">
        <h3>Product Variations</h3>

        {formData.variations.map((variation, index) => (
          <div key={index} className="variation-group">
            <h4>Variation {index + 1}</h4>

            <div className="form-row">
              <div className="form-group">
                <label>Color *</label>
                <input
                  type="text"
                  value={variation.color}
                  onChange={(e) => handleVariationChange(index, 'color', e.target.value)}
                  className={errors[`variation_${index}_color`] ? 'error' : ''}
                />
                {errors[`variation_${index}_color`] &&
                  <span className="error-text">{errors[`variation_${index}_color`]}</span>
                }
              </div>

              <div className="form-group">
                <label>Size *</label>
                <input
                  type="text"
                  value={variation.size}
                  onChange={(e) => handleVariationChange(index, 'size', e.target.value)}
                  className={errors[`variation_${index}_size`] ? 'error' : ''}
                />
                {errors[`variation_${index}_size`] &&
                  <span className="error-text">{errors[`variation_${index}_size`]}</span>
                }
              </div>

              <div className="form-group">
                <label>Quantity *</label>
                <input
                  type="number"
                  value={variation.quantity}
                  onChange={(e) => handleVariationChange(index, 'quantity', parseInt(e.target.value))}
                  className={errors[`variation_${index}_quantity`] ? 'error' : ''}
                />
                {errors[`variation_${index}_quantity`] &&
                  <span className="error-text">{errors[`variation_${index}_quantity`]}</span>
                }
              </div>

              <div className="form-group">
                <label>Price *</label>
                <input
                  type="number"
                  step="0.01"
                  value={variation.price}
                  onChange={(e) => handleVariationChange(index, 'price', parseFloat(e.target.value))}
                  className={errors[`variation_${index}_price`] ? 'error' : ''}
                />
                {errors[`variation_${index}_price`] &&
                  <span className="error-text">{errors[`variation_${index}_price`]}</span>
                }
              </div>

              <div className="form-group">
                <label>SKU</label>
                <input
                  type="text"
                  value={variation.sku}
                  onChange={(e) => handleVariationChange(index, 'sku', e.target.value)}
                />
              </div>
            </div>

            {formData.variations.length > 1 && (
              <button
                type="button"
                onClick={() => removeVariation(index)}
                className="remove-btn"
              >
                Remove Variation
              </button>
            )}
          </div>
        ))}

        <button type="button" onClick={addVariation} className="add-btn">
          Add Variation
        </button>
      </section>

      {/* Submit Button */}
      <div className="form-actions">
        <button type="submit" disabled={loading} className="submit-btn">
          {loading ? 'Saving...' : (productId ? 'Update Product' : 'Create Product')}
        </button>
      </div>
    </form>
  );
};

export default ProductForm;
```

## Best Practices

### 1. Image Optimization
- Compress images before upload
- Use appropriate image formats (WebP, JPEG)
- Implement image preview functionality
- Handle multiple image uploads efficiently

### 2. Form Validation
- Implement client-side validation for better UX
- Show real-time validation feedback
- Handle server-side validation errors gracefully

### 3. Performance Optimization
- Implement pagination for product lists
- Use debouncing for search functionality
- Cache frequently accessed data
- Implement lazy loading for images

### 4. Error Handling
- Provide clear, actionable error messages
- Implement retry mechanisms for failed requests
- Handle network errors gracefully
- Log errors for debugging

### 5. User Experience
- Show loading states during API calls
- Provide confirmation dialogs for destructive actions
- Implement auto-save for draft products
- Use optimistic updates where appropriate

### 6. Security
- Validate file types and sizes on upload
- Sanitize user inputs
- Implement proper authentication checks
- Handle sensitive data appropriately

This comprehensive guide should help you integrate the creator products backend APIs effectively with your frontend application.
```
```
```
