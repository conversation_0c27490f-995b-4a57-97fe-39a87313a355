const express = require('express');
const productController = require('../controllers/buyers/product.controller');

const router = express.Router();

// Public product routes
router.get('/', productController.getProductFeed);
router.get('/filter-params', productController.getFilterParams);
router.get('/sales', productController.getFlashySales);
router.get('/featured', productController.getFeaturedProducts);
router.get('/discounted', productController.getDiscountedProducts);
router.get('/by-categories', productController.getProductsByCategories);
router.get('/category/:id', productController.getProductsByCategory);
router.get('/:id', productController.getProduct);

module.exports = router;
