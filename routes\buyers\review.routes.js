const express = require('express');
const reviewController = require('../../controllers/buyers/review.controller');
const authMiddleware = require('../../middleware/auth.middleware');

const router = express.Router();

// Protect all routes
router.use(authMiddleware.protect);
router.use(authMiddleware.restrictTo('buyer'));

// Review routes
router.get('/', reviewController.getMyReviews);
router.post('/products/:id', reviewController.createProductReview);
router.post('/bales/:id', reviewController.createBaleReview);
router.post('/creators/:id', reviewController.createCreatorReview);
router.patch('/:id/helpful', reviewController.markReviewAsHelpful);

module.exports = router;
