# Creator Model Cleanup Documentation

## Overview
This document outlines the removal of shop policies, seller profile, and sales channel functionality from the Creator model and related controllers to simplify the onboarding and profile management process.

## Changes Made

### 1. Creator Model (`models/creator.model.js`)

#### Removed Fields:
```javascript
// REMOVED: sellerProfile section
sellerProfile: {
  sellerType: {
    type: String,
    enum: ['wholesale', 'retail', 'both']
  },
  sellsOffline: {
    type: Boolean,
    default: false
  },
  usesOtherChannels: {
    type: Boolean,
    default: false
  },
  otherChannels: {
    type: String,
    trim: true
  }
}
```

#### Retained Fields:
- `businessInfo` - Business registration and verification details
- `metrics` - Performance and analytics data
- `paymentInfo` - Payment method preferences
- `payoutPreferences` - Payout frequency and minimum amounts
- `shopInfo` - Basic shop information (name, contact, description, etc.)
- `shippingInfo` - Shipping and return addresses
- `verificationStatus` - Account verification status
- `socialMedia` - Social media links
- `onboardingStatus` and `onboardingProgress` - Onboarding tracking

### 2. Onboarding Controller (`controllers/creators/onboarding.controller.js`)

#### Removed Functionality:

**Shop Info Endpoint Changes:**
- Removed `sellerProfile` from response data
- Removed policies handling in form data processing
- Removed seller profile field processing
- Removed sales channels validation and processing
- Simplified required fields validation

**Before:**
```javascript
// Required fields included seller type and offline selling status
const requiredFields = ['name', 'sellerType', 'sellsOffline'];

// Complex validation for sales channels
if (req.body.sellsOffline === true && (!req.body.salesChannels || !Array.isArray(req.body.salesChannels) || req.body.salesChannels.length === 0)) {
  return next(new AppError('Please provide at least one sales channel', 400));
}
```

**After:**
```javascript
// Simplified to only require shop name
const requiredFields = ['name'];

// No sales channel validation needed
```

#### Updated Response Format:
```javascript
// Before
{
  "status": "success",
  "data": {
    "shopInfo": {...},
    "socialMedia": {...},
    "sellerProfile": {...}  // REMOVED
  }
}

// After
{
  "status": "success",
  "data": {
    "shopInfo": {...},
    "socialMedia": {...}
  }
}
```

### 3. Profile Controller (`controllers/creators/profile.controller.js`)

#### Applied Same Changes:
- Removed `sellerProfile` from GET and UPDATE endpoints
- Removed policies and sales channels handling
- Simplified validation requirements
- Updated response formats to exclude removed fields

### 4. Database Migration Considerations

#### Existing Data:
- Existing `sellerProfile` data in the database will remain but won't be accessible through the API
- No automatic data migration is required
- Data can be manually cleaned up if needed

#### Backward Compatibility:
- API endpoints will continue to work but ignore removed fields
- Frontend applications should be updated to remove references to:
  - `sellerProfile` fields
  - `policies` fields  
  - `salesChannels` fields
  - `sellsOffline` and `sellerType` fields

## Impact on Frontend

### Forms to Update:

1. **Shop Info Form:**
   - Remove seller type selection
   - Remove offline selling toggle
   - Remove sales channels multi-select
   - Remove policies text areas
   - Remove seller profile bio/experience fields

2. **API Calls:**
   - Update request payloads to exclude removed fields
   - Update response handling to not expect removed fields

### Example Frontend Changes:

**Before:**
```javascript
const shopInfoData = {
  name: "My Shop",
  sellerType: "retail",        // REMOVE
  sellsOffline: true,          // REMOVE
  salesChannels: ["market"],   // REMOVE
  policies: {                  // REMOVE
    returns: "30 day returns",
    shipping: "Free shipping"
  },
  contact: {
    name: "John Doe",
    email: "<EMAIL>",
    phone: "+233123456789"
  }
};
```

**After:**
```javascript
const shopInfoData = {
  name: "My Shop",
  contact: {
    name: "John Doe", 
    email: "<EMAIL>",
    phone: "+233123456789"
  },
  description: "Shop description", // Optional
  logo: "logo_url",               // Optional
  banner: "banner_url"            // Optional
};
```

## Benefits of Cleanup

1. **Simplified Onboarding:**
   - Fewer required fields for shop setup
   - Faster onboarding process
   - Reduced complexity for new creators

2. **Cleaner Data Model:**
   - Removed unused/redundant fields
   - More focused shop information
   - Easier to maintain and extend

3. **Better User Experience:**
   - Less overwhelming forms
   - Clearer requirements
   - Streamlined profile management

4. **Reduced Validation Complexity:**
   - Fewer validation rules
   - Simpler error handling
   - More predictable API behavior

## API Endpoints Affected

### Shop Info Endpoints:
- `GET /api/v1/creators/onboarding/shop-info`
- `PATCH /api/v1/creators/onboarding/shop-info`
- `GET /api/v1/creators/profile/shop-info`
- `PATCH /api/v1/creators/profile/shop-info`

### Response Changes:
All shop info endpoints now return simplified data structure without:
- `sellerProfile` object
- `policies` within `shopInfo`
- `salesChannels` within `shopInfo`
- `sellsOffline` and `sellerType` within `shopInfo`

## Testing Recommendations

1. **API Testing:**
   - Verify all shop info endpoints return expected simplified structure
   - Test that removed fields in request payloads are ignored gracefully
   - Confirm validation only requires simplified field set

2. **Frontend Testing:**
   - Update component tests to match new data structure
   - Test form submissions without removed fields
   - Verify UI doesn't break with missing fields

3. **Integration Testing:**
   - Test complete onboarding flow with simplified shop info
   - Verify profile updates work with new structure
   - Test backward compatibility with existing creator accounts

## Migration Steps

1. **Backend Deployment:**
   - Deploy updated model and controllers
   - Monitor for any errors in production

2. **Frontend Updates:**
   - Update forms to remove deprecated fields
   - Update API integration code
   - Test thoroughly in staging environment

3. **Data Cleanup (Optional):**
   - Run database cleanup script to remove unused fields
   - Archive old data if needed for historical purposes

## Rollback Plan

If rollback is needed:
1. Revert model changes to restore removed fields
2. Revert controller changes to restore field processing
3. Update frontend to re-include removed fields
4. Test thoroughly before production deployment

The changes are designed to be backward compatible, so rollback should be straightforward if needed.
