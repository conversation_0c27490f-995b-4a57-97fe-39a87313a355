{"name": "everyfash-api", "version": "1.0.0", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "dev": "nodemon server.js", "start": "node server.js", "setup-dirs": "node scripts/setup-upload-dirs.js"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"bcryptjs": "^3.0.2", "cloudinary": "^1.41.3", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "dotenv": "^16.4.7", "express": "^5.1.0", "express-rate-limit": "^7.5.0", "express-validator": "^7.2.1", "helmet": "^8.1.0", "joi": "^17.13.3", "jsonwebtoken": "^9.0.2", "mongoose": "^8.13.2", "morgan": "^1.10.0", "multer": "^1.4.5-lts.2", "multer-storage-cloudinary": "^4.0.0", "nodemailer": "^6.10.0", "passport": "^0.7.0", "passport-facebook": "^3.0.0", "passport-google-oauth20": "^2.0.0", "passport-jwt": "^4.0.1", "passport-local": "^1.0.0", "slugify": "^1.6.6"}, "devDependencies": {"nodemon": "^3.1.9"}}