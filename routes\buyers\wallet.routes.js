const express = require('express');
const walletController = require('../../controllers/buyers/wallet.controller');
const authMiddleware = require('../../middleware/auth.middleware');

const router = express.Router();

// Protect all routes
router.use(authMiddleware.protect);
router.use(authMiddleware.restrictTo('buyer'));

// Wallet routes
router.get('/balance', walletController.getWalletBalance);
router.get('/transactions', walletController.getWalletTransactions);
router.get('/summary', walletController.getWalletTransactionSummary);

module.exports = router;
