const NotificationService = require('../services/notification.service');
const AppError = require('../utils/appError');
const catchAsync = require('../utils/catchAsync');

/**
 * Get notifications for the authenticated user
 * @route GET /api/v1/notifications
 * @access Private
 */
exports.getNotifications = catchAsync(async (req, res, next) => {
  const options = {
    page: req.query.page ? parseInt(req.query.page) : 1,
    limit: req.query.limit ? parseInt(req.query.limit) : 10,
    read: req.query.read === 'true' ? true : req.query.read === 'false' ? false : undefined,
    type: req.query.type,
    priority: req.query.priority,
    populate: req.query.populate ? req.query.populate.split(',') : []
  };

  const result = await NotificationService.getNotifications(req.user.id, options);

  res.status(200).json({
    status: 'success',
    results: result.notifications.length,
    pagination: result.pagination,
    data: {
      notifications: result.notifications
    }
  });
});

/**
 * Get unread notifications count for the authenticated user
 * @route GET /api/v1/notifications/unread-count
 * @access Private
 */
exports.getUnreadCount = catchAsync(async (req, res, next) => {
  const count = await NotificationService.getUnreadCount(req.user.id);

  res.status(200).json({
    status: 'success',
    data: {
      count
    }
  });
});

/**
 * Mark a notification as read
 * @route PATCH /api/v1/notifications/:id/read
 * @access Private
 */
exports.markAsRead = catchAsync(async (req, res, next) => {
  const notification = await NotificationService.markAsRead(req.params.id, req.user.id);

  res.status(200).json({
    status: 'success',
    data: {
      notification
    }
  });
});

/**
 * Mark all notifications as read for the authenticated user
 * @route PATCH /api/v1/notifications/mark-all-read
 * @access Private
 */
exports.markAllAsRead = catchAsync(async (req, res, next) => {
  const count = await NotificationService.markAllAsRead(req.user.id);

  res.status(200).json({
    status: 'success',
    data: {
      count
    }
  });
});

/**
 * Delete a notification
 * @route DELETE /api/v1/notifications/:id
 * @access Private
 */
exports.deleteNotification = catchAsync(async (req, res, next) => {
  const result = await NotificationService.deleteNotification(req.params.id, req.user.id);

  res.status(200).json({
    status: 'success',
    data: result
  });
});

/**
 * Create a notification (Admin only)
 * @route POST /api/v1/notifications
 * @access Private (Admin only)
 */
exports.createNotification = catchAsync(async (req, res, next) => {
  // Validate request body
  if (!req.body.recipient) {
    return next(new AppError('Recipient is required', 400));
  }

  if (!req.body.type) {
    return next(new AppError('Notification type is required', 400));
  }

  if (!req.body.title) {
    return next(new AppError('Notification title is required', 400));
  }

  if (!req.body.message) {
    return next(new AppError('Notification message is required', 400));
  }

  // Set sender to current user
  req.body.sender = req.user.id;

  const notification = await NotificationService.createNotification(req.body);

  res.status(201).json({
    status: 'success',
    data: {
      notification
    }
  });
});
