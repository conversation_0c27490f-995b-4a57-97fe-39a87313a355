# Authentication and Authorization

This document outlines the authentication and authorization system for the Flashy API.

## Models

### BaseUser Model

```javascript
{
  name: {
    type: String,
    required: [true, 'Please tell us your name!']
  },
  email: {
    type: String,
    required: [true, 'Please provide your email'],
    unique: true,
    lowercase: true,
    validate: [validator.isEmail, 'Please provide a valid email']
  },
  photo: {
    type: String,
  },
  role: {
    type: String,
    enum: ['buyer', 'creator', 'admin'],
    required: [true, 'User must have a role']
  },
  socialProvider: {
    type: String,
    enum: ['google', 'facebook', null],
    default: null
  },
  password: {
    type: String,
    select: false
  },
  passwordChangedAt: Date,
  passwordResetToken: String,
  passwordResetExpires: Date,
  active: {
    type: Boolean,
    default: true,
    select: false
  }
}
```

The BaseUser model includes the following virtual properties and methods:

**Virtual Properties:**
```javascript
// Virtual populate for user's orders
baseUserSchema.virtual('orders', {
  ref: 'Order',
  foreignField: 'user',
  localField: '_id'
});

// Virtual populate for user's notifications
baseUserSchema.virtual('notifications', {
  ref: 'Notification',
  foreignField: 'recipient',
  localField: '_id'
});

// Virtual for unread notifications count
baseUserSchema.virtual('unreadNotificationsCount');

// Virtual for notification preferences
baseUserSchema.virtual('notificationPreferences');
```

**Methods:**
```javascript
// Check if password is correct
baseUserSchema.methods.correctPassword = async function(candidatePassword, userPassword);

// Check if password was changed after token was issued
baseUserSchema.methods.changedPasswordAfter = function(JWTTimestamp);

// Create password reset token
baseUserSchema.methods.createPasswordResetToken = function();

// Reset password
baseUserSchema.methods.resetPassword = async function(newPassword);
```


The BaseUser model serves as the parent model for all user types (Buyer, Creator, Admin) using Mongoose's discriminator pattern.

## Authentication Endpoints

### Registration

| Endpoint | Method | Description | Request Body | Response |
|----------|--------|-------------|--------------|----------|
| `/api/v1/auth/register/buyer` | POST | Register a new buyer | `{ name, email, password, passwordConfirm }` | JWT token, user data |
| `/api/v1/auth/register/creator` | POST | Register a new creator | `{ name, email, password, passwordConfirm }` | JWT token, user data |
| `/api/v1/auth/register/admin` | POST | Register a new admin (requires super admin privileges) | `{ name, email, password, passwordConfirm, adminLevel, department }` | JWT token, user data |

### Login

| Endpoint | Method | Description | Request Body | Response |
|----------|--------|-------------|--------------|----------|
| `/api/v1/auth/login` | POST | Login with email and password | `{ email, password }` | JWT token, user data |
| `/api/v1/auth/logout` | GET | Logout user | - | Success message |

### Password Management

| Endpoint | Method | Description | Request Body | Response |
|----------|--------|-------------|--------------|----------|
| `/api/v1/auth/forgot-password` | POST | Request password reset | `{ email }` | Success message |
| `/api/v1/auth/reset-password/:token` | PATCH | Reset password with token | `{ password, passwordConfirm }` | JWT token, user data |
| `/api/v1/auth/update-password` | PATCH | Update password (authenticated) | `{ passwordCurrent, password, passwordConfirm }` | JWT token, user data |

### Social Authentication

| Endpoint | Method | Description | Request Body | Response |
|----------|--------|-------------|--------------|----------|
| `/api/v1/auth/google/buyer` | GET | Initiate Google auth for buyers | - | Redirect to Google |
| `/api/v1/auth/google/buyer/callback` | GET | Google auth callback for buyers | - | JWT token, user data |
| `/api/v1/auth/google/creator` | GET | Initiate Google auth for creators | - | Redirect to Google |
| `/api/v1/auth/google/creator/callback` | GET | Google auth callback for creators | - | JWT token, user data |
| `/api/v1/auth/facebook/buyer` | GET | Initiate Facebook auth for buyers | - | Redirect to Facebook |
| `/api/v1/auth/facebook/buyer/callback` | GET | Facebook auth callback for buyers | - | JWT token, user data |
| `/api/v1/auth/facebook/creator` | GET | Initiate Facebook auth for creators | - | Redirect to Facebook |
| `/api/v1/auth/facebook/creator/callback` | GET | Facebook auth callback for creators | - | JWT token, user data |

### User Information

| Endpoint | Method | Description | Request Body | Response |
|----------|--------|-------------|--------------|----------|
| `/api/v1/auth/me` | GET | Get current user information | - | User data |

## Authorization Middleware

The system uses middleware for protecting routes and restricting access based on user roles:

### Middleware Functions

1. **protect** - Verifies JWT token and attaches user to request
   - Checks if token exists in Authorization header or cookies
   - Verifies token signature
   - Checks if user still exists
   - Checks if user changed password after token was issued

2. **restrictTo** - Restricts access to specific user roles
   - Takes roles as parameters (e.g., 'admin', 'creator', 'buyer')
   - Returns 403 error if user's role doesn't match

3. **verificationMiddleware.requireVerifiedAndOnboarded** - For creator routes
   - Ensures creator has completed onboarding
   - Ensures creator is verified (when required)

## Authentication Flow

1. **Registration**:
   - User submits registration form with role (buyer or creator)
   - System validates input and checks for existing email
   - Password is hashed using bcrypt with cost factor of 12
   - User is created in database with appropriate role model (Buyer or Creator)
   - JWT token is generated with user ID and role
   - Token is sent to client in HTTP-only cookie and response body

2. **Login**:
   - User submits email and password
   - System finds user by email and checks active status
   - Password is verified using bcrypt.compare()
   - System checks if password was changed after token issuance
   - JWT token is generated with user ID and role
   - Token is sent to client in HTTP-only cookie and response body

3. **Social Authentication**:
   - User clicks social login button (Google or Facebook) with role selection
   - System redirects to OAuth provider with appropriate scopes
   - Provider authenticates user and redirects back with authorization code
   - System exchanges code for user information (email, name, profile picture)
   - System creates new user or updates existing user with socialProvider field
   - JWT token is generated with user ID and role
   - Token is sent to client in HTTP-only cookie and response body

4. **Password Reset**:
   - User requests password reset by providing email
   - System generates random reset token using crypto.randomBytes()
   - Token is hashed and stored in passwordResetToken field
   - Token expiration (10 minutes) is stored in passwordResetExpires field
   - System sends email with reset link containing unhashed token
   - User clicks link in email and submits new password
   - System finds user by hashed token and checks expiration
   - Password is updated and reset fields are cleared
   - JWT token is generated and sent to client

5. **Token Verification**:
   - For protected routes, system extracts token from Authorization header or cookies
   - Token is verified using JWT secret
   - System checks if user still exists in database
   - System checks if user changed password after token was issued
   - User object is attached to request for use in route handlers

## Security Considerations

- Passwords are hashed using bcrypt
- JWT tokens are signed with a secret key
- Password reset tokens are hashed before storage
- Sensitive fields are excluded from query results
- CORS is configured to restrict access
- Rate limiting is applied to prevent brute force attacks
- HTTP security headers are set using Helmet
- XSS protection is implemented
- Parameter pollution protection is applied
