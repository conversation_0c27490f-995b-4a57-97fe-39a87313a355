const fs = require('fs');
const path = require('path');

/**
 * <PERSON><PERSON><PERSON> to create necessary upload directories
 */

const uploadDirs = [
  'public',
  'public/uploads',
  'public/uploads/verification',
  'public/uploads/products',
  'public/uploads/bales',
  'public/uploads/profiles',
  'public/uploads/others'
];

console.log('Creating upload directories...');

uploadDirs.forEach(dir => {
  const dirPath = path.join(process.cwd(), dir);
  
  if (!fs.existsSync(dirPath)) {
    try {
      fs.mkdirSync(dirPath, { recursive: true });
      console.log(`✅ Created directory: ${dir}`);
    } catch (error) {
      console.error(`❌ Failed to create directory ${dir}:`, error.message);
    }
  } else {
    console.log(`📁 Directory already exists: ${dir}`);
  }
});

// Create a .gitkeep file in each upload directory to ensure they're tracked by git
const gitkeepDirs = [
  'public/uploads/verification',
  'public/uploads/products',
  'public/uploads/bales',
  'public/uploads/profiles',
  'public/uploads/others'
];

gitkeepDirs.forEach(dir => {
  const gitkeepPath = path.join(process.cwd(), dir, '.gitkeep');
  
  if (!fs.existsSync(gitkeepPath)) {
    try {
      fs.writeFileSync(gitkeepPath, '# This file ensures the directory is tracked by git\n');
      console.log(`✅ Created .gitkeep in: ${dir}`);
    } catch (error) {
      console.error(`❌ Failed to create .gitkeep in ${dir}:`, error.message);
    }
  }
});

console.log('\n🎉 Upload directories setup complete!');
