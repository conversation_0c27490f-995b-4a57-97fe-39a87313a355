{"info": {"_postman_id": "creator-product-management-2024", "name": "Creator Product Management - Enhanced", "description": "Comprehensive test collection for all creator product management endpoints including variations, analytics, and promotions", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{creator_token}}", "type": "string"}]}, "variable": [{"key": "base_url", "value": "http://localhost:3000/api/v1", "type": "string"}, {"key": "creator_token", "value": "", "type": "string"}, {"key": "product_id", "value": "", "type": "string"}, {"key": "variation_id", "value": "", "type": "string"}, {"key": "promotion_id", "value": "", "type": "string"}], "item": [{"name": "Product Information Updates", "item": [{"name": "Update Basic Info", "request": {"method": "PATCH", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "name", "value": "Updated Product Name", "type": "text"}, {"key": "brand", "value": "Updated Brand", "type": "text"}, {"key": "description", "value": "Updated product description with more details", "type": "text"}, {"key": "highlights", "value": "[\"Premium quality\", \"Fast shipping\", \"Eco-friendly\"]", "type": "text"}, {"key": "gender", "value": "Unisex", "type": "text"}, {"key": "basePrice", "value": "89.99", "type": "text"}, {"key": "tags", "value": "[\"trendy\", \"comfortable\", \"stylish\"]", "type": "text"}, {"key": "images", "type": "file", "src": [], "disabled": true}]}, "url": {"raw": "{{base_url}}/creators/products/{{product_id}}/basic-info", "host": ["{{base_url}}"], "path": ["creators", "products", "{{product_id}}", "basic-info"]}}, "response": []}, {"name": "Update Specifications", "request": {"method": "PATCH", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"mainMaterial\": \"100% Cotton\",\n  \"fitType\": \"Regular\",\n  \"pattern\": \"Solid\",\n  \"closure\": \"Button\",\n  \"neckline\": \"Round Neck\",\n  \"sleeveLength\": \"Short Sleeve\"\n}"}, "url": {"raw": "{{base_url}}/creators/products/{{product_id}}/specifications", "host": ["{{base_url}}"], "path": ["creators", "products", "{{product_id}}", "specifications"]}}, "response": []}, {"name": "Update SEO", "request": {"method": "PATCH", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"metaTitle\": \"Premium Cotton T-Shirt - Comfortable & Stylish\",\n  \"metaDescription\": \"Discover our premium cotton t-shirt collection. Perfect for everyday wear with superior comfort and style.\",\n  \"keywords\": [\"cotton t-shirt\", \"premium clothing\", \"comfortable wear\", \"stylish apparel\"]\n}"}, "url": {"raw": "{{base_url}}/creators/products/{{product_id}}/seo", "host": ["{{base_url}}"], "path": ["creators", "products", "{{product_id}}", "seo"]}}, "response": []}]}, {"name": "Product Variations", "item": [{"name": "Get All Variations", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/creators/products/{{product_id}}/variations", "host": ["{{base_url}}"], "path": ["creators", "products", "{{product_id}}", "variations"]}}, "response": []}, {"name": "Add New Variation", "request": {"method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "color", "value": "Navy Blue", "type": "text"}, {"key": "size", "value": "Large", "type": "text"}, {"key": "quantity", "value": "50", "type": "text"}, {"key": "price", "value": "79.99", "type": "text"}, {"key": "sku", "value": "TSH-NB-L-001", "type": "text"}, {"key": "images", "type": "file", "src": [], "disabled": true}]}, "url": {"raw": "{{base_url}}/creators/products/{{product_id}}/variations", "host": ["{{base_url}}"], "path": ["creators", "products", "{{product_id}}", "variations"]}}, "response": []}, {"name": "Update Variation", "request": {"method": "PATCH", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "price", "value": "74.99", "type": "text"}, {"key": "quantity", "value": "45", "type": "text"}, {"key": "salePrice", "value": "64.99", "type": "text"}, {"key": "saleStartDate", "value": "2024-01-15T00:00:00.000Z", "type": "text"}, {"key": "saleEndDate", "value": "2024-01-31T23:59:59.000Z", "type": "text"}, {"key": "images", "type": "file", "src": [], "disabled": true}]}, "url": {"raw": "{{base_url}}/creators/products/{{product_id}}/variations/{{variation_id}}", "host": ["{{base_url}}"], "path": ["creators", "products", "{{product_id}}", "variations", "{{variation_id}}"]}}, "response": []}, {"name": "Delete Variation", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{base_url}}/creators/products/{{product_id}}/variations/{{variation_id}}", "host": ["{{base_url}}"], "path": ["creators", "products", "{{product_id}}", "variations", "{{variation_id}}"]}}, "response": []}]}, {"name": "Product Analytics", "item": [{"name": "Get Product Reviews", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/creators/products/{{product_id}}/reviews?page=1&limit=10&rating=5", "host": ["{{base_url}}"], "path": ["creators", "products", "{{product_id}}", "reviews"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}, {"key": "rating", "value": "5", "description": "Filter by rating (1-5)"}]}}, "response": []}, {"name": "Get Product Sales History", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/creators/products/{{product_id}}/sales?period=month", "host": ["{{base_url}}"], "path": ["creators", "products", "{{product_id}}", "sales"], "query": [{"key": "period", "value": "month", "description": "today, week, month, year, last30days"}]}}, "response": []}, {"name": "Get Product Statistics", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/creators/products/stats?period=month", "host": ["{{base_url}}"], "path": ["creators", "products", "stats"], "query": [{"key": "period", "value": "month", "description": "today, week, month, year, all_time"}]}}, "response": []}]}, {"name": "Product Promotions", "item": [{"name": "Get Product Promotions", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/creators/products/{{product_id}}/promotions", "host": ["{{base_url}}"], "path": ["creators", "products", "{{product_id}}", "promotions"]}}, "response": []}, {"name": "Join <PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"promotionId\": \"{{promotion_id}}\",\n  \"discountValue\": 20,\n  \"discountType\": \"percentage\",\n  \"promoStock\": 100\n}"}, "url": {"raw": "{{base_url}}/creators/products/{{product_id}}/promotions", "host": ["{{base_url}}"], "path": ["creators", "products", "{{product_id}}", "promotions"]}}, "response": []}, {"name": "Update Promotion Participation", "request": {"method": "PATCH", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"discountValue\": 25,\n  \"discountType\": \"percentage\",\n  \"promoStock\": 80\n}"}, "url": {"raw": "{{base_url}}/creators/products/{{product_id}}/promotions/{{promotion_id}}", "host": ["{{base_url}}"], "path": ["creators", "products", "{{product_id}}", "promotions", "{{promotion_id}}"]}}, "response": []}, {"name": "Leave Promotion", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{base_url}}/creators/products/{{product_id}}/promotions/{{promotion_id}}", "host": ["{{base_url}}"], "path": ["creators", "products", "{{product_id}}", "promotions", "{{promotion_id}}"]}}, "response": []}]}]}