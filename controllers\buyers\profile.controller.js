const { Buyer } = require('../../models/user.model');
const AppError = require('../../utils/appError');
const catchAsync = require('../../utils/catchAsync');
const Order = require('../../models/order.model');
const Review = require('../../models/review.model');
const Wishlist = require('../../models/wishlist.model');

/**
 * Get buyer profile
 * @route GET /api/v1/buyers/profile
 * @access Private (Buyer only)
 */
exports.getProfile = catchAsync(async (req, res, next) => {
  const buyer = await Buyer.findById(req.user.id)
    .select('-__v -password -passwordChangedAt -passwordResetToken -passwordResetExpires');

  if (!buyer) {
    return next(new AppError('Buyer not found', 404));
  }

  res.status(200).json({
    status: 'success',
    data: {
      buyer
    }
  });
});

/**
 * Update buyer profile
 * @route PATCH /api/v1/buyers/profile
 * @access Private (Buyer only)
 */
exports.updateProfile = catchAsync(async (req, res, next) => {
  // Create a filtered body to prevent unwanted fields
  const filteredBody = {};
  const allowedFields = ['name', 'email', 'photo', 'phone', 'gender', 'dateOfBirth'];

  Object.keys(req.body).forEach(key => {
    if (allowedFields.includes(key)) {
      filteredBody[key] = req.body[key];
    }
  });

  // Update buyer profile
  const updatedBuyer = await Buyer.findByIdAndUpdate(
    req.user.id,
    filteredBody,
    {
      new: true,
      runValidators: true
    }
  ).select('-__v -password -passwordChangedAt -passwordResetToken -passwordResetExpires');

  res.status(200).json({
    status: 'success',
    data: {
      buyer: updatedBuyer
    }
  });
});

/**
 * Update buyer shipping address
 * @route PATCH /api/v1/buyers/shipping-address
 * @access Private (Buyer only)
 */
exports.updateShippingAddress = catchAsync(async (req, res, next) => {
  // Validate required fields
  const requiredFields = ['addressLine1', 'city', 'state', 'postalCode', 'country', 'phone'];
  for (const field of requiredFields) {
    if (!req.body[field]) {
      return next(new AppError(`Please provide ${field}`, 400));
    }
  }

  // Update buyer shipping address
  const updatedBuyer = await Buyer.findByIdAndUpdate(
    req.user.id,
    { shippingAddress: req.body },
    {
      new: true,
      runValidators: true
    }
  ).select('-__v -password -passwordChangedAt -passwordResetToken -passwordResetExpires');

  res.status(200).json({
    status: 'success',
    data: {
      buyer: updatedBuyer
    }
  });
});

/**
 * Get buyer dashboard statistics
 * @route GET /api/v1/buyers/dashboard
 * @access Private (Buyer only)
 */
exports.getDashboardStats = catchAsync(async (req, res, next) => {
  // Get order statistics
  const orderCount = await Order.countDocuments({ user: req.user.id });
  const deliveredOrderCount = await Order.countDocuments({ user: req.user.id, status: 'delivered' });
  const pendingOrderCount = await Order.countDocuments({ user: req.user.id, status: { $in: ['pending', 'processing', 'shipped'] } });

  // Get total spent
  const totalSpent = await Order.aggregate([
    {
      $match: { user: req.user._id }
    },
    {
      $group: {
        _id: null,
        total: { $sum: '$total' }
      }
    }
  ]);

  // Get review statistics
  const reviewCount = await Review.countDocuments({ user: req.user.id });

  // Get wishlist statistics
  const wishlist = await Wishlist.findOne({ user: req.user.id });
  const wishlistCount = wishlist ? wishlist.products.length + wishlist.bales.length : 0;

  res.status(200).json({
    status: 'success',
    data: {
      orders: {
        total: orderCount,
        delivered: deliveredOrderCount,
        pending: pendingOrderCount
      },
      totalSpent: totalSpent.length > 0 ? totalSpent[0].total : 0,
      reviews: reviewCount,
      wishlist: wishlistCount
    }
  });
});

/**
 * Update buyer password
 * @route PATCH /api/v1/buyers/profile/password
 * @access Private (Buyer only)
 */
exports.updatePassword = catchAsync(async (req, res, next) => {
  // 1) Get the buyer from the collection
  const buyer = await Buyer.findById(req.user.id).select('+password');

  if (!buyer) {
    return next(new AppError('Buyer not found', 404));
  }

  // 2) Check if the current password is correct
  if (!req.body.currentPassword) {
    return next(new AppError('Please provide your current password', 400));
  }

  if (!(await buyer.correctPassword(req.body.currentPassword, buyer.password))) {
    return next(new AppError('Your current password is incorrect', 401));
  }

  // 3) Check if the new password and confirmation match
  if (!req.body.password || !req.body.passwordConfirm) {
    return next(new AppError('Please provide a new password and password confirmation', 400));
  }

  if (req.body.password !== req.body.passwordConfirm) {
    return next(new AppError('Passwords do not match', 400));
  }

  // 4) Update the password
  buyer.password = req.body.password;
  buyer.passwordConfirm = req.body.passwordConfirm;
  buyer.passwordChangedAt = Date.now();

  await buyer.save();

  res.status(200).json({
    status: 'success',
    message: 'Password updated successfully'
  });
});

/**
 * Update buyer preferences
 * @route PATCH /api/v1/buyers/profile/preferences
 * @access Private (Buyer only)
 */
exports.updatePreferences = catchAsync(async (req, res, next) => {
  // Validate request body
  if (!req.body.preferences) {
    return next(new AppError('Please provide preferences to update', 400));
  }

  // Find buyer
  const buyer = await Buyer.findById(req.user.id);

  if (!buyer) {
    return next(new AppError('Buyer not found', 404));
  }

  // Initialize preferences if they don't exist
  if (!buyer.preferences) {
    buyer.preferences = {};
  }

  // Update notification preferences
  if (req.body.preferences.notificationPreferences) {
    if (!buyer.preferences.notificationPreferences) {
      buyer.preferences.notificationPreferences = {};
    }

    // Update new arrivals preference
    if (req.body.preferences.notificationPreferences.newArrivals !== undefined) {
      buyer.preferences.notificationPreferences.newArrivals =
        req.body.preferences.notificationPreferences.newArrivals;
    }

    // Update other notification preferences
    const notificationTypes = ['orderUpdates', 'promotions', 'priceDrops', 'email', 'push'];
    notificationTypes.forEach(type => {
      if (req.body.preferences.notificationPreferences[type] !== undefined) {
        buyer.preferences.notificationPreferences[type] =
          req.body.preferences.notificationPreferences[type];
      }
    });
  }

  // Update preferred categories
  if (req.body.preferences.categories) {
    buyer.preferences.categories = req.body.preferences.categories;
  }

  // Update other preferences
  const preferenceFields = ['sizes', 'colors', 'priceRange'];
  preferenceFields.forEach(field => {
    if (req.body.preferences[field] !== undefined) {
      buyer.preferences[field] = req.body.preferences[field];
    }
  });

  // Save the updated buyer
  await buyer.save({ validateBeforeSave: false });

  // Return the updated buyer
  const updatedBuyer = await Buyer.findById(req.user.id)
    .select('-__v -password -passwordChangedAt -passwordResetToken -passwordResetExpires');

  res.status(200).json({
    status: 'success',
    data: {
      preferences: updatedBuyer.preferences
    }
  });
});
