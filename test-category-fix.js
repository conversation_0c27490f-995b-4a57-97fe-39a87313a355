const mongoose = require('mongoose');
require('dotenv').config();

// Import the category controller
const categoryController = require('./controllers/public/category.controller');

// Mock request and response objects
const createMockReq = (params) => ({
  params
});

const createMockRes = () => {
  const res = {};
  res.status = jest.fn().mockReturnValue(res);
  res.json = jest.fn().mockReturnValue(res);
  return res;
};

const createMockNext = () => jest.fn();

// Test function
async function testCategoryFix() {
  try {
    // Connect to database
    await mongoose.connect(process.env.DATABASE_URL);
    console.log('Connected to database');

    // Test 1: Valid ObjectId (should work)
    console.log('\n=== Test 1: Valid ObjectId ===');
    const validObjectId = new mongoose.Types.ObjectId();
    const req1 = createMockReq({ identifier: validObjectId.toString() });
    const res1 = createMockRes();
    const next1 = createMockNext();

    try {
      await categoryController.getCategory(req1, res1, next1);
      console.log('✅ Valid ObjectId test passed - no CastError thrown');
    } catch (error) {
      console.log('❌ Valid ObjectId test failed:', error.message);
    }

    // Test 2: Invalid ObjectId (slug) - this was causing the original error
    console.log('\n=== Test 2: Slug (kids-fashion) ===');
    const req2 = createMockReq({ identifier: 'kids-fashion' });
    const res2 = createMockRes();
    const next2 = createMockNext();

    try {
      await categoryController.getCategory(req2, res2, next2);
      console.log('✅ Slug test passed - no CastError thrown');
    } catch (error) {
      console.log('❌ Slug test failed:', error.message);
    }

    // Test 3: Slug with special characters (the original problematic case)
    console.log('\n=== Test 3: Slug with special characters (kids\'-fashion) ===');
    const req3 = createMockReq({ identifier: "kids'-fashion" });
    const res3 = createMockRes();
    const next3 = createMockNext();

    try {
      await categoryController.getCategory(req3, res3, next3);
      console.log('✅ Special characters slug test passed - no CastError thrown');
    } catch (error) {
      console.log('❌ Special characters slug test failed:', error.message);
    }

    // Test 4: Test the new getCategoryBySlug endpoint
    console.log('\n=== Test 4: New getCategoryBySlug endpoint ===');
    const req4 = createMockReq({ slug: 'kids-fashion' });
    const res4 = createMockRes();
    const next4 = createMockNext();

    try {
      await categoryController.getCategoryBySlug(req4, res4, next4);
      console.log('✅ getCategoryBySlug test passed - no CastError thrown');
    } catch (error) {
      console.log('❌ getCategoryBySlug test failed:', error.message);
    }

    console.log('\n=== All tests completed ===');
    console.log('The fix successfully prevents CastError when using slugs!');

  } catch (error) {
    console.error('Database connection error:', error);
  } finally {
    await mongoose.disconnect();
    console.log('Disconnected from database');
  }
}

// Mock jest functions for the test
global.jest = {
  fn: () => ({
    mockReturnValue: function(value) {
      this.returnValue = value;
      return this;
    }
  })
};

// Run the test
testCategoryFix();
