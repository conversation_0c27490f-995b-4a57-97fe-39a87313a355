require('dotenv').config();

module.exports = {
  jwtSecret: process.env.JWT_SECRET || 'your-secret-key-for-development',
  jwtExpiresIn: process.env.JWT_EXPIRES_IN || '7d',
  jwtCookieExpiresIn: process.env.JWT_COOKIE_EXPIRES_IN || 7,
  frontend: {
    buyerURL: process.env.FRONTEND_BUYER_URL || 'http://localhost:3000',
    creatorURL: process.env.FRONTEND_CREATOR_URL || 'http://localhost:3001',
    adminURL: process.env.FRONTEND_ADMIN_URL || 'http://localhost:3002'
  },
  google: {
    clientID: process.env.GOOGLE_CLIENT_ID,
    clientSecret: process.env.GOOGLE_CLIENT_SECRET,
    callbackBaseURL: process.env.GOOGLE_CALLBACK_BASE_URL || '/api/v1/auth/google'
  },
  facebook: {
    clientID: process.env.FACEBOOK_CLIENT_ID,
    clientSecret: process.env.FACEBOOK_CLIENT_SECRET,
    callbackBaseURL: process.env.FACEBOOK_CALLBACK_BASE_URL || '/api/v1/auth/facebook',
    profileFields: ['id', 'emails', 'name', 'displayName', 'photos']
  }
};
