const mongoose = require('mongoose');

const feeConfigSchema = new mongoose.Schema(
  {
    // Platform fee configuration
    platformFee: {
      percentage: {
        type: Number,
        required: true,
        default: 5,
        min: 0,
        max: 100
      },
      description: String
    },

    // Processing fee configuration
    processingFee: {
      percentage: {
        type: Number,
        default: 1.65,
        min: 0,
        max: 100
      },
      description: String
    },

    // Active status
    isActive: {
      type: Boolean,
      default: true
    },

    // Effective date range
    effectiveFrom: {
      type: Date,
      default: Date.now
    },
    effectiveTo: Date,

    // Audit fields
    createdBy: {
      type: mongoose.Schema.ObjectId,
      ref: 'User'
    },
    updatedBy: {
      type: mongoose.Schema.ObjectId,
      ref: 'User'
    }
  },
  {
    timestamps: true
  }
);

// Static method to get current active fee configuration
feeConfigSchema.statics.getCurrentConfig = async function() {
  const now = new Date();

  const config = await this.findOne({
    isActive: true,
    effectiveFrom: { $lte: now },
    $or: [
      { effectiveTo: { $gt: now } },
      { effectiveTo: null }
    ]
  }).sort({ effectiveFrom: -1 });

  return config || await this.create({});
};

const FeeConfig = mongoose.model('FeeConfig', feeConfigSchema);

module.exports = FeeConfig;
