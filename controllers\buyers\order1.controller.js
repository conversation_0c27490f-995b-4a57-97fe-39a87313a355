const Order = require('../../models/order.model');
const Product = require('../../models/product.model');
const Bale = require('../../models/bale.model');
const { Creator, Buyer } = require('../../models/user.model');
const AppError = require('../../utils/appError');
const catchAsync = require('../../utils/catchAsync');

/**
 * Create a new order
 * @route POST /api/v1/orders
 * @access Private (Buyer only)
 */
exports.createOrder = catchAsync(async (req, res, next) => {
  // Add user to req.body
  req.body.user = req.user.id;

  // Validate items in the order
  if (!req.body.items || !Array.isArray(req.body.items) || req.body.items.length === 0) {
    return next(new AppError('Order must have at least one item', 400));
  }

  // Calculate total amount and validate items
  let totalAmount = 0;
  for (const item of req.body.items) {
    let product;
    if (item.type === 'product') {
      product = await Product.findById(item.product);
    } else if (item.type === 'bale') {
      product = await Bale.findById(item.bale);
    }

    if (!product) {
      return next(new AppError(`${item.type} not found with ID: ${item.product || item.bale}`, 404));
    }

    // Check if product is in stock
    if (product.stock < item.quantity) {
      return next(new AppError(`${product.name} is out of stock or has insufficient quantity`, 400));
    }

    // Add creator to item
    item.creator = product.creator;

    // Calculate price
    // Check if the item is part of a promotion
    let price = product.discountPrice || product.price;

    // If promotionId is provided, check for promotional price
    if (item.promotionId) {
      const Promotion = require('../../models/promotion.model');
      const promotion = await Promotion.findById(item.promotionId);

      if (promotion && promotion.isValid) {
        // Find the participant (creator) in the promotion
        const participant = promotion.participants.find(
          p => p.creator.toString() === product.creator.toString() && p.status === 'approved'
        );

        if (participant) {
          // Find the product in the participant's products
          const promoProduct = participant.products.find(
            p => p.product.toString() === (item.type === 'product' ? item.product.toString() : item.bale.toString())
          );

          if (promoProduct && promoProduct.promoStock >= item.quantity) {
            // Use promotional price
            price = promoProduct.promoPrice;

            // Update promotional stock
            promoProduct.promoStock -= item.quantity;
            await promotion.save();

            // Add promotion info to item
            item.promotion = {
              id: promotion._id,
              name: promotion.name,
              discountValue: promoProduct.discountValue
            };
          }
        }
      }
    }

    item.price = price;
    totalAmount += price * item.quantity;

    // Reduce stock
    product.stock -= item.quantity;
    await product.save();
  }

  // Add total amount to req.body
  req.body.totalAmount = totalAmount;

  const newOrder = await Order.create(req.body);

  res.status(201).json({
    status: 'success',
    data: {
      order: newOrder
    }
  });
});

/**
 * Get all orders (admin only)
 * @route GET /api/v1/orders
 * @access Private (Admin only)
 */
exports.getAllOrders = catchAsync(async (req, res, next) => {
  // Build query
  const queryObj = { ...req.query };
  const excludedFields = ['page', 'sort', 'limit', 'fields', 'search'];
  excludedFields.forEach(el => delete queryObj[el]);

  // Advanced filtering
  let queryStr = JSON.stringify(queryObj);
  queryStr = queryStr.replace(/\b(gte|gt|lte|lt)\b/g, match => `$${match}`);

  let query = Order.find(JSON.parse(queryStr));

  // Search functionality
  if (req.query.search) {
    const searchRegex = new RegExp(req.query.search, 'i');
    query = query.find({
      $or: [
        { 'shippingAddress.name': searchRegex },
        { 'shippingAddress.email': searchRegex },
        { 'shippingAddress.phone': searchRegex },
        { notes: searchRegex }
      ]
    });
  }

  // Filter by date range
  if (req.query.startDate || req.query.endDate) {
    const dateFilter = {};
    if (req.query.startDate) dateFilter.$gte = new Date(req.query.startDate);
    if (req.query.endDate) {
      const endDate = new Date(req.query.endDate);
      endDate.setHours(23, 59, 59, 999); // End of the day
      dateFilter.$lte = endDate;
    }
    query = query.find({ createdAt: dateFilter });
  }

  // Filter by total amount range
  if (req.query.minAmount || req.query.maxAmount) {
    const amountFilter = {};
    if (req.query.minAmount) amountFilter.$gte = req.query.minAmount;
    if (req.query.maxAmount) amountFilter.$lte = req.query.maxAmount;
    query = query.find({ totalAmount: amountFilter });
  }

  // Count total before applying pagination
  const total = await Order.countDocuments(query);

  // Sorting
  if (req.query.sort) {
    const sortBy = req.query.sort.split(',').join(' ');
    query = query.sort(sortBy);
  } else {
    query = query.sort('-createdAt');
  }

  // Field limiting
  if (req.query.fields) {
    const fields = req.query.fields.split(',').join(' ');
    query = query.select(fields);
  } else {
    query = query.select('-__v');
  }

  // Pagination
  const page = req.query.page * 1 || 1;
  const limit = req.query.limit * 1 || 20;
  const skip = (page - 1) * limit;

  query = query.skip(skip).limit(limit);

  // Execute query
  const orders = await query
    .populate({
      path: 'user',
      select: 'name email'
    })
    .populate({
      path: 'items.creator',
      select: 'name email storeInfo'
    });

  res.status(200).json({
    status: 'success',
    results: orders.length,
    total,
    page,
    limit,
    data: {
      orders
    }
  });
});

/**
 * Get order by ID
 * @route GET /api/v1/orders/:id
 * @access Private (Buyer, Creator of items in order, Admin)
 */
exports.getOrder = catchAsync(async (req, res, next) => {
  const order = await Order.findById(req.params.id)
    .populate({
      path: 'user',
      select: 'name email'
    })
    .populate({
      path: 'items.creator',
      select: 'name email storeInfo'
    });

  if (!order) {
    return next(new AppError('No order found with that ID', 404));
  }

  // Check if user is authorized to view this order
  const isAdmin = req.user.role === 'admin';
  const isBuyer = req.user.role === 'user' && order.user._id.toString() === req.user.id;
  const isCreator = req.user.role === 'creator' && order.items.some(
    item => item.creator._id.toString() === req.user.id
  );

  if (!isAdmin && !isBuyer && !isCreator) {
    return next(
      new AppError('You do not have permission to view this order', 403)
    );
  }

  res.status(200).json({
    status: 'success',
    data: {
      order
    }
  });
});

/**
 * Update order status
 * @route PATCH /api/v1/orders/:id/status
 * @access Private (Creator of items in order, Admin)
 */
exports.updateOrderStatus = catchAsync(async (req, res, next) => {
  const order = await Order.findById(req.params.id);

  if (!order) {
    return next(new AppError('No order found with that ID', 404));
  }

  // Check if user is authorized to update this order
  const isAdmin = req.user.role === 'admin';
  const isCreator = req.user.role === 'creator' && order.items.some(
    item => item.creator.toString() === req.user.id
  );

  if (!isAdmin && !isCreator) {
    return next(
      new AppError('You do not have permission to update this order', 403)
    );
  }

  // Validate request body
  if (!req.body || !req.body.status) {
    return next(new AppError('Please provide a status to update', 400));
  }

  // Validate status value
  const validStatuses = ['pending', 'processing', 'shipped', 'delivered', 'cancelled'];
  if (!validStatuses.includes(req.body.status)) {
    return next(new AppError(`Status must be one of: ${validStatuses.join(', ')}`, 400));
  }

  // If creator, only update status of their items
  if (isCreator && !isAdmin) {
    const creatorItems = order.items.filter(
      item => item.creator.toString() === req.user.id
    );

    if (creatorItems.length === 0) {
      return next(new AppError('You do not have any items in this order', 404));
    }

    for (const item of creatorItems) {
      item.status = req.body.status;
    }
  } else {
    // Admin can update the entire order status
    order.status = req.body.status;
  }

  await order.save();

  res.status(200).json({
    status: 'success',
    data: {
      order
    }
  });
});

/**
 * Get my orders (for logged in buyer)
 * @route GET /api/v1/orders/my-orders
 * @access Private (Buyer only)
 */
exports.getMyOrders = catchAsync(async (req, res, next) => {
  const orders = await Order.find({ user: req.user.id })
    .populate({
      path: 'items.creator',
      select: 'name email storeInfo'
    });

  res.status(200).json({
    status: 'success',
    results: orders.length,
    data: {
      orders
    }
  });
});

/**
 * Get orders for creator
 * @route GET /api/v1/orders/creator-orders
 * @access Private (Creator only)
 */
exports.getCreatorOrders = catchAsync(async (req, res, next) => {
  // Build base query - find orders where the creator has items
  let query = {
    'items.creator': req.user.id
  };

  // Filter by status if specified
  if (req.query.status) {
    query.status = req.query.status;
  }

  // Filter by date range
  if (req.query.startDate || req.query.endDate) {
    query.createdAt = {};
    if (req.query.startDate) {
      query.createdAt.$gte = new Date(req.query.startDate);
    }
    if (req.query.endDate) {
      const endDate = new Date(req.query.endDate);
      endDate.setHours(23, 59, 59, 999); // End of the day
      query.createdAt.$lte = endDate;
    }
  }

  // Count total before applying pagination
  const total = await Order.countDocuments(query);

  // Pagination
  const page = req.query.page * 1 || 1;
  const limit = req.query.limit * 1 || 20;
  const skip = (page - 1) * limit;

  // Sorting
  let sortBy = '-createdAt';
  if (req.query.sort) {
    sortBy = req.query.sort.split(',').join(' ');
  }

  // Execute query
  const orders = await Order.find(query)
    .sort(sortBy)
    .skip(skip)
    .limit(limit)
    .populate({
      path: 'user',
      select: 'name email'
    });

  // Filter items to only include those from this creator
  const filteredOrders = orders.map(order => {
    const filteredItems = order.items.filter(
      item => item.creator.toString() === req.user.id
    );

    // Calculate creator's portion of the order
    const creatorTotal = filteredItems.reduce(
      (total, item) => total + (item.price * item.quantity),
      0
    );

    return {
      _id: order._id,
      user: order.user,
      status: order.status,
      totalAmount: order.totalAmount,
      creatorTotal,
      items: filteredItems,
      shippingAddress: order.shippingAddress,
      paymentInfo: order.paymentInfo,
      createdAt: order.createdAt,
      updatedAt: order.updatedAt
    };
  });

  res.status(200).json({
    status: 'success',
    results: filteredOrders.length,
    total,
    page,
    limit,
    data: {
      orders: filteredOrders
    }
  });
});

/**
 * Get order statistics for creator
 * @route GET /api/v1/orders/creator-stats
 * @access Private (Creator only)
 */
exports.getCreatorOrderStats = catchAsync(async (req, res, next) => {
  const stats = await Order.aggregate([
    {
      $unwind: '$items'
    },
    {
      $match: {
        'items.creator': req.user._id
      }
    },
    {
      $group: {
        _id: '$status',
        count: { $sum: 1 },
        totalSales: { $sum: '$items.quantity' },
        totalRevenue: { $sum: { $multiply: ['$items.price', '$items.quantity'] } }
      }
    }
  ]);

  // Format stats into a more usable structure
  const formattedStats = {
    pending: { count: 0, totalSales: 0, totalRevenue: 0 },
    processing: { count: 0, totalSales: 0, totalRevenue: 0 },
    shipped: { count: 0, totalSales: 0, totalRevenue: 0 },
    delivered: { count: 0, totalSales: 0, totalRevenue: 0 },
    cancelled: { count: 0, totalSales: 0, totalRevenue: 0 }
  };

  stats.forEach(stat => {
    if (formattedStats[stat._id]) {
      formattedStats[stat._id] = {
        count: stat.count,
        totalSales: stat.totalSales,
        totalRevenue: stat.totalRevenue
      };
    }
  });

  res.status(200).json({
    status: 'success',
    data: {
      stats: formattedStats
    }
  });
});
