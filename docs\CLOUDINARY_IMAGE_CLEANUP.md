# Cloudinary Image Cleanup System

## Overview
This document outlines the comprehensive image cleanup system that automatically deletes images from Cloudinary when they are unlinked in the backend, preventing storage bloat and reducing costs.

## Features

### 1. Automatic Image Cleanup
- **Model-Level Middleware**: Automatically tracks and cleans up images when documents are updated or deleted
- **Smart Detection**: Compares old vs new image arrays to identify removed images
- **Batch Processing**: Efficiently handles multiple image deletions
- **Error Resilience**: Cleanup failures don't affect main operations

### 2. Manual Cleanup Tools
- **Orphaned Image Detection**: Finds images in Cloudinary not referenced in database
- **Admin API Endpoints**: Web interface for manual cleanup operations
- **Command Line Scripts**: Batch processing for large cleanup operations
- **Dry Run Mode**: Preview changes before executing deletions

### 3. Storage Management
- **Usage Statistics**: Monitor Cloudinary storage and bandwidth usage
- **Recent Uploads**: Track newly uploaded images
- **Folder Organization**: Organized cleanup by image categories

## Implementation Details

### Automatic Cleanup Middleware

#### Product Model
```javascript
// Tracks changes to product images and variation images
productSchema.pre('save', createImageCleanupMiddleware(['images', 'variations.images']));
productSchema.post('save', executeImageCleanup);

// Cleanup all images when product is deleted
productSchema.pre('remove', async function(next) {
  const result = await cleanupEntityImages(this, ['images', 'variations.images']);
  console.log(`Cleaned up ${result.deleted} images for deleted product ${this._id}`);
  next();
});
```

#### Bale Model
```javascript
// Tracks changes to bale images
baleSchema.pre('save', createImageCleanupMiddleware(['images']));
baleSchema.post('save', executeImageCleanup);

// Cleanup all images when bale is deleted
baleSchema.pre('remove', async function(next) {
  const result = await cleanupEntityImages(this, ['images']);
  console.log(`Cleaned up ${result.deleted} images for deleted bale ${this._id}`);
  next();
});
```

#### Creator Model
```javascript
// Tracks changes to creator profile images, shop media, and verification documents
creatorSchema.pre('save', createImageCleanupMiddleware([
  'photo', 
  'shopInfo.logo', 
  'shopInfo.banner', 
  'businessInfo.verificationDocuments'
]));
creatorSchema.post('save', executeImageCleanup);
```

### Core Utilities (`utils/cloudinaryCleanup.js`)

#### Key Functions:

1. **`deleteSingleImage(imageUrl)`**
   - Deletes a single image from Cloudinary
   - Extracts public_id from URL
   - Returns success/failure status

2. **`deleteMultipleImages(imageUrls)`**
   - Batch deletes multiple images
   - Processes in batches of 5 for performance
   - Returns detailed results for each deletion

3. **`cleanupRemovedImages(oldImages, newImages)`**
   - Compares old vs new image arrays
   - Identifies and deletes removed images
   - Used by model middleware

4. **`cleanupEntityImages(entity, imageFields)`**
   - Deletes all images associated with an entity
   - Used when entire documents are deleted

### Admin API Endpoints

#### Available Endpoints:

1. **`GET /api/v1/admin/images/scan`**
   - Scans for orphaned images
   - Returns list of images not referenced in database

2. **`DELETE /api/v1/admin/images/cleanup?dryRun=true`**
   - Cleans up orphaned images
   - Supports dry run mode for preview

3. **`DELETE /api/v1/admin/images/delete`**
   - Deletes specific images by URLs
   - Body: `{ "imageUrls": ["url1", "url2"] }`

4. **`DELETE /api/v1/admin/images/delete-single`**
   - Deletes a single image by URL
   - Body: `{ "imageUrl": "url" }`

5. **`GET /api/v1/admin/images/stats`**
   - Returns Cloudinary usage statistics
   - Storage, bandwidth, and folder statistics

6. **`GET /api/v1/admin/images/recent?limit=20`**
   - Returns recent uploads
   - Useful for monitoring new images

### Command Line Scripts

#### Usage:
```bash
# Scan for orphaned images
node scripts/cleanupOrphanedImages.js scan

# Dry run cleanup (preview only)
node scripts/cleanupOrphanedImages.js cleanup --dry-run

# Execute cleanup
node scripts/cleanupOrphanedImages.js cleanup

# Find duplicate image references
node scripts/cleanupOrphanedImages.js duplicates
```

## Usage Examples

### Frontend Integration

#### Admin Dashboard - Image Management
```javascript
// Scan for orphaned images
const scanOrphanedImages = async () => {
  const response = await fetch('/api/v1/admin/images/scan', {
    headers: { 'Authorization': `Bearer ${adminToken}` }
  });
  const data = await response.json();
  
  if (data.status === 'success') {
    console.log(`Found ${data.data.orphanedCount} orphaned images`);
    setOrphanedImages(data.data.orphanedImages);
  }
};

// Cleanup orphaned images with confirmation
const cleanupOrphanedImages = async (dryRun = false) => {
  const response = await fetch(`/api/v1/admin/images/cleanup?dryRun=${dryRun}`, {
    method: 'DELETE',
    headers: { 'Authorization': `Bearer ${adminToken}` }
  });
  const data = await response.json();
  
  if (data.status === 'success') {
    if (dryRun) {
      console.log(`Would delete ${data.data.wouldDelete} images`);
    } else {
      console.log(`Deleted ${data.data.deleted} images`);
    }
  }
};

// Get storage statistics
const getStorageStats = async () => {
  const response = await fetch('/api/v1/admin/images/stats', {
    headers: { 'Authorization': `Bearer ${adminToken}` }
  });
  const data = await response.json();
  
  if (data.status === 'success') {
    setStorageStats(data.data);
  }
};
```

### Automatic Cleanup in Action

#### Product Update Example:
```javascript
// When a product is updated and images are removed
const product = await Product.findById(productId);
product.images = ['new_image1.jpg', 'new_image2.jpg']; // Removed old_image.jpg

await product.save(); 
// Middleware automatically detects that old_image.jpg was removed
// and deletes it from Cloudinary
```

#### Product Deletion Example:
```javascript
// When a product is deleted
await Product.findByIdAndDelete(productId);
// Pre-remove middleware automatically deletes all associated images
// from Cloudinary before removing the document
```

## Benefits

### 1. Cost Optimization
- **Reduced Storage Costs**: Eliminates orphaned images
- **Bandwidth Savings**: Fewer unnecessary images to serve
- **Automatic Management**: No manual intervention required

### 2. Performance Improvements
- **Faster Cloudinary Operations**: Fewer images to manage
- **Cleaner Storage**: Organized and relevant images only
- **Reduced API Calls**: Efficient batch processing

### 3. Maintenance Benefits
- **Automated Cleanup**: Set-and-forget operation
- **Audit Trail**: Detailed logging of cleanup operations
- **Error Handling**: Graceful failure handling

### 4. Admin Control
- **Manual Override**: Admin can manually trigger cleanup
- **Preview Mode**: Dry run before actual deletion
- **Monitoring**: Real-time storage statistics

## Configuration

### Environment Variables
```env
CLOUDINARY_CLOUD_NAME=your_cloud_name
CLOUDINARY_API_KEY=your_api_key
CLOUDINARY_API_SECRET=your_api_secret
```

### Cleanup Settings
```javascript
// Batch size for image deletions (default: 5)
const CLEANUP_BATCH_SIZE = 5;

// Maximum images per manual deletion (default: 50)
const MAX_MANUAL_DELETE = 50;

// Folders to scan for orphaned images
const SCAN_FOLDERS = [
  'everyfash/products',
  'everyfash/bales',
  'everyfash/profiles',
  'everyfash/shop',
  'everyfash/verification'
];
```

## Monitoring and Logging

### Automatic Logging
```javascript
// Successful cleanup
console.log(`Cleaned up 5 images from field 'images' for Product 64f7b1234567890123456789`);

// Failed cleanup (non-blocking)
console.warn(`Failed to delete 1 images from Cloudinary: [error details]`);

// Entity deletion
console.log(`Cleaned up 12 images for deleted product 64f7b1234567890123456789`);
```

### Error Handling
- Cleanup failures don't affect main operations
- Detailed error logging for troubleshooting
- Graceful degradation when Cloudinary is unavailable

## Best Practices

1. **Regular Monitoring**: Check storage stats weekly
2. **Scheduled Cleanup**: Run orphaned image scans monthly
3. **Dry Run First**: Always preview before bulk deletions
4. **Backup Strategy**: Consider backup before major cleanups
5. **Performance Testing**: Monitor impact on save operations

## Troubleshooting

### Common Issues:

1. **Cleanup Not Working**
   - Check Cloudinary credentials
   - Verify middleware is properly attached
   - Check console logs for errors

2. **Performance Impact**
   - Reduce batch size if saves are slow
   - Consider async cleanup for large operations

3. **False Positives in Orphan Detection**
   - Verify all image fields are included in scan
   - Check for images referenced in other collections

The image cleanup system provides comprehensive, automated management of Cloudinary images while maintaining performance and reliability.
