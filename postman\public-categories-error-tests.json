{"info": {"_postman_id": "public-categories-error-tests-2024", "name": "Public Categories API - Error Tests", "description": "Error scenarios and edge cases for public category endpoints", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "variable": [{"key": "base_url", "value": "http://localhost:3000/api/v1", "type": "string"}, {"key": "invalid_category_id", "value": "507f1f77bcf86cd799439011", "type": "string"}, {"key": "invalid_slug", "value": "non-existent-category-slug", "type": "string"}], "event": [{"listen": "test", "script": {"exec": ["// Global test script for error scenarios", "pm.test('Response time is less than 2000ms', function () {", "    pm.expect(pm.response.responseTime).to.be.below(2000);", "});", "", "pm.test('Response has correct content type', function () {", "    pm.expect(pm.response.headers.get('Content-Type')).to.include('application/json');", "});"], "type": "text/javascript"}}], "item": [{"name": "<PERSON><PERSON><PERSON>", "item": [{"name": "Get Category - Invalid ID", "event": [{"listen": "test", "script": {"exec": ["pm.test('Returns 404 for invalid category ID', function () {", "    pm.response.to.have.status(404);", "    const response = pm.response.json();", "    pm.expect(response.status).to.eql('error');", "    pm.expect(response.message).to.include('No category found');", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/categories/{{invalid_category_id}}", "host": ["{{base_url}}"], "path": ["categories", "{{invalid_category_id}}"]}}, "response": []}, {"name": "Get Category - Invalid Slug", "event": [{"listen": "test", "script": {"exec": ["pm.test('Returns 404 for invalid category slug', function () {", "    pm.response.to.have.status(404);", "    const response = pm.response.json();", "    pm.expect(response.status).to.eql('error');", "    pm.expect(response.message).to.include('No category found');", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/categories/{{invalid_slug}}", "host": ["{{base_url}}"], "path": ["categories", "{{invalid_slug}}"]}}, "response": []}, {"name": "Search Categories - Missing Query", "event": [{"listen": "test", "script": {"exec": ["pm.test('Returns 400 for missing search query', function () {", "    pm.response.to.have.status(400);", "    const response = pm.response.json();", "    pm.expect(response.status).to.eql('error');", "    pm.expect(response.message).to.include('Please provide a search query');", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/categories/search", "host": ["{{base_url}}"], "path": ["categories", "search"]}}, "response": []}, {"name": "Search Categories - Empty Query", "event": [{"listen": "test", "script": {"exec": ["pm.test('Returns 400 for empty search query', function () {", "    pm.response.to.have.status(400);", "    const response = pm.response.json();", "    pm.expect(response.status).to.eql('error');", "    pm.expect(response.message).to.include('Please provide a search query');", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/categories/search?q=", "host": ["{{base_url}}"], "path": ["categories", "search"], "query": [{"key": "q", "value": ""}]}}, "response": []}]}, {"name": "Edge Cases", "item": [{"name": "Get Categories - Very High Limit", "event": [{"listen": "test", "script": {"exec": ["pm.test('Handles high limit gracefully', function () {", "    pm.response.to.have.status(200);", "    const response = pm.response.json();", "    pm.expect(response.status).to.eql('success');", "    pm.expect(response.data.categories).to.be.an('array');", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/categories/featured?limit=9999", "host": ["{{base_url}}"], "path": ["categories", "featured"], "query": [{"key": "limit", "value": "9999"}]}}, "response": []}, {"name": "Search Categories - Special Characters", "event": [{"listen": "test", "script": {"exec": ["pm.test('<PERSON>les special characters in search', function () {", "    pm.response.to.have.status(200);", "    const response = pm.response.json();", "    pm.expect(response.status).to.eql('success');", "    pm.expect(response.data.categories).to.be.an('array');", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/categories/search?q=@#$%^&*()", "host": ["{{base_url}}"], "path": ["categories", "search"], "query": [{"key": "q", "value": "@#$%^&*()"}]}}, "response": []}, {"name": "Get Categories - Invalid Boolean Parameters", "event": [{"listen": "test", "script": {"exec": ["pm.test('<PERSON>les invalid boolean parameters', function () {", "    pm.response.to.have.status(200);", "    const response = pm.response.json();", "    pm.expect(response.status).to.eql('success');", "    pm.expect(response.data.categories).to.be.an('array');", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/categories?featured=invalid&withProducts=maybe&withBales=yes", "host": ["{{base_url}}"], "path": ["categories"], "query": [{"key": "featured", "value": "invalid"}, {"key": "withProducts", "value": "maybe"}, {"key": "with<PERSON><PERSON>", "value": "yes"}]}}, "response": []}]}]}