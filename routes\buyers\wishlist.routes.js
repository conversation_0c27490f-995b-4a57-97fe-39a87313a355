const express = require('express');
const wishlistController = require('../../controllers/buyers/wishlist.controller');
const authMiddleware = require('../../middleware/auth.middleware');

const router = express.Router();



// Wishlist routes
router.get('/', wishlistController.getWishlist);
router.delete('/', wishlistController.clearWishlist);
router.get('/count', wishlistController.getWishlistCount);

// Wishlist items
router.patch('/products', wishlistController.addProductToWishlist);
router.patch('/bales', wishlistController.addBaleToWishlist);
router.delete('/products/:id', wishlistController.removeProductFromWishlist);
router.delete('/bales/:id', wishlistController.removeBaleFromWishlist);
router.patch('/products/:id', wishlistController.updateProductQuantity);
router.patch('/bales/:id', wishlistController.updateBaleQuantity);
router.post('/move-to-cart', wishlistController.moveToCart);
router.post('/move-all-to-cart', wishlistController.moveAllToCart);

module.exports = router;


