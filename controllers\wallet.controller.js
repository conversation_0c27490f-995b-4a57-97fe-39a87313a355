const WalletService = require('../services/wallet.service');
const AppError = require('../utils/appError');
const catchAsync = require('../utils/catchAsync');

/**
 * Get wallet balance
 * @route GET /api/v1/wallet/balance
 * @access Private
 */
exports.getWalletBalance = catchAsync(async (req, res, next) => {
  // Determine wallet type based on user role
  let walletType;
  if (req.user.role === 'creator') {
    walletType = 'creator';
  } else if (req.user.role === 'buyer') {
    walletType = 'buyer';
  } else if (req.user.role === 'admin') {
    walletType = req.query.type || 'platform';
  } else {
    return next(new AppError('Invalid user role', 400));
  }

  // Get wallet balance
  const balance = await WalletService.getWalletBalance(
    walletType === 'platform' ? null : req.user.id,
    walletType
  );

  res.status(200).json({
    status: 'success',
    data: {
      balance
    }
  });
});

/**
 * Get wallet transactions
 * @route GET /api/v1/wallet/transactions
 * @access Private
 */
exports.getWalletTransactions = catchAsync(async (req, res, next) => {
  // Determine wallet type based on user role
  let walletType;
  if (req.user.role === 'creator') {
    walletType = 'creator';
  } else if (req.user.role === 'buyer') {
    walletType = 'buyer';
  } else if (req.user.role === 'admin') {
    walletType = req.query.type || 'platform';
  } else {
    return next(new AppError('Invalid user role', 400));
  }

  // Parse query parameters
  const options = {
    limit: req.query.limit ? parseInt(req.query.limit) : 10,
    skip: req.query.page ? (parseInt(req.query.page) - 1) * (req.query.limit ? parseInt(req.query.limit) : 10) : 0,
    type: req.query.type,
    status: req.query.status,
    startDate: req.query.startDate,
    endDate: req.query.endDate
  };

  // Get wallet transactions
  const transactions = await WalletService.getWalletTransactions(
    walletType === 'platform' ? null : req.user.id,
    walletType,
    options
  );

  res.status(200).json({
    status: 'success',
    results: transactions.length,
    data: {
      transactions
    }
  });
});

/**
 * Get wallet transaction summary
 * @route GET /api/v1/wallet/summary
 * @access Private
 */
exports.getWalletTransactionSummary = catchAsync(async (req, res, next) => {
  // Determine wallet type based on user role
  let walletType;
  if (req.user.role === 'creator') {
    walletType = 'creator';
  } else if (req.user.role === 'buyer') {
    walletType = 'buyer';
  } else if (req.user.role === 'admin') {
    walletType = req.query.type || 'platform';
  } else {
    return next(new AppError('Invalid user role', 400));
  }

  // Get wallet transaction summary
  const summary = await WalletService.getWalletTransactionSummary(
    walletType === 'platform' ? null : req.user.id,
    walletType
  );

  res.status(200).json({
    status: 'success',
    data: {
      summary
    }
  });
});

/**
 * Process order transactions (Admin only)
 * @route POST /api/v1/wallet/process-order
 * @access Private (Admin only)
 */
exports.processOrderTransactions = catchAsync(async (req, res, next) => {
  // Check if order ID is provided
  if (!req.body.orderId) {
    return next(new AppError('Order ID is required', 400));
  }

  // Process order transactions
  const result = await WalletService.processOrderTransactions(req.body.orderId);

  res.status(200).json({
    status: 'success',
    data: {
      result
    }
  });
});

/**
 * Process payout (Admin only)
 * @route POST /api/v1/wallet/process-payout
 * @access Private (Admin only)
 */
exports.processPayout = catchAsync(async (req, res, next) => {
  // Check if payout ID is provided
  if (!req.body.payoutId) {
    return next(new AppError('Payout ID is required', 400));
  }

  // Process payout
  const result = await WalletService.processPayout(req.body.payoutId);

  res.status(200).json({
    status: 'success',
    data: {
      result
    }
  });
});

/**
 * Process refund (Admin only)
 * @route POST /api/v1/wallet/process-refund
 * @access Private (Admin only)
 */
exports.processRefund = catchAsync(async (req, res, next) => {
  // Check if order ID and refund data are provided
  if (!req.body.orderId) {
    return next(new AppError('Order ID is required', 400));
  }

  if (!req.body.refundData) {
    return next(new AppError('Refund data is required', 400));
  }

  // Process refund
  const result = await WalletService.processRefund(req.body.orderId, req.body.refundData);

  res.status(200).json({
    status: 'success',
    data: {
      result
    }
  });
});

/**
 * Add manual adjustment (Admin only)
 * @route POST /api/v1/wallet/adjustment
 * @access Private (Admin only)
 */
exports.addManualAdjustment = catchAsync(async (req, res, next) => {
  // Check if user ID, wallet type, and adjustment data are provided
  if (!req.body.userId && req.body.walletType !== 'platform') {
    return next(new AppError('User ID is required for non-platform wallets', 400));
  }

  if (!req.body.walletType) {
    return next(new AppError('Wallet type is required', 400));
  }

  if (!req.body.adjustmentData) {
    return next(new AppError('Adjustment data is required', 400));
  }

  // Add manual adjustment
  const wallet = await WalletService.addManualAdjustment(
    req.body.walletType === 'platform' ? null : req.body.userId,
    req.body.walletType,
    req.body.adjustmentData,
    req.user.id
  );

  res.status(200).json({
    status: 'success',
    data: {
      wallet
    }
  });
});

/**
 * Sync wallet balance (Admin only)
 * @route POST /api/v1/wallet/sync
 * @access Private (Admin only)
 */
exports.syncWalletBalance = catchAsync(async (req, res, next) => {
  // Check if user ID and wallet type are provided
  if (!req.body.userId && req.body.walletType !== 'platform') {
    return next(new AppError('User ID is required for non-platform wallets', 400));
  }

  if (!req.body.walletType) {
    return next(new AppError('Wallet type is required', 400));
  }

  // Sync wallet balance
  const wallet = await WalletService.syncWalletBalance(
    req.body.walletType === 'platform' ? null : req.body.userId,
    req.body.walletType
  );

  res.status(200).json({
    status: 'success',
    data: {
      wallet
    }
  });
});

/**
 * Calculate creator available balance
 * @route GET /api/v1/wallet/creator-balance/:creatorId
 * @access Private (Admin only)
 */
exports.calculateCreatorAvailableBalance = catchAsync(async (req, res, next) => {
  // Check if creator ID is provided
  if (!req.params.creatorId) {
    return next(new AppError('Creator ID is required', 400));
  }

  // Calculate creator available balance
  const balance = await WalletService.calculateCreatorAvailableBalance(req.params.creatorId);

  res.status(200).json({
    status: 'success',
    data: {
      balance
    }
  });
});
