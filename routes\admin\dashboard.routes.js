const express = require('express');
const dashboardController = require('../../controllers/admin/dashboard.controller');
const authMiddleware = require('../../middleware/auth.middleware');

const router = express.Router();

// Protect all routes
router.use(authMiddleware.protect);
router.use(authMiddleware.restrictTo('admin'));

// Dashboard routes
router.get('/', dashboardController.getDashboardStats);

module.exports = router;
