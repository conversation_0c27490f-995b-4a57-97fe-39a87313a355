{"info": {"_postman_id": "a1b2c3d4-e5f6-7890-abcd-ef1234567890", "name": "Flashy Admin - Product Routes", "description": "A collection for testing the admin product and bale routes of the Flashy API", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Products", "item": [{"name": "Get All Products", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{ADMIN_AUTH_TOKEN}}", "type": "text"}], "url": {"raw": "{{BASE_URL}}/api/v1/admin/products?page=1&limit=10&sort=-createdAt", "host": ["{{BASE_URL}}"], "path": ["api", "v1", "admin", "products"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}, {"key": "sort", "value": "-createdAt"}, {"key": "status", "value": "pending", "disabled": true}, {"key": "creator", "value": "{{creatorId}}", "disabled": true}, {"key": "search", "value": "shirt", "disabled": true}, {"key": "minPrice", "value": "10", "disabled": true}, {"key": "maxPrice", "value": "100", "disabled": true}, {"key": "category", "value": "{{categoryId}}", "disabled": true}]}, "description": "Get all products with pagination, sorting, and filtering options"}, "response": []}, {"name": "Get Product Stats", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{ADMIN_AUTH_TOKEN}}", "type": "text"}], "url": {"raw": "{{BASE_URL}}/api/v1/admin/products/stats", "host": ["{{BASE_URL}}"], "path": ["api", "v1", "admin", "products", "stats"]}, "description": "Get product statistics including counts by status, category, and price ranges"}, "response": []}, {"name": "Get Product by ID", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{ADMIN_AUTH_TOKEN}}", "type": "text"}], "url": {"raw": "{{BASE_URL}}/api/v1/admin/products/{{productId}}", "host": ["{{BASE_URL}}"], "path": ["api", "v1", "admin", "products", "{{productId}}"]}, "description": "Get a specific product by ID with all details"}, "response": []}, {"name": "Update Product", "request": {"method": "PATCH", "header": [{"key": "Authorization", "value": "Bearer {{ADMIN_AUTH_TOKEN}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"Updated Product Name\",\n    \"description\": \"Updated product description with more details\",\n    \"highlights\": [\"Updated highlight 1\", \"Updated highlight 2\"],\n    \"basePrice\": 59.99,\n    \"discountPrice\": 49.99,\n    \"discountStartDate\": \"2023-08-01T00:00:00.000Z\",\n    \"discountEndDate\": \"2023-09-30T23:59:59.999Z\",\n    \"category\": \"{{categoryId}}\",\n    \"relatedCategories\": [\"{{relatedCategoryId1}}\", \"{{relatedCategoryId2}}\"],\n    \"specifications\": {\n        \"mainMaterial\": \"Cotton\",\n        \"weight\": \"0.5kg\",\n        \"dimensions\": \"30cm x 20cm x 5cm\"\n    },\n    \"variations\": [\n        {\n            \"color\": \"Red\",\n            \"sizes\": [\n                {\n                    \"size\": \"S\",\n                    \"quantity\": 20,\n                    \"price\": 59.99\n                },\n                {\n                    \"size\": \"M\",\n                    \"quantity\": 30,\n                    \"price\": 59.99\n                }\n            ]\n        },\n        {\n            \"color\": \"Blue\",\n            \"sizes\": [\n                {\n                    \"size\": \"S\",\n                    \"quantity\": 15,\n                    \"price\": 59.99\n                },\n                {\n                    \"size\": \"M\",\n                    \"quantity\": 25,\n                    \"price\": 59.99\n                }\n            ]\n        }\n    ],\n    \"tags\": [\"updated\", \"fashion\", \"apparel\"],\n    \"brand\": \"Updated Brand\",\n    \"material\": \"Updated Material\",\n    \"style\": \"Updated Style\",\n    \"fitType\": \"Updated Fit\",\n    \"gender\": \"unisex\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BASE_URL}}/api/v1/admin/products/{{productId}}", "host": ["{{BASE_URL}}"], "path": ["api", "v1", "admin", "products", "{{productId}}"]}, "description": "Update a product's details"}, "response": []}, {"name": "Update Product Status", "request": {"method": "PATCH", "header": [{"key": "Authorization", "value": "Bearer {{ADMIN_AUTH_TOKEN}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"status\": \"approved\",\n    \"statusNote\": \"Product meets all requirements and has been approved.\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BASE_URL}}/api/v1/admin/products/{{productId}}/status", "host": ["{{BASE_URL}}"], "path": ["api", "v1", "admin", "products", "{{productId}}", "status"]}, "description": "Update a product's status (pending, approved, rejected, inactive)"}, "response": []}, {"name": "Delete Product", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{ADMIN_AUTH_TOKEN}}", "type": "text"}], "url": {"raw": "{{BASE_URL}}/api/v1/admin/products/{{productId}}", "host": ["{{BASE_URL}}"], "path": ["api", "v1", "admin", "products", "{{productId}}"]}, "description": "Delete a product"}, "response": []}, {"name": "Get Product Reviews", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{ADMIN_AUTH_TOKEN}}", "type": "text"}], "url": {"raw": "{{BASE_URL}}/api/v1/admin/products/{{productId}}/reviews?page=1&limit=10&sort=-createdAt", "host": ["{{BASE_URL}}"], "path": ["api", "v1", "admin", "products", "{{productId}}", "reviews"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}, {"key": "sort", "value": "-createdAt"}, {"key": "rating", "value": "5", "disabled": true}, {"key": "hidden", "value": "false", "disabled": true}]}, "description": "Get all reviews for a specific product"}, "response": []}, {"name": "Get Creator Products", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{ADMIN_AUTH_TOKEN}}", "type": "text"}], "url": {"raw": "{{BASE_URL}}/api/v1/admin/creators/{{creatorId}}/products?page=1&limit=10&sort=-createdAt", "host": ["{{BASE_URL}}"], "path": ["api", "v1", "admin", "creators", "{{creatorId}}", "products"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}, {"key": "sort", "value": "-createdAt"}, {"key": "status", "value": "approved", "disabled": true}]}, "description": "Get all products for a specific creator"}, "response": []}], "description": "Endpoints for managing products"}, {"name": "<PERSON><PERSON>", "item": [{"name": "Get All Bales", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{ADMIN_AUTH_TOKEN}}", "type": "text"}], "url": {"raw": "{{BASE_URL}}/api/v1/admin/bales?page=1&limit=10&sort=-createdAt", "host": ["{{BASE_URL}}"], "path": ["api", "v1", "admin", "bales"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}, {"key": "sort", "value": "-createdAt"}, {"key": "status", "value": "pending", "disabled": true}, {"key": "creator", "value": "{{creatorId}}", "disabled": true}, {"key": "search", "value": "bale", "disabled": true}, {"key": "minPrice", "value": "100", "disabled": true}, {"key": "maxPrice", "value": "1000", "disabled": true}, {"key": "category", "value": "{{categoryId}}", "disabled": true}, {"key": "country", "value": "UK", "disabled": true}]}, "description": "Get all bales with pagination, sorting, and filtering options"}, "response": []}, {"name": "Get Bale Stats", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{ADMIN_AUTH_TOKEN}}", "type": "text"}], "url": {"raw": "{{BASE_URL}}/api/v1/admin/bales/stats", "host": ["{{BASE_URL}}"], "path": ["api", "v1", "admin", "bales", "stats"]}, "description": "Get bale statistics including counts by status, category, and price ranges"}, "response": []}, {"name": "Get <PERSON>le by ID", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{ADMIN_AUTH_TOKEN}}", "type": "text"}], "url": {"raw": "{{BASE_URL}}/api/v1/admin/bales/{{baleId}}", "host": ["{{BASE_URL}}"], "path": ["api", "v1", "admin", "bales", "{{baleId}}"]}, "description": "Get a specific bale by ID with all details"}, "response": []}, {"name": "Update <PERSON><PERSON>", "request": {"method": "PATCH", "header": [{"key": "Authorization", "value": "Bearer {{ADMIN_AUTH_TOKEN}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"Updated Bale Name\",\n    \"description\": \"Updated bale description with more details\",\n    \"highlights\": [\"Updated highlight 1\", \"Updated highlight 2\"],\n    \"basePrice\": 599.99,\n    \"discountPrice\": 499.99,\n    \"discountStartDate\": \"2023-08-01T00:00:00.000Z\",\n    \"discountEndDate\": \"2023-09-30T23:59:59.999Z\",\n    \"category\": \"{{categoryId}}\",\n    \"relatedCategories\": [\"{{relatedCategoryId1}}\", \"{{relatedCategoryId2}}\"],\n    \"country\": \"UK\",\n    \"totalItems\": 50,\n    \"specifications\": {\n        \"mainMaterial\": \"Mixed\",\n        \"weight\": \"25kg\",\n        \"dimensions\": \"100cm x 80cm x 60cm\"\n    },\n    \"tags\": [\"updated\", \"bale\", \"wholesale\"],\n    \"products\": [\n        {\n            \"product\": \"{{productId1}}\",\n            \"quantity\": 10\n        },\n        {\n            \"product\": \"{{productId2}}\",\n            \"quantity\": 15\n        }\n    ]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BASE_URL}}/api/v1/admin/bales/{{baleId}}", "host": ["{{BASE_URL}}"], "path": ["api", "v1", "admin", "bales", "{{baleId}}"]}, "description": "Update a bale's details"}, "response": []}, {"name": "Update Bale Status", "request": {"method": "PATCH", "header": [{"key": "Authorization", "value": "Bearer {{ADMIN_AUTH_TOKEN}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"status\": \"approved\",\n    \"statusNote\": \"<PERSON><PERSON> meets all requirements and has been approved.\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BASE_URL}}/api/v1/admin/bales/{{baleId}}/status", "host": ["{{BASE_URL}}"], "path": ["api", "v1", "admin", "bales", "{{baleId}}", "status"]}, "description": "Update a bale's status (pending, approved, rejected, inactive)"}, "response": []}, {"name": "Delete Bale", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{ADMIN_AUTH_TOKEN}}", "type": "text"}], "url": {"raw": "{{BASE_URL}}/api/v1/admin/bales/{{baleId}}", "host": ["{{BASE_URL}}"], "path": ["api", "v1", "admin", "bales", "{{baleId}}"]}, "description": "Delete a bale"}, "response": []}, {"name": "Get Bale Reviews", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{ADMIN_AUTH_TOKEN}}", "type": "text"}], "url": {"raw": "{{BASE_URL}}/api/v1/admin/bales/{{baleId}}/reviews?page=1&limit=10&sort=-createdAt", "host": ["{{BASE_URL}}"], "path": ["api", "v1", "admin", "bales", "{{baleId}}", "reviews"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}, {"key": "sort", "value": "-createdAt"}, {"key": "rating", "value": "5", "disabled": true}, {"key": "hidden", "value": "false", "disabled": true}]}, "description": "Get all reviews for a specific bale"}, "response": []}, {"name": "Get Creator Bales", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{ADMIN_AUTH_TOKEN}}", "type": "text"}], "url": {"raw": "{{BASE_URL}}/api/v1/admin/creators/{{creatorId}}/bales?page=1&limit=10&sort=-createdAt", "host": ["{{BASE_URL}}"], "path": ["api", "v1", "admin", "creators", "{{creatorId}}", "bales"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}, {"key": "sort", "value": "-createdAt"}, {"key": "status", "value": "approved", "disabled": true}]}, "description": "Get all bales for a specific creator"}, "response": []}], "description": "Endpoints for managing bales"}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test(\"Status code is 200 or 201\", function () {", "    pm.expect(pm.response.code).to.be.oneOf([200, 201]);", "});", "", "pm.test(\"Response has success status\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.status).to.eql(\"success\");", "});", "", "pm.test(\"Response time is less than 1000ms\", function () {", "    pm.expect(pm.response.responseTime).to.be.below(1000);", "});"]}}], "variable": [{"key": "BASE_URL", "value": "http://localhost:5000", "type": "string"}, {"key": "ADMIN_AUTH_TOKEN", "value": "your-admin-auth-token-here", "type": "string"}, {"key": "productId", "value": "product-id-here", "type": "string"}, {"key": "baleId", "value": "bale-id-here", "type": "string"}, {"key": "creatorId", "value": "creator-id-here", "type": "string"}, {"key": "categoryId", "value": "category-id-here", "type": "string"}, {"key": "relatedCategoryId1", "value": "related-category-id-1-here", "type": "string"}, {"key": "relatedCategoryId2", "value": "related-category-id-2-here", "type": "string"}, {"key": "productId1", "value": "product-id-1-here", "type": "string"}, {"key": "productId2", "value": "product-id-2-here", "type": "string"}]}