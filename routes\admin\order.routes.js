const express = require('express');
const orderController = require('../../controllers/admin/order.controller');
const exchangeController = require('../../controllers/admin/exchange.controller');
const authMiddleware = require('../../middleware/auth.middleware');


const router = express.Router();

// Protect all routes
router.use(authMiddleware.protect);
router.use(authMiddleware.restrictTo('admin'));

// Order routes
router.get('/orders', orderController.getAllOrders);
router.get('/orders/stats', orderController.getOrderStats);
router.get('/orders/:id', orderController.getOrder);
router.patch('/orders/:id/status', orderController.updateOrderStatus);
router.patch('/orders/:id/tracking', orderController.updateOrderTracking);
router.patch('/orders/:id/mark-paid', orderController.markOrderAsPaid);
router.post('/orders/:orderId/exchange', exchangeController.exchangeOrderItem);

module.exports = router;
