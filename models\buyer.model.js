const mongoose = require('mongoose');
const BaseUser = require('./baseUser.model');

const buyerSchema = new mongoose.Schema({
  // Buyer-specific fields
  addresses: [
    {
      name: String,
      addressLine1: String,
      additionalInfo: String,
      city: String,
      region: String,
      country: String,
      phone: String,
      otherPhone: String,
      isDefault: {
        type: Boolean,
        default: false
      }
    }
  ],
  wishlist: {
      type: mongoose.Schema.ObjectId,
      ref: 'Wishlist'
  },
  preferences: {
    categories: [
      {
        type: mongoose.Schema.ObjectId,
        ref: 'Category'
      }
    ],
    sizes: [String],
    colors: [String],
    priceRange: {
      min: Number,
      max: Number
    },
    notificationPreferences: {
      orderUpdates: {
        type: Boolean,
        default: true
      },
      promotions: {
        type: Boolean,
        default: true
      },
      priceDrops: {
        type: Boolean,
        default: true
      },
      newArrivals: {
        type: Boolean,
        default: false
      },
      email: {
        type: Boolean,
        default: true
      },
      push: {
        type: Boolean,
        default: true
      }
    }
  },
  followedCreators: [
    {
      type: mongoose.Schema.ObjectId,
      ref: 'User'
    }
  ],
  cart: {
    type: mongoose.Schema.ObjectId,
    ref: 'Cart'
  },
  recentlyViewed: [
    {
      product: {
        type: mongoose.Schema.ObjectId,
        ref: 'Product'
      },
      bale: {
        type: mongoose.Schema.ObjectId,
        ref: 'Bale'
      },
      viewedAt: {
        type: Date,
        default: Date.now
      }
    }
  ],
  // Future features (not implemented in MVP)
  loyaltyPoints: {
    balance: {
      type: Number,
      default: 0
    },
    history: [
      {
        amount: Number,
        type: {
          type: String,
          enum: ['earned', 'redeemed', 'expired', 'adjusted']
        },
        reason: String,
        orderId: {
          type: mongoose.Schema.ObjectId,
          ref: 'Order'
        },
        createdAt: {
          type: Date,
          default: Date.now
        }
      }
    ],
    tier: {
      type: String,
      enum: ['bronze', 'silver', 'gold', 'platinum'],
      default: 'bronze'
    }
  }
});



// Method to follow a creator
buyerSchema.methods.followCreator = function(creatorId) {
  if (!this.followedCreators.includes(creatorId)) {
    this.followedCreators.push(creatorId);
  }
  return this.save();
};

// Method to unfollow a creator
buyerSchema.methods.unfollowCreator = function(creatorId) {
  this.followedCreators = this.followedCreators.filter(
    id => id.toString() !== creatorId.toString()
  );
  return this.save();
};

// Method to add recently viewed product
buyerSchema.methods.addRecentlyViewed = function(itemId, type = 'product') {
  // Remove if already exists
  this.recentlyViewed = this.recentlyViewed.filter(item => {
    if (type === 'product' && item.product) {
      return item.product._id.toString() !== itemId.toString();
    } else if (type === 'bale' && item.bale) {
      return item.bale.toString() !== itemId.toString();
    }
    return true;
  });

  // Add to the beginning of the array
  const newItem = {
    viewedAt: Date.now()
  };

  if (type === 'product') {
    newItem.product = itemId;
  } else if (type === 'bale') {
    newItem.bale = itemId;
  }

  this.recentlyViewed.unshift(newItem);

  // Limit to 20 items
  if (this.recentlyViewed.length > 20) {
    this.recentlyViewed = this.recentlyViewed.slice(0, 20);
  }

  return this.save();
};



// Set default role to 'buyer' for buyers
buyerSchema.pre('save', function(next) {
  if (!this.role) {
    this.role = 'buyer';
  }
  next();
});

// Create the Buyer model as a discriminator of BaseUser
const Buyer = BaseUser.discriminator('Buyer', buyerSchema);

module.exports = Buyer;



