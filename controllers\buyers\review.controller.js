const Review = require('../../models/review.model');
const Product = require('../../models/product.model');
const Order = require('../../models/order.model');
const { Creator } = require('../../models/user.model');
const AppError = require('../../utils/appError');
const catchAsync = require('../../utils/catchAsync');

/**
 * Get buyer reviews
 * @route GET /api/v1/buyers/reviews
 * @access Private (Buyer only)
 */
exports.getMyReviews = catchAsync(async (req, res, next) => {
  const reviews = await Review.find({ user: req.user.id })
    .sort('-createdAt');

  res.status(200).json({
    status: 'success',
    results: reviews.length,
    data: {
      reviews
    }
  });
});

/**
 * Create a review for a product
 * @route POST /api/v1/buyers/products/:id/reviews
 * @access Private (Buyer only)
 */
exports.createProductReview = catchAsync(async (req, res, next) => {
  // Check if product exists
  const product = await Product.findById(req.params.id);
  if (!product) {
    return next(new AppError('No product found with that ID', 404));
  }

  // Check if buyer has purchased the product
  const hasPurchased = await Order.exists({
    user: req.user.id,
    'items.product': req.params.id,
    status: 'delivered'
  });

  if (!hasPurchased) {
    return next(new AppError('You can only review products you have purchased', 403));
  }

  // Check if buyer has already reviewed the product
  const hasReviewed = await Review.exists({
    user: req.user.id,
    product: req.params.id
  });

  if (hasReviewed) {
    return next(new AppError('You have already reviewed this product', 400));
  }

  // Validate required fields
  if (!req.body.title || !req.body.review || !req.body.rating) {
    return next(new AppError('Please provide title, review, and rating', 400));
  }

  // Validate rating
  if (req.body.rating < 1 || req.body.rating > 5) {
    return next(new AppError('Rating must be between 1 and 5', 400));
  }

  // Create review
  const review = await Review.create({
    title: req.body.title,
    review: req.body.review,
    rating: req.body.rating,
    user: req.user.id,
    product: req.params.id,
    creator: product.creator
  });

  res.status(201).json({
    status: 'success',
    data: {
      review
    }
  });
});

/**
 * Create a review for a bale (now handled as product with type: 'bale')
 * @route POST /api/v1/buyers/bales/:id/reviews
 * @access Private (Buyer only)
 */
exports.createBaleReview = catchAsync(async (req, res, next) => {
  // Check if bale exists (now stored as product with type: 'bale')
  const bale = await Product.findOne({ _id: req.params.id, type: 'bale' });
  if (!bale) {
    return next(new AppError('No bale found with that ID', 404));
  }

  // Check if buyer has purchased the bale
  const hasPurchased = await Order.exists({
    user: req.user.id,
    'items.product': req.params.id,
    status: 'delivered'
  });

  if (!hasPurchased) {
    return next(new AppError('You can only review bales you have purchased', 403));
  }

  // Check if buyer has already reviewed the bale
  const hasReviewed = await Review.exists({
    user: req.user.id,
    product: req.params.id
  });

  if (hasReviewed) {
    return next(new AppError('You have already reviewed this bale', 400));
  }

  // Validate required fields
  if (!req.body.title || !req.body.review || !req.body.rating) {
    return next(new AppError('Please provide title, review, and rating', 400));
  }

  // Validate rating
  if (req.body.rating < 1 || req.body.rating > 5) {
    return next(new AppError('Rating must be between 1 and 5', 400));
  }

  // Create review
  const review = await Review.create({
    title: req.body.title,
    review: req.body.review,
    rating: req.body.rating,
    user: req.user.id,
    product: req.params.id,
    creator: bale.creator
  });

  res.status(201).json({
    status: 'success',
    data: {
      review
    }
  });
});

/**
 * Create a review for a creator
 * @route POST /api/v1/buyers/creators/:id/reviews
 * @access Private (Buyer only)
 */
exports.createCreatorReview = catchAsync(async (req, res, next) => {
  // Check if creator exists
  const creator = await Creator.findById(req.params.id);
  if (!creator) {
    return next(new AppError('No creator found with that ID', 404));
  }

  // Check if buyer has purchased from the creator
  const hasPurchased = await Order.exists({
    user: req.user.id,
    'items.creator': req.params.id,
    status: 'delivered'
  });

  if (!hasPurchased) {
    return next(new AppError('You can only review creators you have purchased from', 403));
  }

  // Check if buyer has already reviewed the creator
  const hasReviewed = await Review.exists({
    user: req.user.id,
    creator: req.params.id
  });

  if (hasReviewed) {
    return next(new AppError('You have already reviewed this creator', 400));
  }

  // Validate required fields
  if (!req.body.title || !req.body.review || !req.body.rating) {
    return next(new AppError('Please provide title, review, and rating', 400));
  }

  // Validate rating
  if (req.body.rating < 1 || req.body.rating > 5) {
    return next(new AppError('Rating must be between 1 and 5', 400));
  }

  // Create review
  const review = await Review.create({
    title: req.body.title,
    review: req.body.review,
    rating: req.body.rating,
    user: req.user.id,
    creator: req.params.id
  });

  res.status(201).json({
    status: 'success',
    data: {
      review
    }
  });
});

/**
 * Mark a review as helpful
 * @route PATCH /api/v1/buyers/reviews/:id/helpful
 * @access Private (Buyer only)
 */
exports.markReviewAsHelpful = catchAsync(async (req, res, next) => {
  // Find review
  const review = await Review.findById(req.params.id);

  if (!review) {
    return next(new AppError('No review found with that ID', 404));
  }

  // Check if buyer has already marked the review as helpful
  if (review.helpfulVotes.users.includes(req.user.id)) {
    return next(new AppError('You have already marked this review as helpful', 400));
  }

  // Mark review as helpful
  review.helpfulVotes.count += 1;
  review.helpfulVotes.users.push(req.user.id);
  await review.save();

  res.status(200).json({
    status: 'success',
    data: {
      review
    }
  });
});
