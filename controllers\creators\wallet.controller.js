const WalletService = require('../../services/wallet.service');
const AppError = require('../../utils/appError');
const catchAsync = require('../../utils/catchAsync');

/**
 * Get creator wallet balance
 * @route GET /api/v1/creators/wallet/balance
 * @access Private (Creator only)
 */
exports.getWalletBalance = catchAsync(async (req, res, next) => {
  // Get wallet balance
  const balance = await WalletService.getWalletBalance(req.user.id, 'creator');

  res.status(200).json({
    status: 'success',
    data: {
      balance
    }
  });
});

/**
 * Get creator wallet transactions
 * @route GET /api/v1/creators/wallet/transactions
 * @access Private (Creator only)
 */
exports.getWalletTransactions = catchAsync(async (req, res, next) => {
  // Parse query parameters
  const options = {
    limit: req.query.limit ? parseInt(req.query.limit) : 10,
    skip: req.query.page ? (parseInt(req.query.page) - 1) * (req.query.limit ? parseInt(req.query.limit) : 10) : 0,
    type: req.query.type,
    status: req.query.status,
    startDate: req.query.startDate,
    endDate: req.query.endDate
  };

  // Get wallet transactions
  const transactions = await WalletService.getWalletTransactions(
    req.user.id,
    'creator',
    options
  );

  res.status(200).json({
    status: 'success',
    results: transactions.length,
    data: {
      transactions
    }
  });
});

/**
 * Get creator wallet transaction summary
 * @route GET /api/v1/creators/wallet/summary
 * @access Private (Creator only)
 */
exports.getWalletTransactionSummary = catchAsync(async (req, res, next) => {
  // Get wallet transaction summary
  const summary = await WalletService.getWalletTransactionSummary(
    req.user.id,
    'creator'
  );

  res.status(200).json({
    status: 'success',
    data: {
      summary
    }
  });
});

/**
 * Get creator earnings
 * @route GET /api/v1/creators/wallet/earnings
 * @access Private (Creator only)
 */
exports.getEarnings = catchAsync(async (req, res, next) => {
  // Calculate creator available balance
  const earnings = await WalletService.calculateCreatorAvailableBalance(req.user.id);

  res.status(200).json({
    status: 'success',
    data: {
      earnings
    }
  });
});
