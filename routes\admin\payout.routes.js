const express = require('express');
const payoutController = require('../../controllers/buyers/payout1.controller');
const authMiddleware = require('../../middleware/auth.middleware');

const router = express.Router();

// Protect all routes
router.use(authMiddleware.protect);

// Creator routes
router.get('/', authMiddleware.restrictTo('creator'), payoutController.getMyPayouts);
router.get('/stats', authMiddleware.restrictTo('creator'), payoutController.getMyPayoutStats);
router.post('/request', authMiddleware.restrictTo('creator'), payoutController.requestPayout);
router.patch('/:id/cancel', authMiddleware.restrictTo('creator'), payoutController.cancelPayoutRequest);
router.get('/:id', authMiddleware.restrictTo('creator'), payoutController.getMyPayout);

// Admin routes
router.get('/admin', authMiddleware.restrictTo('admin'), payoutController.getAllPayouts);
router.get('/admin/stats', authMiddleware.restrictTo('admin'), payoutController.getPayoutStats);
router.get('/admin/:id', authMiddleware.restrictTo('admin'), payoutController.getPayout);
router.patch('/admin/:id/status', authMiddleware.restrictTo('admin'), payoutController.updatePayoutStatus);
router.post('/admin/batch', authMiddleware.restrictTo('admin'), payoutController.createPayoutBatch);
router.patch('/admin/batch/:id/add', authMiddleware.restrictTo('admin'), payoutController.addPayoutsToBatch);
router.patch('/admin/batch/:id/process', authMiddleware.restrictTo('admin'), payoutController.processBatch);

module.exports = router;
