const axios = require('axios');

/**
 * Test script to verify the updateBasicInfo fix
 * This tests that the endpoint properly handles partial updates
 */

const BASE_URL = 'http://localhost:3000/api/v1';

// Test cases for different update scenarios
const testCases = [
  {
    name: "Update only name",
    data: { name: "Updated Product Name" }
  },
  {
    name: "Update only price",
    data: { basePrice: 149.99 }
  },
  {
    name: "Update multiple fields",
    data: { 
      name: "New Product Name",
      brand: "New Brand",
      description: "Updated description"
    }
  },
  {
    name: "Update with category",
    data: { 
      name: "Product with Category",
      category: "507f1f77bcf86cd799439011" // Sample ObjectId
    }
  },
  {
    name: "Update with tags",
    data: { 
      tags: ["updated", "test", "product"]
    }
  }
];

async function testUpdateBasicInfo() {
  console.log('🧪 Testing updateBasicInfo Fix\n');

  // You'll need to get a valid JWT token for a creator and a product ID
  const authToken = 'YOUR_JWT_TOKEN_HERE'; // Replace with actual token
  const productId = 'YOUR_PRODUCT_ID_HERE'; // Replace with actual product ID
  
  const headers = {
    'Authorization': `Bearer ${authToken}`,
    'Content-Type': 'application/json'
  };

  try {
    for (let i = 0; i < testCases.length; i++) {
      const testCase = testCases[i];
      console.log(`--- Test ${i + 1}: ${testCase.name} ---`);
      
      try {
        const response = await axios.patch(
          `${BASE_URL}/creators/products/${productId}/basic-info`, 
          testCase.data, 
          { headers }
        );
        
        console.log(`✅ Success: ${response.status}`);
        console.log(`Updated fields: ${Object.keys(testCase.data).join(', ')}`);
        console.log(`Product name: ${response.data.data.product.name}\n`);
        
      } catch (error) {
        if (error.response) {
          console.log(`❌ Failed: ${error.response.status} - ${error.response.data.message}`);
        } else {
          console.log(`❌ Failed: ${error.message}`);
        }
        console.log('');
      }
    }

    // Test empty body (should fail gracefully)
    console.log('--- Test: Empty Body (should fail gracefully) ---');
    try {
      const response = await axios.patch(
        `${BASE_URL}/creators/products/${productId}/basic-info`, 
        {}, 
        { headers }
      );
      console.log(`❌ Unexpected success: ${response.status}`);
    } catch (error) {
      if (error.response && error.response.status === 400) {
        console.log(`✅ Correctly rejected empty body: ${error.response.data.message}`);
      } else {
        console.log(`❌ Unexpected error: ${error.response?.data?.message || error.message}`);
      }
    }

    console.log('\n🎯 All tests completed!');
    console.log('The updateBasicInfo method now properly handles:');
    console.log('- Partial updates (only provided fields)');
    console.log('- Empty request body validation');
    console.log('- ObjectId conversion for category fields');
    console.log('- No more "Cannot read properties of undefined" errors');

  } catch (error) {
    console.error('Test setup error:', error.message);
    console.log('\n💡 Make sure to:');
    console.log('1. Replace YOUR_JWT_TOKEN_HERE with a valid creator JWT token');
    console.log('2. Replace YOUR_PRODUCT_ID_HERE with a valid product ID');
    console.log('3. Ensure the server is running on localhost:3000');
  }
}

// Instructions for running the test
console.log('📋 Setup Instructions:');
console.log('1. Start your server: npm run dev');
console.log('2. Create a product or get an existing product ID');
console.log('3. Get a JWT token by logging in as a creator');
console.log('4. Replace the placeholders in this file');
console.log('5. Run this test: node test-update-basic-info.js\n');

// Uncomment the line below to run the test
// testUpdateBasicInfo();
