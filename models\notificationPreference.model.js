const mongoose = require('mongoose');

const notificationPreferenceSchema = new mongoose.Schema(
  {
    user: {
      type: mongoose.Schema.ObjectId,
      ref: 'User',
      required: [true, 'Notification preference must belong to a user'],
      unique: true
    },
    // General channel preferences
    channels: {
      inApp: {
        type: Boolean,
        default: true
      },
      email: {
        type: Boolean,
        default: true
      },
      push: {
        type: <PERSON><PERSON>an,
        default: true
      },
      sms: {
        type: Boolean,
        default: false
      }
    },
    // Notification type preferences
    types: {
      // Order related
      orders: {
        type: Boolean,
        default: true
      },
      orderUpdates: {
        type: Boolean,
        default: true
      },
      orderShipped: {
        type: <PERSON><PERSON>an,
        default: true
      },
      orderDelivered: {
        type: Boolean,
        default: true
      },
      
      // Payment related
      payments: {
        type: Boolean,
        default: true
      },
      paymentFailed: {
        type: Boolean,
        default: true
      },
      
      // Product related
      productUpdates: {
        type: Boolean,
        default: true
      },
      lowStock: {
        type: Boolean,
        default: true
      },
      outOfStock: {
        type: Boolean,
        default: true
      },
      priceDrops: {
        type: <PERSON>olean,
        default: true
      },
      newArrivals: {
        type: <PERSON>olean,
        default: false
      },
      
      // Review related
      reviews: {
        type: Boolean,
        default: true
      },
      
      // Payout related
      payouts: {
        type: Boolean,
        default: true
      },
      
      // Wallet related
      walletTransactions: {
        type: Boolean,
        default: true
      },
      
      // Promotion related
      promotions: {
        type: Boolean,
        default: true
      },
      flashSales: {
        type: Boolean,
        default: true
      },
      
      // System related
      systemMessages: {
        type: Boolean,
        default: true
      },
      accountUpdates: {
        type: Boolean,
        default: true
      },
      
      // Social related
      newFollowers: {
        type: Boolean,
        default: true
      },
      messages: {
        type: Boolean,
        default: true
      },
      newCreators: {
      type: Boolean,
      default: false
      },
      creatorVerifications: {
        type: Boolean,
        default: false
      },
      highValueOrders: {
        type: Boolean,
        default: false
      },
      disputes: {
        type: Boolean,
        default: false
      },
      systemAlerts: {
        type: Boolean,
        default: false
      }
    },
    // Quiet hours (don't send notifications during these hours)
    quietHours: {
      enabled: {
        type: Boolean,
        default: false
      },
      start: {
        type: String,
        default: '22:00' // 10 PM
      },
      end: {
        type: String,
        default: '08:00' // 8 AM
      },
      timezone: {
        type: String,
        default: 'UTC'
      }
    }
  },
  {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true }
  }
);

// Check if notification should be sent during quiet hours
notificationPreferenceSchema.methods.isQuietHours = function() {
  if (!this.quietHours.enabled) {
    return false;
  }
  
  const now = new Date();
  const timezone = this.quietHours.timezone || 'UTC';
  
  // Convert current time to user's timezone
  const options = { timeZone: timezone, hour12: false, hour: '2-digit', minute: '2-digit' };
  const currentTime = now.toLocaleTimeString('en-US', options);
  
  // Parse quiet hours start and end times
  const startParts = this.quietHours.start.split(':');
  const endParts = this.quietHours.end.split(':');
  
  const startHour = parseInt(startParts[0]);
  const startMinute = parseInt(startParts[1]);
  const endHour = parseInt(endParts[0]);
  const endMinute = parseInt(endParts[1]);
  
  // Parse current time
  const currentParts = currentTime.split(':');
  const currentHour = parseInt(currentParts[0]);
  const currentMinute = parseInt(currentParts[1]);
  
  // Convert to minutes for easier comparison
  const startMinutes = startHour * 60 + startMinute;
  const endMinutes = endHour * 60 + endMinute;
  const currentMinutes = currentHour * 60 + currentMinute;
  
  // Check if current time is within quiet hours
  if (startMinutes < endMinutes) {
    // Normal case: start time is before end time (e.g., 22:00 to 08:00)
    return currentMinutes >= startMinutes && currentMinutes <= endMinutes;
  } else {
    // Wrap around case: start time is after end time (e.g., 22:00 to 08:00)
    return currentMinutes >= startMinutes || currentMinutes <= endMinutes;
  }
};

// Check if a specific notification type is enabled
notificationPreferenceSchema.methods.isTypeEnabled = function(type) {
  // Map notification type to preference field
  const typeMap = {
    'order_placed': 'orders',
    'order_status_update': 'orderUpdates',
    'order_shipped': 'orderShipped',
    'order_delivered': 'orderDelivered',
    'order_cancelled': 'orderUpdates',
    'order_refunded': 'orderUpdates',
    
    'payment_received': 'payments',
    'payment_failed': 'paymentFailed',
    'payment_refunded': 'payments',
    
    'product_review': 'reviews',
    'product_question': 'productUpdates',
    'product_answer': 'productUpdates',
    'product_approved': 'productUpdates',
    'product_rejected': 'productUpdates',
    'low_stock': 'lowStock',
    'out_of_stock': 'outOfStock',
    'price_drop': 'priceDrops',
    'product_featured': 'productUpdates',
    
    'bale_approved': 'productUpdates',
    'bale_rejected': 'productUpdates',
    'bale_low_stock': 'lowStock',
    'bale_out_of_stock': 'outOfStock',
    
    'creator_application': 'accountUpdates',
    'creator_verified': 'accountUpdates',
    'creator_rejected': 'accountUpdates',
    'creator_featured': 'accountUpdates',
    
    'payout_processed': 'payouts',
    'payout_failed': 'payouts',
    'payout_initiated': 'payouts',
    
    'wallet_transaction': 'walletTransactions',
    'wallet_low_balance': 'walletTransactions',
    'wallet_adjustment': 'walletTransactions',
    
    'promotion': 'promotions',
    'promotion_ending': 'promotions',
    'flash_sale_started': 'flashSales',
    'flash_sale_ending': 'flashSales',
    
    'account_update': 'accountUpdates',
    'system_message': 'systemMessages',
    'new_feature': 'systemMessages',
    'maintenance': 'systemMessages',
    
    'new_follower': 'newFollowers',
    'message_received': 'messages'
  };
  
  const preferenceField = typeMap[type] || 'systemMessages';
  
  return this.types[preferenceField] !== false; // Default to true if not explicitly set
};

// Check if a specific channel is enabled
notificationPreferenceSchema.methods.isChannelEnabled = function(channel) {
  return this.channels[channel] !== false; // Default to true if not explicitly set
};

// Static method to get or create preferences for a user
notificationPreferenceSchema.statics.getForUser = async function(userId) {
  let preferences = await this.findOne({ user: userId });
  
  if (!preferences) {
    // Create default preferences
    preferences = await this.create({ user: userId });
  }
  
  return preferences;
};

// Static method to update preferences for a user
notificationPreferenceSchema.statics.updateForUser = async function(userId, updates) {
  const preferences = await this.getForUser(userId);
  
  // Update channels
  if (updates.channels) {
    Object.keys(updates.channels).forEach(channel => {
      if (preferences.channels[channel] !== undefined) {
        preferences.channels[channel] = updates.channels[channel];
      }
    });
  }
  
  // Update types
  if (updates.types) {
    Object.keys(updates.types).forEach(type => {
      if (preferences.types[type] !== undefined) {
        preferences.types[type] = updates.types[type];
      }
    });
  }
  
  // Update quiet hours
  if (updates.quietHours) {
    if (updates.quietHours.enabled !== undefined) {
      preferences.quietHours.enabled = updates.quietHours.enabled;
    }
    
    if (updates.quietHours.start) {
      preferences.quietHours.start = updates.quietHours.start;
    }
    
    if (updates.quietHours.end) {
      preferences.quietHours.end = updates.quietHours.end;
    }
    
    if (updates.quietHours.timezone) {
      preferences.quietHours.timezone = updates.quietHours.timezone;
    }
  }
  
  return preferences.save();
};

const NotificationPreference = mongoose.model('NotificationPreference', notificationPreferenceSchema);

module.exports = NotificationPreference;
