const express = require('express');
const onboardingController = require('../../controllers/creators/onboarding.controller');
const uploadMiddleware = require('../../middleware/cloudinaryUpload.middleware');

const router = express.Router();


// Onboarding routes
router.get('/', onboardingController.getOnboardingStatus);

// Business Info routes
router.get('/business-info', onboardingController.getBusinessInfo);
router.patch('/business-info',
  uploadMiddleware.uploadVerificationDocuments,
  uploadMiddleware.processCloudinaryFiles,
  uploadMiddleware.processNestedFormData,
  uploadMiddleware.handleUploadError,
  onboardingController.updateBusinessInfo
);

// Payment Info routes
router.get('/payment-info', onboardingController.getPaymentInfo);
router.patch('/payment-info', onboardingController.updatePaymentInfo);

// Shop Info routes
router.get('/shop-info', onboardingController.getShopInfo);
router.patch('/shop-info',
  uploadMiddleware.uploadShopMedia,
  uploadMiddleware.processCloudinaryFiles,
  uploadMiddleware.processNestedFormData,
  uploadMiddleware.handleUploadError,
  onboardingController.updateShopInfo
);

// Shipping Info routes
router.get('/shipping-info', onboardingController.getShippingInfo);
router.patch('/shipping-info',
  uploadMiddleware.processNestedFormData,
  onboardingController.updateShippingInfo
);


// Status route (alias for '/')
router.get('/status', onboardingController.getOnboardingStatus);

// Verification status route
router.get('/verification-status', onboardingController.getVerificationStatus);

module.exports = router;
