const axios = require('axios');

/**
 * SMS Service
 * Handles all SMS-related operations using Arkesel
 */
class SMSService {
  constructor() {
    // Initialize Arkesel API key if available
    this.apiKey = process.env.ARKESEL_API_KEY;
    this.sender = process.env.ARKESEL_SENDER_ID || 'FLASHY';

    if (!this.apiKey) {
      // Create a mock implementation for development
      this.useMockImplementation = true;
      console.warn('Arkesel API key not found. Using mock implementation for SMS service');
    } else {
      console.log('Arkesel SMS service initialized successfully');
    }
  }

  /**
   * Send an SMS using Arkesel
   * @param {String} to - Recipient phone number
   * @param {String} message - SMS message
   * @returns {Promise<Object>} - Arkesel response
   */
  async sendSMS(to, message) {
    // If using mock implementation, just log the SMS
    if (this.useMockImplementation) {
      console.log(`[SMS] To: ${to}, Message: ${message}`);
      return Promise.resolve({
        status: 'success',
        code: 200,
        message: 'SMS sent successfully (mock)',
        data: {
          balance: 100,
          queued: 1,
          smsId: `mock-${Date.now()}`
        }
      });
    }

    try {
      // Validate phone number
      if (!this.isValidPhoneNumber(to)) {
        throw new Error(`Invalid phone number: ${to}`);
      }

      // Format phone number for Arkesel (remove + and add country code if needed)
      const formattedNumber = this.formatPhoneNumber(to);

      // Send SMS using Arkesel API
      const response = await axios({
        method: 'post',
        url: 'https://sms.arkesel.com/api/v2/sms/send',
        headers: {
          'api-key': this.apiKey,
          'Content-Type': 'application/json'
        },
        data: {
          sender: this.sender,
          recipients: [formattedNumber],
          message
        }
      });

      return response.data;
    } catch (error) {
      console.error('Error sending SMS:', error.response?.data || error.message);
      throw error;
    }
  }

  /**
   * Send a notification SMS
   * @param {Object} notification - Notification object
   * @param {String} phoneNumber - Recipient phone number
   * @returns {Promise<Object>} - Twilio response
   */
  async sendNotificationSMS(notification, phoneNumber) {
    try {
      // Prepare SMS body
      let body = `${notification.title}\n\n${notification.message}`;

      // Add action URL if available
      if (notification.data?.url) {
        body += `\n\nView details: ${process.env.FRONTEND_URL || 'https://flashy.com'}${notification.data.url}`;
      }

      // Send SMS
      return await this.sendSMS(phoneNumber, body);
    } catch (error) {
      console.error('Error sending notification SMS:', error);
      throw error;
    }
  }

  /**
   * Send an order confirmation SMS
   * @param {Object} order - Order object
   * @param {String} phoneNumber - Recipient phone number
   * @returns {Promise<Object>} - Twilio response
   */
  async sendOrderConfirmationSMS(order, phoneNumber) {
    try {
      // Prepare SMS body
      const body = `Your order #${order.orderNumber} has been confirmed. Total: ${order.total.toFixed(2)} GHS. Thank you for shopping with Flashy!`;

      // Send SMS
      return await this.sendSMS(phoneNumber, body);
    } catch (error) {
      console.error('Error sending order confirmation SMS:', error);
      throw error;
    }
  }

  /**
   * Send an order status update SMS
   * @param {Object} order - Order object
   * @param {String} phoneNumber - Recipient phone number
   * @returns {Promise<Object>} - Twilio response
   */
  async sendOrderStatusUpdateSMS(order, phoneNumber) {
    try {
      // Prepare SMS body
      const body = `Your order #${order.orderNumber} status has been updated to: ${order.status}. Thank you for shopping with Flashy!`;

      // Send SMS
      return await this.sendSMS(phoneNumber, body);
    } catch (error) {
      console.error('Error sending order status update SMS:', error);
      throw error;
    }
  }

  /**
   * Validate phone number
   * @param {String} phoneNumber - Phone number to validate
   * @returns {Boolean} - Whether the phone number is valid
   */
  isValidPhoneNumber(phoneNumber) {
    // Simple validation for phone numbers
    return /^(\+|00)?[0-9]{10,15}$/.test(phoneNumber);
  }

  /**
   * Format phone number for Arkesel
   * @param {String} phoneNumber - Phone number to format
   * @returns {String} - Formatted phone number
   */
  formatPhoneNumber(phoneNumber) {
    // Remove any non-digit characters
    let digits = phoneNumber.replace(/\D/g, '');

    // If the number starts with a country code (e.g., 233 for Ghana)
    if (digits.length > 10) {
      return digits;
    }

    // Otherwise, assume it's a Ghanaian number and add the country code
    if (digits.startsWith('0')) {
      digits = digits.substring(1);
    }

    return `233${digits}`;
  }
}

// Create a singleton instance
const smsService = new SMSService();

module.exports = smsService;
