const express = require('express');
const imageCleanupController = require('../../controllers/admin/imageCleanup.controller');

const router = express.Router();

// All routes are protected by admin middleware in the main admin routes

/**
 * @route GET /api/v1/admin/images/scan
 * @desc Scan for orphaned images in Cloudinary
 * @access Private (Admin only)
 */
router.get('/scan', imageCleanupController.scanOrphanedImages);

/**
 * @route DELETE /api/v1/admin/images/cleanup
 * @desc Clean up orphaned images
 * @query dryRun - Set to 'true' for dry run mode
 * @access Private (Admin only)
 */
router.delete('/cleanup', imageCleanupController.cleanupOrphanedImages);

/**
 * @route DELETE /api/v1/admin/images/delete
 * @desc Delete specific images by URLs
 * @body imageUrls - Array of image URLs to delete
 * @access Private (Admin only)
 */
router.delete('/delete', imageCleanupController.deleteSpecificImages);

/**
 * @route DELETE /api/v1/admin/images/delete-single
 * @desc Delete a single image by URL
 * @body imageUrl - Image URL to delete
 * @access Private (Admin only)
 */
router.delete('/delete-single', imageCleanupController.deleteSingleImageByUrl);

/**
 * @route GET /api/v1/admin/images/stats
 * @desc Get Cloudinary storage statistics
 * @access Private (Admin only)
 */
router.get('/stats', imageCleanupController.getStorageStats);

/**
 * @route GET /api/v1/admin/images/recent
 * @desc Get recent uploads
 * @query limit - Number of recent uploads to fetch (max 100, default 20)
 * @access Private (Admin only)
 */
router.get('/recent', imageCleanupController.getRecentUploads);

module.exports = router;
