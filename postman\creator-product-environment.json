{"id": "creator-product-test-env", "name": "Creator Product Test Environment", "values": [{"key": "base_url", "value": "http://localhost:3000/api/v1", "type": "default", "enabled": true}, {"key": "creator_token", "value": "", "type": "secret", "enabled": true}, {"key": "creator_email", "value": "<EMAIL>", "type": "default", "enabled": true}, {"key": "creator_password", "value": "password123", "type": "secret", "enabled": true}, {"key": "product_id", "value": "", "type": "default", "enabled": true}, {"key": "variation_id", "value": "", "type": "default", "enabled": true}, {"key": "promotion_id", "value": "", "type": "default", "enabled": true}, {"key": "category_id", "value": "", "type": "default", "enabled": true}, {"key": "invalid_product_id", "value": "507f1f77bcf86cd799439011", "type": "default", "enabled": true}, {"key": "active_product_id", "value": "", "type": "default", "enabled": true}, {"key": "draft_product_id", "value": "", "type": "default", "enabled": true}, {"key": "pending_product_id", "value": "", "type": "default", "enabled": true}], "_postman_variable_scope": "environment"}