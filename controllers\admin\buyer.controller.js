const mongoose = require('mongoose');
const { Creator, Buyer, Admin } = require('../../models/user.model');
const Order = require('../../models/order.model');
const Review = require('../../models/review.model');
const Wishlist = require('../../models/wishlist.model');
const Cart = require('../../models/cart.model');
const Product = require('../../models/product.model');
const AppError = require('../../utils/appError');
const catchAsync = require('../../utils/catchAsync');
const crypto = require('crypto');
const sendEmail = require('../../utils/sendEmail');


/**
 * Update buyer details by admin
 * @route PATCH /api/v1/admin/buyers/:id/details
 * @access Private (Admin only)
 */
exports.updateBuyerDetails = catchAsync(async (req, res, next) => {
  // Create a filtered body to prevent unwanted fields
  const filteredBody = {};
  const allowedFields = ['name', 'email', 'phone', 'gender', 'dateOfBirth', 'active'];

  Object.keys(req.body).forEach(key => {
    if (allowedFields.includes(key)) {
      filteredBody[key] = req.body[key];
    }
  });

  // Find and update buyer
  const updatedBuyer = await Buyer.findByIdAndUpdate(
    req.params.id,
    filteredBody,
    {
      new: true,
      runValidators: true
    }
  ).select('-__v -password -passwordChangedAt -passwordResetToken -passwordResetExpires');

  if (!updatedBuyer) {
    return next(new AppError('No buyer found with that ID', 404));
  }

  res.status(200).json({
    status: 'success',
    data: {
      buyer: updatedBuyer
    }
  });
});


/**
 * Reset buyer password by admin
 * @route POST /api/v1/admin/buyers/:id/reset-password
 * @access Private (Admin only)
 */
exports.resetBuyerPassword = catchAsync(async (req, res, next) => {
  // Find buyer
  const buyer = await Buyer.findById(req.params.id);

  if (!buyer) {
    return next(new AppError('No buyer found with that ID', 404));
  }

  // Generate a random password
  const tempPassword = crypto.randomBytes(8).toString('hex');

  // Update buyer with new password
  buyer.password = tempPassword;
  buyer.passwordConfirm = tempPassword;
  buyer.passwordChangedAt = Date.now();

  await buyer.save();

   // 4. Prepare the email
   const htmlMessage = `
   <p>Your password has been reset by an administrator.</p>
   <p>Your temporary password is: <strong>${tempPassword}</strong></p>
   <p>Please log in and change your password immediately.</p>
 `;

 const plainMessage = `
   Your password has been reset by an administrator.\n
   Your temporary password is: ${tempPassword}\n
   Please log in and change your password immediately.
 `;

  try {
    await sendEmail({
      email: creator.email,
      subject: 'Your Password Has Been Reset',
      message: plainMessage, // plaintext version
      html: htmlMessage      // html version
    });

    res.status(200).json({
      status: 'success',
      message: 'Password reset successfully. Temporary password has been sent to the buyer\'s email.'
    });
  } catch (err) {
    // If there was an error sending the email, return the temporary password in the response
    res.status(200).json({
      status: 'success',
      message: 'Password reset successfully, but email could not be sent.',
      data: {
        tempPassword
      }
    });
  }
});

/**
 * Get all buyers
 * @route GET /api/v1/admin/buyers
 * @access Private (Admin only)
 */
exports.getAllBuyers = catchAsync(async (req, res, next) => {
  // Build query
  const queryObj = { ...req.query };
  const excludedFields = ['page', 'sort', 'limit', 'fields', 'search'];
  excludedFields.forEach(el => delete queryObj[el]);

  // Advanced filtering
  let queryStr = JSON.stringify(queryObj);
  queryStr = queryStr.replace(/\b(gte|gt|lte|lt)\b/g, match => `$${match}`);

  let query = Buyer.find(JSON.parse(queryStr));

  // Search functionality
  if (req.query.search) {
    const searchRegex = new RegExp(req.query.search, 'i');
    query = query.find({
      $or: [
        { name: searchRegex },
        { email: searchRegex },
        { phone: searchRegex }
      ]
    });
  }

  // Count total before applying pagination
  const total = await Buyer.countDocuments(query);

  // Sorting
  if (req.query.sort) {
    const sortBy = req.query.sort.split(',').join(' ');
    query = query.sort(sortBy);
  } else {
    query = query.sort('-createdAt');
  }

  // Pagination
  const page = req.query.page * 1 || 1;
  const limit = req.query.limit * 1 || 10;
  const skip = (page - 1) * limit;

  query = query.skip(skip).limit(limit);

  // Execute query
  const buyers = await query.select('-__v -password -passwordChangedAt -passwordResetToken -passwordResetExpires');

  res.status(200).json({
    status: 'success',
    results: buyers.length,
    total,
    page,
    limit,
    totalPages: Math.ceil(total / limit),
    data: {
      buyers
    }
  });
});

/**
 * Get buyer by ID with detailed information
 * @route GET /api/v1/admin/buyers/:id
 * @access Private (Admin only)
 */
exports.getBuyer = catchAsync(async (req, res, next) => {
  // Get buyer basic information
  const buyer = await Buyer.findById(req.params.id)
    .select('-__v -password -passwordChangedAt -passwordResetToken -passwordResetExpires');

  if (!buyer) {
    return next(new AppError('No buyer found with that ID', 404));
  }

  // Get pagination parameters
  const ordersPage = parseInt(req.query.ordersPage) || 1;
  const ordersLimit = parseInt(req.query.ordersLimit) || 5;
  const wishlistPage = parseInt(req.query.wishlistPage) || 1;
  const wishlistLimit = parseInt(req.query.wishlistLimit) || 5;
  const cartPage = parseInt(req.query.cartPage) || 1;
  const cartLimit = parseInt(req.query.cartLimit) || 5;

  // Get order statistics
  const totalOrders = await Order.countDocuments({ user: req.params.id });
  const completedOrders = await Order.countDocuments({
    user: req.params.id,
    status: 'delivered'
  });
  const pendingOrders = await Order.countDocuments({
    user: req.params.id,
    status: { $in: ['pending', 'processing', 'shipped'] }
  });
  const cancelledOrders = await Order.countDocuments({
    user: req.params.id,
    status: 'cancelled'
  });

  // Get total spent
  const totalSpentResult = await Order.aggregate([
    {
      $match: {
        $expr: { $eq: ["$user", { $toObjectId: req.params.id }] },
        status: { $ne: 'cancelled' }
      }
    },
    {
      $group: {
        _id: null,
        totalSpent: { $sum: '$total' }
      }
    }
  ]);
  const totalSpent = totalSpentResult.length > 0 ? totalSpentResult[0].totalSpent : 0;

  // Get average order value
  const avgOrderValue = totalOrders > 0 ? totalSpent / totalOrders : 0;

  // Get paginated orders
  const ordersSkip = (ordersPage - 1) * ordersLimit;
  const orders = await Order.find({ user: req.params.id })
    .sort('-createdAt')
    .skip(ordersSkip)
    .limit(ordersLimit)
    .populate({
      path: 'items.product',
      select: 'name images'
    })
    .populate({
      path: 'items.creator',
      select: 'name businessInfo.businessName'
    });

  // Get wishlist information
  const wishlist = await Wishlist.findOne({ user: req.params.id });
  let wishlistItems = [];
  let totalWishlistItems = 0;

  if (wishlist) {
    // Calculate total wishlist items
    totalWishlistItems = wishlist.products.length + wishlist.bales.length;

    // Get paginated wishlist items
    const wishlistSkip = (wishlistPage - 1) * wishlistLimit;
    const wishlistEnd = wishlistSkip + wishlistLimit;

    // Combine products and bales, sort by addedAt, and paginate
    const allWishlistItems = [
      ...wishlist.products.map(item => ({ ...item.toObject(), type: 'product' })),
      ...wishlist.bales.map(item => ({ ...item.toObject(), type: 'bale' }))
    ].sort((a, b) => new Date(b.addedAt) - new Date(a.addedAt));

    wishlistItems = allWishlistItems.slice(wishlistSkip, wishlistEnd);

    // Populate product and bale references
    if (wishlistItems.length > 0) {
      const productIds = wishlistItems
        .filter(item => item.type === 'product')
        .map(item => item.product);

      const baleIds = wishlistItems
        .filter(item => item.type === 'bale')
        .map(item => item.bale);

      if (productIds.length > 0) {
        try {
          if (!Product) Product = mongoose.model('Product');
          const products = await Product.find({ _id: { $in: productIds } })
            .select('name images price');

          wishlistItems = wishlistItems.map(item => {
            if (item.type === 'product') {
              const productData = products.find(p => p._id.toString() === item.product.toString());
              return { ...item, productDetails: productData };
            }
            return item;
          });
        } catch (error) {
          console.error('Error fetching products for wishlist:', error);
        }
      }

      if (baleIds.length > 0) {
        try {
          if (!Bale) Bale = mongoose.model('Bale');
          const bales = await Bale.find({ _id: { $in: baleIds } })
            .select('name images price');

          wishlistItems = wishlistItems.map(item => {
            if (item.type === 'bale') {
              const baleData = bales.find(b => b._id.toString() === item.bale.toString());
              return { ...item, baleDetails: baleData };
            }
            return item;
          });
        } catch (error) {
          console.error('Error fetching bales for wishlist:', error);
        }
      }
    }
  }

  // Get cart information
  let cart;
  try {
    if (!Cart) Cart = mongoose.model('Cart');
    cart = await Cart.findOne({ user: req.params.id })
      .populate({
        path: 'items.product',
        select: 'name images'
      })
      .populate({
        path: 'items.bale',
        select: 'name images'
      })
      .populate({
        path: 'items.creator',
        select: 'name businessInfo.businessName'
      });
  } catch (error) {
    console.error('Error fetching cart:', error);
    cart = null;
  }

  let cartItems = [];
  let cartItemCount = 0;
  let cartTotal = 0;

  if (cart) {
    cartItemCount = cart.items.length;
    cartTotal = cart.total;

    // Get paginated cart items
    const cartSkip = (cartPage - 1) * cartLimit;
    cartItems = cart.items.slice(cartSkip, cartSkip + cartLimit);
  }

  // Get review statistics
  const reviewCount = await Review.countDocuments({ user: req.params.id });
  const averageRating = await Review.aggregate([
    {
      $match: { $expr: { $eq: ["$user", { $toObjectId: req.params.id }] } }
    },
    {
      $group: {
        _id: null,
        averageRating: { $avg: '$rating' }
      }
    }
  ]);

  // Get first and last order dates
  const firstOrder = await Order.findOne({ user: req.params.id })
    .sort('createdAt')
    .limit(1);

  const lastOrder = await Order.findOne({ user: req.params.id })
    .sort('-createdAt')
    .limit(1);

  // Calculate days since last order
  const daysSinceLastOrder = lastOrder
    ? Math.floor((Date.now() - new Date(lastOrder.createdAt)) / (1000 * 60 * 60 * 24))
    : null;

  // Calculate days since registration
  const daysSinceRegistration = Math.floor((Date.now() - new Date(buyer.createdAt)) / (1000 * 60 * 60 * 24));

  // Get buyer preferences
  const preferences = buyer.preferences || {};
  const notificationPreferences = preferences.notificationPreferences || {};

  // Check if buyer has new arrivals preference
  const newArrivalsEnabled = notificationPreferences.newArrivals || false;

  // Get preferred categories for new arrivals
  const preferredCategories = preferences.categories || [];

  // Get new arrivals based on buyer preferences
  let newArrivals = [];
  try {
    // Check if Product model exists
    const models = mongoose.modelNames();
    if (models.includes('Product') && newArrivalsEnabled) {
      const ProductModel = mongoose.model('Product');

      // Define the date threshold for new arrivals (e.g., last 7 days)
      const newArrivalThreshold = new Date();
      newArrivalThreshold.setDate(newArrivalThreshold.getDate() - 7);

      // Build query for new arrivals
      const newArrivalsQuery = {
        createdAt: { $gte: newArrivalThreshold },
        status: 'approved'
      };

      // Add category filter if buyer has preferred categories
      if (preferredCategories.length > 0) {
        newArrivalsQuery.category = { $in: preferredCategories };
      }

      // Get new arrivals
      newArrivals = await ProductModel.find(newArrivalsQuery)
        .sort('-createdAt')
        .limit(5)
        .select('name images price discountPrice category createdAt')
        .populate('category', 'name');
    }
  } catch (error) {
    console.error('Error fetching new arrivals:', error);
  }

  res.status(200).json({
    status: 'success',
    data: {
      buyer,
      statistics: {
        orders: {
          total: totalOrders,
          completed: completedOrders,
          pending: pendingOrders,
          cancelled: cancelledOrders
        },
        spending: {
          totalSpent,
          averageOrderValue: avgOrderValue
        },
        wishlist: {
          totalItems: totalWishlistItems
        },
        cart: {
          totalItems: cartItemCount,
          totalValue: cartTotal
        },
        reviews: {
          total: reviewCount,
          averageRating: averageRating.length > 0 ? averageRating[0].averageRating : 0
        },
        activity: {
          firstOrderDate: firstOrder ? firstOrder.createdAt : null,
          lastOrderDate: lastOrder ? lastOrder.createdAt : null,
          daysSinceLastOrder,
          daysSinceRegistration
        }
      },
      preferences: {
        notificationPreferences: notificationPreferences,
        categories: preferredCategories,
        newArrivalsEnabled: newArrivalsEnabled
      },
      orders: {
        data: orders,
        page: ordersPage,
        limit: ordersLimit,
        total: totalOrders,
        totalPages: Math.ceil(totalOrders / ordersLimit)
      },
      wishlist: {
        data: wishlistItems,
        page: wishlistPage,
        limit: wishlistLimit,
        total: totalWishlistItems,
        totalPages: Math.ceil(totalWishlistItems / wishlistLimit)
      },
      cart: {
        data: cartItems,
        page: cartPage,
        limit: cartLimit,
        total: cartItemCount,
        totalPages: Math.ceil(cartItemCount / cartLimit)
      },
      newArrivals: {
        enabled: newArrivalsEnabled,
        data: newArrivals,
        total: newArrivals.length
      }
    }
  });
});

/**
 * Update buyer active status
 * @route PATCH /api/v1/admin/buyers/:id/status
 * @access Private (Admin only)
 */
exports.updateBuyerStatus = catchAsync(async (req, res, next) => {
  if (req.body.active === undefined) {
    return next(new AppError('Please provide active status', 400));
  }

  const buyer = await Buyer.findByIdAndUpdate(
    req.params.id,
    { active: req.body.active },
    {
      new: true,
      runValidators: true
    }
  ).select('-__v -password -passwordChangedAt -passwordResetToken -passwordResetExpires');

  if (!buyer) {
    return next(new AppError('No buyer found with that ID', 404));
  }

  res.status(200).json({
    status: 'success',
    data: {
      buyer
    }
  });
});

/**
 * Update buyer preferences
 * @route PATCH /api/v1/admin/buyers/:id/preferences
 * @access Private (Admin only)
 */
exports.updateBuyerPreferences = catchAsync(async (req, res, next) => {
  // Validate request body
  if (!req.body.preferences) {
    return next(new AppError('Please provide preferences to update', 400));
  }

  // Find buyer
  const buyer = await Buyer.findById(req.params.id);

  if (!buyer) {
    return next(new AppError('No buyer found with that ID', 404));
  }

  // Initialize preferences if they don't exist
  if (!buyer.preferences) {
    buyer.preferences = {};
  }

  // Update notification preferences
  if (req.body.preferences.notificationPreferences) {
    if (!buyer.preferences.notificationPreferences) {
      buyer.preferences.notificationPreferences = {};
    }

    // Update new arrivals preference
    if (req.body.preferences.notificationPreferences.newArrivals !== undefined) {
      buyer.preferences.notificationPreferences.newArrivals =
        req.body.preferences.notificationPreferences.newArrivals;
    }

    // Update other notification preferences
    const notificationTypes = ['orderUpdates', 'promotions', 'priceDrops', 'email', 'push'];
    notificationTypes.forEach(type => {
      if (req.body.preferences.notificationPreferences[type] !== undefined) {
        buyer.preferences.notificationPreferences[type] =
          req.body.preferences.notificationPreferences[type];
      }
    });
  }

  // Update preferred categories
  if (req.body.preferences.categories) {
    buyer.preferences.categories = req.body.preferences.categories;
  }

  // Update other preferences
  const preferenceFields = ['sizes', 'colors', 'priceRange'];
  preferenceFields.forEach(field => {
    if (req.body.preferences[field] !== undefined) {
      buyer.preferences[field] = req.body.preferences[field];
    }
  });

  // Save the updated buyer
  await buyer.save({ validateBeforeSave: false });

  // Return the updated buyer
  const updatedBuyer = await Buyer.findById(req.params.id)
    .select('-__v -password -passwordChangedAt -passwordResetToken -passwordResetExpires');

  res.status(200).json({
    status: 'success',
    data: {
      buyer: updatedBuyer
    }
  });
});

/**
 * Delete buyer
 * @route DELETE /api/v1/admin/buyers/:id
 * @access Private (Admin only)
 */
exports.deleteBuyer = catchAsync(async (req, res, next) => {
  // Find buyer
  const buyer = await Buyer.findById(req.params.id);

  if (!buyer) {
    return next(new AppError('No buyer found with that ID', 404));
  }

  // Check if buyer has orders, reviews, or wishlist items
  const orderCount = await Order.countDocuments({ user: req.params.id });
  const reviewCount = await Review.countDocuments({ user: req.params.id });
  const wishlist = await Wishlist.findOne({ user: req.params.id });
  const wishlistCount = wishlist ? wishlist.products.length + wishlist.bales.length : 0;

  // If buyer has associated data, don't allow deletion
  if (orderCount > 0 || reviewCount > 0 || wishlistCount > 0) {
    return next(
      new AppError(
        `Cannot delete buyer with existing data. Buyer has ${orderCount} orders, ${reviewCount} reviews, and ${wishlistCount} wishlist items.`,
        400
      )
    );
  }

  // Delete buyer
  await Buyer.findByIdAndDelete(req.params.id);

  res.status(204).json({
    status: 'success',
    data: null
  });
});
