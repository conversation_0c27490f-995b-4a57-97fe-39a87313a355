const AdminLog = require('../../models/adminLog.model');
const catchAsync = require('../../utils/catchAsync');
const AppError = require('../../utils/appError');

/**
 * Get all admin logs with pagination, filtering and sorting
 */
exports.getAllLogs = catchAsync(async (req, res, next) => {
  // Build query
  let query = {};

  // Filtering
  if (req.query.action) {
    query.action = req.query.action;
  }
  if (req.query.resourceType) {
    query.resourceType = req.query.resourceType;
  }
  if (req.query.admin) {
    query.admin = req.query.admin;
  }
  if (req.query.status) {
    query.status = req.query.status;
  }

  // Date range
  if (req.query.startDate || req.query.endDate) {
    query.createdAt = {};
    if (req.query.startDate) {
      query.createdAt.$gte = new Date(req.query.startDate);
    }
    if (req.query.endDate) {
      query.createdAt.$lte = new Date(req.query.endDate);
    }
  }

  // Pagination
  const page = req.query.page * 1 || 1;
  const limit = req.query.limit * 1 || 50;
  const skip = (page - 1) * limit;

  // Sorting
  const sortBy = req.query.sort || '-createdAt';

  // Execute query
  const logs = await AdminLog.find(query)
    .populate('adminDetails', 'name email adminLevel')
    .sort(sortBy)
    .skip(skip)
    .limit(limit);

  // Get total count
  const total = await AdminLog.countDocuments(query);

  res.status(200).json({
    status: 'success',
    results: logs.length,
    total,
    page,
    totalPages: Math.ceil(total / limit),
    data: {
      logs
    }
  });
});

/**
 * Get specific log by ID
 */
exports.getLog = catchAsync(async (req, res, next) => {
  const log = await AdminLog.findById(req.params.id)
    .populate('adminDetails', 'name email adminLevel');

  if (!log) {
    return next(new AppError('No log found with that ID', 404));
  }

  res.status(200).json({
    status: 'success',
    data: {
      log
    }
  });
});

/**
 * Get logs by resource
 */
exports.getResourceLogs = catchAsync(async (req, res, next) => {
  const { resourceType, resourceId } = req.params;

  const logs = await AdminLog.getLogsByResource(resourceType, resourceId);

  res.status(200).json({
    status: 'success',
    results: logs.length,
    data: {
      logs
    }
  });
});

/**
 * Get logs by admin
 */
exports.getAdminLogs = catchAsync(async (req, res, next) => {
  const logs = await AdminLog.getLogsByAdmin(req.params.adminId);

  res.status(200).json({
    status: 'success',
    results: logs.length,
    data: {
      logs
    }
  });
});

/**
 * Delete log (Super admin only)
 */
exports.deleteLog = catchAsync(async (req, res, next) => {
  // Check if user is super admin
  if (req.user.adminLevel !== 'super') {
    return next(new AppError('Only super admins can delete logs', 403));
  }

  const log = await AdminLog.findByIdAndDelete(req.params.id);

  if (!log) {
    return next(new AppError('No log found with that ID', 404));
  }

  res.status(204).json({
    status: 'success',
    data: null
  });
});