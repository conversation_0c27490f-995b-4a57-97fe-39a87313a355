const express = require('express');
const promotionController = require('../../controllers/creators/promotion.controller');
const authMiddleware = require('../../middleware/auth.middleware');

const router = express.Router();

// Public routes
router.get('/', promotionController.getAllPromotions);
router.get('/:id', promotionController.getPromotion);

// Protected routes
router.use(authMiddleware.protect);

// Creator only routes
router.use(authMiddleware.restrictTo('creator'));

// Get creator's promotions
router.get('/my/promotions', promotionController.getMyPromotions);

// Join, update, and leave promotions
router.post('/:id/join', promotionController.joinPromotion);
router.patch('/:id/products', promotionController.updatePromotionProducts);
router.delete('/:id/leave', promotionController.leavePromotion);

module.exports = router;
