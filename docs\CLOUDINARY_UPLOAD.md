# Cloudinary Upload Integration

This document explains the Cloudinary upload system that replaces the local file storage system.

## Overview

The application now uses Cloudinary for all file uploads, providing:
- ✅ **Reliable file storage** - No more missing file errors
- ✅ **Automatic image optimization** - Better performance
- ✅ **CDN delivery** - Faster loading times
- ✅ **Automatic transformations** - Resizing, format conversion
- ✅ **Better scalability** - No local storage limitations

## Configuration

### Environment Variables
Make sure these are set in your `.env` file:
```env
CLOUDINARY_CLOUD_NAME="djxjevjcm"
CLOUDINARY_API_KEY="836248778658842"
CLOUDINARY_API_SECRET="xA6adnakeuCP9ITarsi_bykjl4Q"
```

### Upload Presets
Different file types have different configurations:

#### Verification Documents
- **Folder**: `everyfash/verification`
- **Formats**: JPG, JPEG, PNG, PDF
- **Max Size**: 10MB
- **Use Case**: Business registration, ID documents

#### Product Images
- **Folder**: `everyfash/products`
- **Formats**: JPG, JPEG, PNG
- **Max Size**: 5MB
- **Transformations**: Max 1200x1200px, auto quality/format
- **Use Case**: Product photos

#### Bale Images
- **Folder**: `everyfash/bales`
- **Formats**: JPG, JPEG, PNG
- **Max Size**: 5MB
- **Transformations**: Max 1200x1200px, auto quality/format
- **Use Case**: Bale photos

#### Profile Photos
- **Folder**: `everyfash/profiles`
- **Formats**: JPG, JPEG, PNG
- **Max Size**: 2MB
- **Transformations**: 400x400px, face-centered crop
- **Use Case**: User profile pictures

#### Shop Media
- **Folder**: `everyfash/shop`
- **Formats**: JPG, JPEG, PNG
- **Max Size**: 3MB
- **Transformations**: Max 800x400px
- **Use Case**: Shop logos and banners

## API Endpoints

### Test Endpoints
Use these to verify the integration is working:

#### Single File Upload Test
```bash
POST /api/v1/test-upload/test-image
Content-Type: multipart/form-data

# Form data:
profilePhoto: [image file]
```

#### Multiple Files Upload Test
```bash
POST /api/v1/test-upload/test-multiple
Content-Type: multipart/form-data

# Form data:
verificationDocuments: [file1, file2, ...]
```

### Production Endpoints

#### Creator Verification Documents
```bash
PATCH /api/v1/creators/onboarding/business-info
PATCH /api/v1/creators/profile/business-info
Content-Type: multipart/form-data

# Form data:
verificationDocuments: [file1, file2, ...]
businessName: "Company Name"
# ... other fields
```

#### Product Images
```bash
POST /api/v1/creators/products
PATCH /api/v1/creators/products/:id
Content-Type: multipart/form-data

# Form data:
productImages: [image1, image2, ...]
name: "Product Name"
# ... other fields
```

#### Profile Photos
```bash
PATCH /api/v1/creators/profile
PATCH /api/v1/creators/profile/photo
PATCH /api/v1/buyers/profile
Content-Type: multipart/form-data

# Form data:
profilePhoto: [image file]
```

## Response Format

### Successful Upload Response
```json
{
  "status": "success",
  "data": {
    "user": {
      "photo": "https://res.cloudinary.com/djxjevjcm/image/upload/v*********0/everyfash/profiles/profile-*********0-*********.jpg"
    }
  }
}
```

### Error Responses
```json
{
  "status": "fail",
  "message": "File too large. Please check the file size limits."
}
```

```json
{
  "status": "fail", 
  "message": "Only jpg, jpeg, png files are allowed!"
}
```

## Frontend Integration

### Using FormData (Recommended)
```javascript
const formData = new FormData();
formData.append('profilePhoto', fileInput.files[0]);
formData.append('name', 'John Doe');

fetch('/api/v1/creators/profile', {
  method: 'PATCH',
  headers: {
    'Authorization': `Bearer ${token}`
  },
  body: formData
});
```

### Multiple Files
```javascript
const formData = new FormData();
Array.from(fileInput.files).forEach(file => {
  formData.append('verificationDocuments', file);
});

fetch('/api/v1/creators/profile/business-info', {
  method: 'PATCH',
  headers: {
    'Authorization': `Bearer ${token}`
  },
  body: formData
});
```

## File URL Format

Cloudinary URLs follow this pattern:
```
https://res.cloudinary.com/{cloud_name}/{resource_type}/upload/v{version}/{folder}/{public_id}.{format}
```

Example:
```
https://res.cloudinary.com/djxjevjcm/image/upload/v*********0/everyfash/profiles/profile-*********0-*********.jpg
```

## Image Transformations

### Automatic Optimizations
- **Format**: Auto-converted to WebP/AVIF when supported
- **Quality**: Automatically optimized for best size/quality ratio
- **Compression**: Lossless compression applied

### Manual Transformations
You can modify URLs to apply transformations:
```
# Original
https://res.cloudinary.com/.../image.jpg

# Resized to 300x300
https://res.cloudinary.com/.../w_300,h_300,c_fill/image.jpg

# Thumbnail with rounded corners
https://res.cloudinary.com/.../w_150,h_150,c_fill,r_max/image.jpg
```

## Migration from Local Storage

### What Changed
1. **File paths**: Now Cloudinary URLs instead of local paths
2. **Storage location**: Files stored on Cloudinary instead of `public/uploads/`
3. **Error handling**: Better error messages for upload failures

### Backward Compatibility
- Existing local file paths in the database will continue to work
- New uploads will use Cloudinary URLs
- No data migration required

## Troubleshooting

### Common Issues

#### "File upload failed. Please try again."
- Check Cloudinary credentials in `.env`
- Verify internet connection
- Check file size limits

#### "Only jpg, jpeg, png files are allowed!"
- Verify file extension
- Check file MIME type
- Ensure file is not corrupted

#### "File too large. Please check the file size limits."
- Compress images before upload
- Check specific limits for each file type
- Consider using different image format

### Debug Mode
Set `NODE_ENV=development` to see detailed error messages.

## Security

### File Validation
- File type validation by extension and MIME type
- File size limits enforced
- Malicious file detection

### Access Control
- All uploads require authentication
- Files organized by user/purpose in folders
- Public access only to approved files

## Performance

### Optimization Features
- Automatic image compression
- Format conversion (WebP, AVIF)
- CDN delivery worldwide
- Lazy loading support

### Best Practices
- Use appropriate image sizes
- Leverage automatic transformations
- Cache Cloudinary URLs on frontend
