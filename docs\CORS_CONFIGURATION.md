# Simple CORS Configuration

Simple and precise CORS setup for the Everyfash API.

## Configuration

**File**: `config/cors.config.js`

```javascript
const corsOptions = {
  origin: function (origin, callback) {
    // Allow requests with no origin (mobile apps, Postman, etc.)
    if (!origin) return callback(null, true);

    // In development, allow all origins
    if (process.env.NODE_ENV === 'development') {
      return callback(null, true);
    }

    // Production allowed origins
    const allowedOrigins = [
      'https://everyfash.com',
      'https://www.everyfash.com',
      'https://admin.everyfash.com',
      'https://creator.everyfash.com'
    ];

    if (allowedOrigins.includes(origin)) {
      callback(null, true);
    } else {
      callback(new Error('Not allowed by CORS'));
    }
  },

  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'PATCH', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Origin', 'X-Requested-With', 'Content-Type', 'Accept', 'Authorization'],
  optionsSuccessStatus: 200
};
```

## Environment Variables

```env
# Development mode (allows all origins)
NODE_ENV=development

# Custom origins (optional)
ALLOWED_ORIGINS=https://custom1.com,https://custom2.com
```

## Testing

```bash
# Test CORS
curl -X GET http://localhost:3000/api/v1/cors-test \
  -H "Origin: http://localhost:3000"
```

**Response:**
```json
{
  "status": "success",
  "message": "CORS is working!",
  "origin": "http://localhost:3000",
  "timestamp": "2024-01-01T12:00:00.000Z"
}
```

## Features

✅ **Development**: All origins allowed
✅ **Production**: Specific domains only
✅ **Credentials**: Cookies and auth headers supported
✅ **Simple**: No complex middleware or error handlers

That's it! Clean and simple CORS configuration.
