{"info": {"_postman_id": "b1c2d3e4-f5g6-7890-abcd-ef1234567890", "name": "Creator Bales API", "description": "API endpoints for creator bale management", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Get All Bales", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{CREATOR_AUTH_TOKEN}}", "type": "text"}], "url": {"raw": "{{BASE_URL}}/api/v1/creators/bales", "host": ["{{BASE_URL}}"], "path": ["api", "v1", "creators", "bales"]}, "description": "Get all bales for the logged-in creator"}, "response": []}, {"name": "Get Bale Stats", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{CREATOR_AUTH_TOKEN}}", "type": "text"}], "url": {"raw": "{{BASE_URL}}/api/v1/creators/bales/stats", "host": ["{{BASE_URL}}"], "path": ["api", "v1", "creators", "bales", "stats"]}, "description": "Get bale statistics for the logged-in creator"}, "response": []}, {"name": "Get <PERSON>le by ID", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{CREATOR_AUTH_TOKEN}}", "type": "text"}], "url": {"raw": "{{BASE_URL}}/api/v1/creators/bales/{{baleId}}", "host": ["{{BASE_URL}}"], "path": ["api", "v1", "creators", "bales", "{{baleId}}"]}, "description": "Get a specific bale by ID for the logged-in creator"}, "response": []}, {"name": "Create <PERSON><PERSON>", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{CREATOR_AUTH_TOKEN}}", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "name", "value": "Premium Denim Jeans Ba<PERSON>", "type": "text"}, {"key": "description", "value": "High-quality denim jeans bale with premium stitching and comfortable fit.", "type": "text"}, {"key": "basePrice", "value": "899.99", "type": "text"}, {"key": "country", "value": "Ghana", "type": "text"}, {"key": "totalItems", "value": "50", "type": "text"}, {"key": "weight", "value": "25.5", "type": "text"}, {"key": "condition", "value": "Good", "type": "text"}, {"key": "category", "value": "{{categoryId}}", "type": "text", "description": "ID of the Jeans category"}, {"key": "baleImages", "type": "file", "src": "/path/to/image1.jpg", "description": "First bale image"}, {"key": "baleImages", "type": "file", "src": "/path/to/image2.jpg", "description": "Second bale image"}, {"key": "highlights[]", "value": "Premium quality denim", "type": "text"}, {"key": "highlights[]", "value": "Assorted sizes", "type": "text"}, {"key": "highlights[]", "value": "Durable stitching", "type": "text"}, {"key": "tags[]", "value": "jeans", "type": "text"}, {"key": "tags[]", "value": "denim", "type": "text"}, {"key": "tags[]", "value": "wholesale", "type": "text"}, {"key": "dimensions[length]", "value": "100", "type": "text"}, {"key": "dimensions[width]", "value": "80", "type": "text"}, {"key": "dimensions[height]", "value": "60", "type": "text"}, {"key": "variations[0][size]", "value": "Small Bale", "type": "text"}, {"key": "variations[0][quantity]", "value": "5", "type": "text"}, {"key": "variations[0][price]", "value": "899.99", "type": "text"}, {"key": "variations[0][globalPrice]", "value": "999.99", "type": "text"}, {"key": "variations[1][size]", "value": "Medium Bale", "type": "text"}, {"key": "variations[1][quantity]", "value": "3", "type": "text"}, {"key": "variations[1][price]", "value": "1299.99", "type": "text"}, {"key": "variations[1][globalPrice]", "value": "1499.99", "type": "text"}, {"key": "relatedCategories[]", "value": "{{relatedCategoryId1}}", "type": "text", "description": "First related category"}, {"key": "relatedCategories[]", "value": "{{relatedCategoryId2}}", "type": "text", "description": "Second related category"}, {"key": "products[0][product]", "value": "{{productId1}}", "type": "text"}, {"key": "products[0][quantity]", "value": "10", "type": "text"}, {"key": "products[1][product]", "value": "{{productId2}}", "type": "text"}, {"key": "products[1][quantity]", "value": "15", "type": "text"}]}, "url": {"raw": "{{BASE_URL}}/api/v1/creators/bales", "host": ["{{BASE_URL}}"], "path": ["api", "v1", "creators", "bales"]}, "description": "Create a new bale with images"}, "response": []}, {"name": "Update <PERSON><PERSON>", "request": {"method": "PATCH", "header": [{"key": "Authorization", "value": "Bearer {{CREATOR_AUTH_TOKEN}}", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "name", "value": "Updated Premium Denim Jeans Bale", "type": "text"}, {"key": "description", "value": "Updated description with more details about the premium jeans bale.", "type": "text"}, {"key": "basePrice", "value": "999.99", "type": "text"}, {"key": "baleImages", "type": "file", "src": "/path/to/new_image.jpg", "description": "New bale image"}, {"key": "variations[0][_id]", "value": "{{variationId}}", "type": "text"}, {"key": "variations[0][quantity]", "value": "8", "type": "text"}, {"key": "variations[1][size]", "value": "Large Bale", "type": "text"}, {"key": "variations[1][quantity]", "value": "2", "type": "text"}, {"key": "variations[1][price]", "value": "1999.99", "type": "text"}, {"key": "variations[1][globalPrice]", "value": "2199.99", "type": "text"}, {"key": "condition", "value": "Excellent", "type": "text"}, {"key": "totalItems", "value": "60", "type": "text"}]}, "url": {"raw": "{{BASE_URL}}/api/v1/creators/bales/{{baleId}}", "host": ["{{BASE_URL}}"], "path": ["api", "v1", "creators", "bales", "{{baleId}}"]}, "description": "Update an existing bale"}, "response": []}, {"name": "Delete Bale", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{CREATOR_AUTH_TOKEN}}", "type": "text"}], "url": {"raw": "{{BASE_URL}}/api/v1/creators/bales/{{baleId}}", "host": ["{{BASE_URL}}"], "path": ["api", "v1", "creators", "bales", "{{baleId}}"]}, "description": "Delete a bale by ID"}, "response": []}, {"name": "<PERSON><PERSON> by Status", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{CREATOR_AUTH_TOKEN}}", "type": "text"}], "url": {"raw": "{{BASE_URL}}/api/v1/creators/bales?status=pending", "host": ["{{BASE_URL}}"], "path": ["api", "v1", "creators", "bales"], "query": [{"key": "status", "value": "pending"}]}, "description": "Get bales filtered by status (pending, active, inactive, rejected)"}, "response": []}, {"name": "<PERSON> Bales", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{CREATOR_AUTH_TOKEN}}", "type": "text"}], "url": {"raw": "{{BASE_URL}}/api/v1/creators/bales?search=jeans", "host": ["{{BASE_URL}}"], "path": ["api", "v1", "creators", "bales"], "query": [{"key": "search", "value": "jeans"}]}, "description": "Search bales by name, description, or country"}, "response": []}, {"name": "<PERSON><PERSON>", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{CREATOR_AUTH_TOKEN}}", "type": "text"}], "url": {"raw": "{{BASE_URL}}/api/v1/creators/bales?sort=-createdAt", "host": ["{{BASE_URL}}"], "path": ["api", "v1", "creators", "bales"], "query": [{"key": "sort", "value": "-createdAt"}]}, "description": "Sort bales by creation date (newest first)"}, "response": []}, {"name": "<PERSON><PERSON><PERSON>", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{CREATOR_AUTH_TOKEN}}", "type": "text"}], "url": {"raw": "{{BASE_URL}}/api/v1/creators/bales?page=1&limit=10", "host": ["{{BASE_URL}}"], "path": ["api", "v1", "creators", "bales"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}]}, "description": "Get paginated bales (10 per page)"}, "response": []}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "variable": [{"key": "BASE_URL", "value": "http://localhost:5000", "type": "string"}, {"key": "baleId", "value": "", "type": "string", "description": "ID of a bale to view, update, or delete"}, {"key": "categoryId", "value": "", "type": "string", "description": "ID of a category for the bale"}, {"key": "relatedCategoryId1", "value": "", "type": "string", "description": "ID of a related category"}, {"key": "relatedCategoryId2", "value": "", "type": "string", "description": "ID of another related category"}, {"key": "variationId", "value": "", "type": "string", "description": "ID of a variation to update"}, {"key": "productId1", "value": "", "type": "string", "description": "ID of a product to include in the bale"}, {"key": "productId2", "value": "", "type": "string", "description": "ID of another product to include in the bale"}]}