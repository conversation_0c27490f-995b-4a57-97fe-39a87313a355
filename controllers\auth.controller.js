const crypto = require('crypto');
const jwt = require('jsonwebtoken');
const passport = require('../config/passport');
const { BaseUser, Buyer, Creator, Admin } = require('../models/user.model');
const AppError = require('../utils/appError');
const catchAsync = require('../utils/catchAsync');
const authConfig = require('../config/auth.config');
const validator = require('validator'); // For email validation
const bcrypt = require('bcryptjs'); // For password hashing
const sendEmail = require('../utils/sendEmail'); // Assumed utility function for sending email


// Helper function to sign JWT token
const signToken = (id) => {
  return jwt.sign({ id }, authConfig.jwtSecret, {
    expiresIn: authConfig.jwtExpiresIn
  });
};

// Helper function to create and send token
const createSendToken = (user, statusCode, req, res) => {
  const token = signToken(user._id);
  const cookieOptions = {
    expires: new Date(
      Date.now() + authConfig.jwtCookieExpiresIn * 24 * 60 * 60 * 1000
    ),
    httpOnly: true,
    secure: req.secure || req.headers['x-forwarded-proto'] === 'https'
  };

  // Set JWT as cookie
  res.cookie('jwt', token, cookieOptions);

  // Remove password from output
  user.password = undefined;

  res.status(statusCode).json({
    status: 'success',
    token,
    data: {
      user
    }
  });
};

// Helper function to redirect to frontend with token for social auth
const redirectWithToken = (user, userType, req, res) => {
  const token = signToken(user._id);
  const cookieOptions = {
    expires: new Date(
      Date.now() + authConfig.jwtCookieExpiresIn * 24 * 60 * 60 * 1000
    ),
    httpOnly: true,
    secure: req.secure || req.headers['x-forwarded-proto'] === 'https',
    sameSite: 'lax'
  };

  // Set JWT as cookie
  res.cookie('jwt', token, cookieOptions);

  // Remove password from output (same as normal login)
  user.password = undefined;

  // Determine frontend URL based on user type
  let frontendURL;
  if (userType === 'buyer') {
    frontendURL = authConfig.frontend.buyerURL;
  } else if (userType === 'creator') {
    frontendURL = authConfig.frontend.creatorURL;
  } else {
    frontendURL = authConfig.frontend.buyerURL; // Default to buyer
  }

  // Create response data with same structure as normal login
  const responseData = {
    status: 'success',
    token,
    data: {
      user
    }
  };

  // Redirect to frontend with success parameters (same structure as normal login)
  const redirectURL = `${frontendURL}/auth/callback?success=true&data=${encodeURIComponent(JSON.stringify(responseData))}`;

  res.redirect(redirectURL);
};

// Helper function to validate password
const validatePassword = (password) => {
  const passwordRegex = /^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d@$!%*?&]{8,}$/;
  return passwordRegex.test(password); // Password must have at least 8 characters, including letters and numbers
};


// Register a new buyer
exports.registerBuyer = catchAsync(async (req, res, next) => {
  const { name, email, password, passwordConfirm } = req.body;

  // Validate email
  if (!validator.isEmail(email)) {
    return next(new AppError('Invalid email format', 400));
  }

  // Check if email already exists
  const existingUser = await Buyer.findOne({ email });
  if (existingUser) {
    return next(new AppError('An account with this email address already exists. Please use a different email or try logging in instead.', 400));
  }

  // Validate password strength
  if (!validatePassword(password)) {
    return next(
      new AppError(
        'Password is too weak. Please create a password that is at least 8 characters long and includes at least one letter and one number.',
        400
      )
    );
  }

  if (password !== passwordConfirm) {
    return next(new AppError('Passwords do not match. Please make sure both password fields are identical.', 400));
  }

  const newUser = await Buyer.create({
    name,
    email,
    password,
    passwordConfirm,
    role: 'buyer',
    emailVerified: false
  });

  // Generate email verification token
  const verificationToken = newUser.createEmailVerificationToken();
  await newUser.save({ validateBeforeSave: false });

  // Create verification URL pointing to frontend
  const frontendURL = authConfig.frontend.buyerURL;
  const verificationURL = `${frontendURL}/verify-email?token=${verificationToken}`;

  const htmlMessage = `
  <h2>Welcome to Everyfash!</h2>
  <p>Hi ${name},</p>
  <p>Thank you for registering as a buyer on Everyfash. Please click the link below to verify your email address:</p>
  <p><a href="${verificationURL}" style="color: #1a73e8; text-decoration: none; background-color: #f0f8ff; padding: 10px 20px; border-radius: 5px; display: inline-block;">Verify Email Address</a></p>
  <p>This link will expire in 24 hours for security reasons.</p>
  <p>If you didn't create this account, please ignore this email.</p>
  <p>Best regards,<br>The Everyfash Team</p>
`;

  try {
    // Send verification email
    await sendEmail({
      email: newUser.email,
      subject: 'Verify Your Email Address - Everyfash',
      message: htmlMessage,
    });

    res.status(201).json({
      status: 'success',
      message: 'Registration successful! Please check your email to verify your account.',
      data: {
        user: {
          id: newUser._id,
          name: newUser.name,
          email: newUser.email,
          role: newUser.role,
          emailVerified: newUser.emailVerified
        }
      }
    });
  } catch (err) {
    // If there was an error sending the email, remove the verification token
    newUser.emailVerificationToken = undefined;
    newUser.emailVerificationExpires = undefined;
    await newUser.save({ validateBeforeSave: false });

    return next(new AppError('There was an error sending the verification email. Please try again later!', 500));
  }
});




// Register a new creator
exports.registerCreator = catchAsync(async (req, res, next) => {
  const { name, email, password, passwordConfirm, bio, location } = req.body;

  // Validate email
  if (!validator.isEmail(email)) {
    return next(new AppError('Invalid email format', 400));
  }

  // Check if email already exists
  const existingUser = await Creator.findOne({ email });
  if (existingUser) {
    return next(new AppError('An account with this email address already exists. Please use a different email or try logging in instead.', 400));
  }

  // Validate password strength
  if (!validatePassword(password)) {
    return next(
      new AppError(
        'Password is too weak. Please create a password that is at least 8 characters long and includes at least one letter and one number.',
        400
      )
    );
  }

  if (password !== passwordConfirm) {
    return next(new AppError('Passwords do not match. Please make sure both password fields are identical.', 400));
  }

  const newUser = await Creator.create({
    name,
    email,
    password,
    passwordConfirm,
    role: 'creator',
    bio,
    location,
    emailVerified: false
  });

  // Generate email verification token
  const verificationToken = newUser.createEmailVerificationToken();
  await newUser.save({ validateBeforeSave: false });

  // Create verification URL pointing to frontend
  const frontendURL = authConfig.frontend.creatorURL;
  const verificationURL = `${frontendURL}/verify-email?token=${verificationToken}`;

  const htmlMessage = `
  <h2>Welcome to Everyfash Creator Platform!</h2>
  <p>Hi ${name},</p>
  <p>Thank you for registering as a creator on Everyfash. Please click the link below to verify your email address:</p>
  <p><a href="${verificationURL}" style="color: #1a73e8; text-decoration: none; background-color: #f0f8ff; padding: 10px 20px; border-radius: 5px; display: inline-block;">Verify Email Address</a></p>
  <p>This link will expire in 24 hours for security reasons.</p>
  <p>Once verified, you can start setting up your creator profile and begin selling on our platform.</p>
  <p>If you didn't create this account, please ignore this email.</p>
  <p>Best regards,<br>The Everyfash Team</p>
`;

  try {
    // Send verification email
    await sendEmail({
      email: newUser.email,
      subject: 'Verify Your Email Address - Everyfash Creator',
      message: htmlMessage,
    });

    res.status(201).json({
      status: 'success',
      message: 'Registration successful! Please check your email to verify your account.',
      data: {
        user: {
          id: newUser._id,
          name: newUser.name,
          email: newUser.email,
          role: newUser.role,
          emailVerified: newUser.emailVerified
        }
      }
    });
  } catch (err) {
    // If there was an error sending the email, remove the verification token
    newUser.emailVerificationToken = undefined;
    newUser.emailVerificationExpires = undefined;
    await newUser.save({ validateBeforeSave: false });

    return next(new AppError('There was an error sending the verification email. Please try again later!', 500));
  }
});



// Register a new admin (protected, only super admins can create new admins)
exports.registerAdmin = catchAsync(async (req, res, next) => {
  // Check if the current user is a super admin
  if (req.user.role !== 'admin' || req.user.adminLevel !== 'super') {
    return next(new AppError('You do not have permission to perform this action', 403));
  }

  const { name, email, password, passwordConfirm, adminLevel, department } = req.body;

  // Validate email
  if (!validator.isEmail(email)) {
    return next(new AppError('Invalid email format', 400));
  }

  // Check if email already exists
  const existingAdmin = await Admin.findOne({ email });
  if (existingAdmin) {
    return next(new AppError('Email is already registered', 400));
  }

  // Validate password strength
  if (!validatePassword(password)) {
    return next(
      new AppError(
        'Password must be at least 8 characters, include at least one letter and one number.',
        400
      )
    );
  }

  if (password !== passwordConfirm) {
    return next(new AppError('Passwords do not match', 400));
  }

  // Validate admin level and department
  const validAdminLevels = ['junior', 'senior', 'super'];
  if (!validAdminLevels.includes(adminLevel)) {
    return next(new AppError('Invalid admin level', 400));
  }

  const validDepartments = [
    'customer_support',
    'content_moderation',
    'finance',
    'operations',
    'technical',
    'marketing',
    'executive',
  ];
  if (!validDepartments.includes(department)) {
    return next(new AppError('Invalid department', 400));
  }

  const newAdmin = await Admin.create({
    name,
    email,
    password,
    passwordConfirm,
    role: 'admin',
    adminLevel: adminLevel || 'junior',
    department: department || 'customer_support',
  });

  createSendToken(newAdmin, 201, req, res);
});




// For backward compatibility
exports.signup = catchAsync(async (req, res, next) => {
  const { name, email, password, passwordConfirm, role } = req.body;

  // Validate email
  if (!validator.isEmail(email)) {
    return next(new AppError('Invalid email format', 400));
  }

  // Check if email already exists
  const existingUser = await BaseUser.findOne({ email });
  if (existingUser) {
    return next(new AppError('Email is already registered', 400));
  }

  // Validate password strength
  if (!validatePassword(password)) {
    return next(
      new AppError(
        'Password must be at least 8 characters, include at least one letter and one number.',
        400
      )
    );
  }

  if (password !== passwordConfirm) {
    return next(new AppError('Passwords do not match', 400));
  }

  let newUser;
  if (role === 'creator') {
    newUser = await Creator.create({
      name,
      email,
      password,
      passwordConfirm,
      role: 'creator',
    });
  } else {
    newUser = await Buyer.create({
      name,
      email,
      password,
      passwordConfirm,
      role: 'buyer',
    });
  }

  createSendToken(newUser, 201, req, res);
});


// Login with email and password
exports.login = catchAsync(async (req, res, next) => {
  passport.authenticate('local', { session: false }, async (err, user, info) => {
    if (err) {
      return next(err);
    }
    if (!user) {
      // Use the specific error message from passport strategy
      const message = info?.message || 'Authentication failed. Please check your credentials.';
      return next(new AppError(message, 401));
    }

    // Check if email is verified (skip for social auth users)
    if (!user.socialProvider && !user.emailVerified) {
      try {
        // Generate new verification token
        const verificationToken = user.createEmailVerificationToken();
        await user.save({ validateBeforeSave: false });

        // Determine frontend URL based on user role
        let frontendURL;
        if (user.role === 'creator') {
          frontendURL = authConfig.frontend.creatorURL;
        } else if (user.role === 'admin') {
          frontendURL = authConfig.frontend.adminURL;
        } else {
          frontendURL = authConfig.frontend.buyerURL; // Default to buyer
        }

        // Create verification URL pointing to frontend
        const verificationURL = `${frontendURL}/verify-email?token=${verificationToken}`;

        const htmlMessage = `
        <h2>Email Verification Required - Everyfash</h2>
        <p>Hi ${user.name},</p>
        <p>You attempted to log in, but your email address hasn't been verified yet. Please click the link below to verify your email address:</p>
        <p><a href="${verificationURL}" style="color: #1a73e8; text-decoration: none; background-color: #f0f8ff; padding: 10px 20px; border-radius: 5px; display: inline-block;">Verify Email Address</a></p>
        <p>This link will expire in 24 hours for security reasons.</p>
        <p>Once verified, you'll be able to log in to your account.</p>
        <p>Best regards,<br>The Everyfash Team</p>
      `;

        // Send verification email
        await sendEmail({
          email: user.email,
          subject: 'Email Verification Required - Everyfash',
          message: htmlMessage,
        });

        return res.status(401).json({
          status: 'fail',
          message: 'Your email address is not verified. We\'ve sent a new verification link to your email address. Please check your inbox and click the verification link to complete your account setup.',
          emailSent: true,
          userEmail: user.email,
          errorType: 'email_not_verified'
        });

      } catch (emailErr) {
        // If email sending fails, still show the original error
        return next(new AppError('Please verify your email address before logging in. If you need a new verification email, please use the resend verification option.', 401));
      }
    }

    createSendToken(user, 200, req, res);
  })(req, res, next);
});

// Logout user
exports.logout = (_, res) => {
  res.cookie('jwt', 'loggedout', {
    expires: new Date(Date.now() + 10 * 1000),
    httpOnly: true
  });
  res.status(200).json({ status: 'success' });
};



// Authentication middleware has been moved to middleware/auth.middleware.js


// Forgot password function
exports.forgotPassword = catchAsync(async (req, res, next) => {
  // 1) Get user based on POSTed email
  const user = await BaseUser.findOne({ email: req.body.email });
  if (!user) {
    return next(new AppError('No account found with this email address. Please check your email or create a new account.', 404));
  }

  // 2) Generate a reset token
  const resetToken = crypto.randomBytes(32).toString('hex'); // Generate a random reset token
  // Encrypt the token (optional, for additional security)
  const resetTokenHash = crypto
    .createHash('sha256')
    .update(resetToken)
    .digest('hex');

  // Set reset token and expiration time (1 hour expiration)
  user.passwordResetToken = resetTokenHash;
  user.passwordResetExpires = Date.now() + 60 * 60 * 1000; // Token expires in 1 hour

  await user.save({ validateBeforeSave: false });

  // 3) Determine frontend URL based on user role
  let frontendURL;
  if (user.role === 'creator') {
    frontendURL = authConfig.frontend.creatorURL;
  } else if (user.role === 'admin') {
    frontendURL = authConfig.frontend.adminURL;
  } else {
    frontendURL = authConfig.frontend.buyerURL; // Default to buyer
  }

  // Create reset URL pointing to frontend with token as parameter
  const resetURL = `${frontendURL}/reset-password?token=${resetToken}`;

  const htmlMessage = `
  <p>You requested a password reset. Please click the link below to reset your password (valid for 1 hour):</p>
  <p><a href="${resetURL}" style="color: #1a73e8; text-decoration: none;">Reset Password</a></p>
  <p>If you didn't request this, please ignore this email.</p>
  <p>This link will expire in 1 hour for security reasons.</p>
`;

  try {
    // Send an email with the reset URL (you would use a service like Nodemailer here)
    await sendEmail({
      email: user.email,
      subject: 'Password Reset Request',
      message: htmlMessage,
    });

    res.status(200).json({
      status: 'success',
      message: 'Token sent to email! Please check your inbox.',
    });
  } catch (err) {
    // If there was an error sending the email, reset the token and expiration date
    user.passwordResetToken = undefined;
    user.passwordResetExpires = undefined;
    await user.save({ validateBeforeSave: false });

    return next(new AppError('We encountered an issue sending the password reset email. Please try again in a few minutes or contact support if the problem persists.', 500));
  }
});



// Verify email
exports.verifyEmail = catchAsync(async (req, res, next) => {
  // 1) Get user based on verification token
  const hashedToken = crypto
    .createHash('sha256')
    .update(req.params.token)
    .digest('hex');

  const user = await BaseUser.findOne({
    emailVerificationToken: hashedToken,
    emailVerificationExpires: { $gt: Date.now() }
  });

  // 2) Check if token is valid and not expired
  if (!user) {
    return next(new AppError('Email verification link is invalid or has expired. Please request a new verification email.', 400));
  }

  // 3) Check if email is already verified
  if (user.emailVerified) {
    return next(new AppError('Your email address is already verified. You can proceed to log in.', 400));
  }

  // 4) Verify the email
  user.verifyEmail();
  await user.save({ validateBeforeSave: false });

  // 5) Generate JWT token and send response (same as login)
  createSendToken(user, 200, req, res);
});

// Resend email verification
exports.resendEmailVerification = catchAsync(async (req, res, next) => {
  // 1) Get user based on email
  const user = await BaseUser.findOne({ email: req.body.email });
  if (!user) {
    return next(new AppError('No account found with this email address. Please check your email or create a new account.', 404));
  }

  // 2) Check if email is already verified
  if (user.emailVerified) {
    return next(new AppError('Your email address is already verified. You can proceed to log in.', 400));
  }

  // 3) Generate new verification token
  const verificationToken = user.createEmailVerificationToken();
  await user.save({ validateBeforeSave: false });

  // 4) Determine frontend URL based on user role
  let frontendURL;
  if (user.role === 'creator') {
    frontendURL = authConfig.frontend.creatorURL;
  } else if (user.role === 'admin') {
    frontendURL = authConfig.frontend.adminURL;
  } else {
    frontendURL = authConfig.frontend.buyerURL; // Default to buyer
  }

  // 5) Create verification URL pointing to frontend
  const verificationURL = `${frontendURL}/verify-email?token=${verificationToken}`;

  const htmlMessage = `
  <h2>Email Verification - Everyfash</h2>
  <p>Hi ${user.name},</p>
  <p>You requested a new email verification link. Please click the link below to verify your email address:</p>
  <p><a href="${verificationURL}" style="color: #1a73e8; text-decoration: none; background-color: #f0f8ff; padding: 10px 20px; border-radius: 5px; display: inline-block;">Verify Email Address</a></p>
  <p>This link will expire in 24 hours for security reasons.</p>
  <p>If you didn't request this, please ignore this email.</p>
  <p>Best regards,<br>The Everyfash Team</p>
`;

  try {
    // Send verification email
    await sendEmail({
      email: user.email,
      subject: 'Email Verification - Everyfash',
      message: htmlMessage,
    });

    res.status(200).json({
      status: 'success',
      message: 'Verification email sent! Please check your inbox.',
    });
  } catch (err) {
    // If there was an error sending the email, remove the verification token
    user.emailVerificationToken = undefined;
    user.emailVerificationExpires = undefined;
    await user.save({ validateBeforeSave: false });

    return next(new AppError('We encountered an issue sending the verification email. Please try again in a few minutes or contact support if the problem persists.', 500));
  }
});

// Reset password
exports.resetPassword = catchAsync(async (req, res, next) => {
  const { password, passwordConfirm } = req.body;
  
  // 1) Get user based on reset token
  const hashedToken = crypto
    .createHash('sha256')
    .update(req.params.token)
    .digest('hex');

  const user = await BaseUser.findOne({
    passwordResetToken: hashedToken,
    passwordResetExpires: { $gt: Date.now() }
  });

  // 2) Check if token is valid and not expired
  if (!user) {
    return next(new AppError('Password reset link is invalid or has expired. Please request a new password reset link.', 400));
  }

  // 3) Validate passwords
  if (!password || !passwordConfirm) {
    return next(new AppError('Please provide both a new password and password confirmation.', 400));
  }

  if (password !== passwordConfirm) {
    return next(new AppError('New passwords do not match. Please make sure both password fields are identical.', 400));
  }

  // 4) Reset password using the model method
  await user.resetPassword(password);
  
  // 5) Save the changes (this will trigger the pre-save middleware for password hashing)
  await user.save();

  // 6) Log user in by sending JWT
  createSendToken(user, 200, req, res);
});



// Update password
exports.updatePassword = catchAsync(async (req, res, next) => {
  // 1) Get the user from the collection (including their current password field)
  const user = await BaseUser.findById(req.user.id).select('+password');

  if (!req.body.currentPassword || !req.body.password || !req.body.passwordConfirm) {
    return next(new AppError('Please provide your current password, new password, and password confirmation.', 400));
  }

  // 2) Check if the POSTed current password is correct
  if (!(await user.correctPassword(req.body.currentPassword, user.password))) {
    return next(new AppError('Your current password is incorrect. Please enter your current password correctly.', 401));
  }

  // 3) If the current password is correct, validate the new password and confirmation
  if (req.body.password !== req.body.passwordConfirm) {
    return next(new AppError('Your new passwords do not match. Please make sure both new password fields are identical.', 400));
  }

  // 4) Update the password field with the new password (ensure it gets hashed)
  user.password = req.body.password;
  user.passwordConfirm = req.body.passwordConfirm; // This is just for validation, we don't store it

  // 5) Save the updated user instance (the password will be hashed automatically if using middleware)
  await user.save();

  // 6) Log the user in immediately by generating a new JWT
  createSendToken(user, 200, req, res);
});

// Get current user
exports.getMe = catchAsync(async (req, res, next) => {
  // The user is already fetched and verified by the auth middleware
  // We just need to return it with the correct discriminated model
  let user = req.user;

  // Fetch the user with the correct discriminated model to get all fields
  if (user.role === 'buyer') {
    user = await Buyer.findById(user._id);
  } else if (user.role === 'creator') {
    user = await Creator.findById(user._id);
  } else if (user.role === 'admin') {
    user = await Admin.findById(user._id);
  }

  if (!user) {
    return next(new AppError('User not found', 404));
  }

  res.status(200).json({
    status: 'success',
    data: { user },
  });
});

exports.getUser = catchAsync(async (req, res, next) => {
  const user = await BaseUser.findById(req.params.id);
  if (!user) {
    return next(new AppError('User not found', 404));
  }

  res.status(200).json({
    status: 'success',
    data: { user },
  });
});



// Google authentication
// exports.googleAuth = passport.authenticate('google', { scope: ['profile', 'email'] });

// Google Buyer Authentication
exports.googleBuyerAuth = passport.authenticate('google-buyer', { scope: ['profile', 'email'] });

exports.googleBuyerCallback = (req, res, next) => {
  passport.authenticate('google-buyer', { session: false }, (err, user) => {
    if (err) {
      // Redirect to frontend with error
      const frontendURL = authConfig.frontend.buyerURL;
      return res.redirect(`${frontendURL}/auth/callback?success=false&error=${encodeURIComponent(err.message)}`);
    }
    if (!user) {
      // Redirect to frontend with error
      const frontendURL = authConfig.frontend.buyerURL;
      return res.redirect(`${frontendURL}/auth/callback?success=false&error=${encodeURIComponent('Authentication failed')}`);
    }
    redirectWithToken(user, 'buyer', req, res);
  })(req, res, next);
};


// Google Creator Authentication
exports.googleCreatorAuth = passport.authenticate('google-creator', { scope: ['profile', 'email'] });

exports.googleCreatorCallback = (req, res, next) => {
  passport.authenticate('google-creator', { session: false }, (err, user) => {
    if (err) {
      // Redirect to frontend with error
      const frontendURL = authConfig.frontend.creatorURL;
      return res.redirect(`${frontendURL}/auth/callback?success=false&error=${encodeURIComponent(err.message)}`);
    }
    if (!user) {
      // Redirect to frontend with error
      const frontendURL = authConfig.frontend.creatorURL;
      return res.redirect(`${frontendURL}/auth/callback?success=false&error=${encodeURIComponent('Authentication failed')}`);
    }
    redirectWithToken(user, 'creator', req, res);
  })(req, res, next);
};




// Facebook authentication
exports.facebookBuyerAuth = passport.authenticate('facebook-buyer', { scope: ['email'] });

exports.facebookCreatorAuth = passport.authenticate('facebook-creator', { scope: ['email'] });


// Callbacks
exports.facebookBuyerCallback = (req, res, next) => {
  passport.authenticate('facebook-buyer', { session: false }, (err, user) => {
    if (err) {
      // Redirect to frontend with error
      const frontendURL = authConfig.frontend.buyerURL;
      return res.redirect(`${frontendURL}/auth/callback?success=false&error=${encodeURIComponent(err.message)}`);
    }
    if (!user) {
      // Redirect to frontend with error
      const frontendURL = authConfig.frontend.buyerURL;
      return res.redirect(`${frontendURL}/auth/callback?success=false&error=${encodeURIComponent('Authentication failed')}`);
    }
    redirectWithToken(user, 'buyer', req, res);
  })(req, res, next);
};

exports.facebookCreatorCallback = (req, res, next) => {
  passport.authenticate('facebook-creator', { session: false }, (err, user) => {
    if (err) {
      // Redirect to frontend with error
      const frontendURL = authConfig.frontend.creatorURL;
      return res.redirect(`${frontendURL}/auth/callback?success=false&error=${encodeURIComponent(err.message)}`);
    }
    if (!user) {
      // Redirect to frontend with error
      const frontendURL = authConfig.frontend.creatorURL;
      return res.redirect(`${frontendURL}/auth/callback?success=false&error=${encodeURIComponent('Authentication failed')}`);
    }
    redirectWithToken(user, 'creator', req, res);
  })(req, res, next);
};


// Initialize super admin - this should only work when no admin exists
exports.initializeSuperAdmin = catchAsync(async (req, res, next) => {
  // Check if any admin already exists
  // const adminExists = await Admin.findOne({ role: 'admin' });
  // if (adminExists) {
  //   return next(new AppError('Super admin already exists. Cannot initialize another one.', 400));
  // }

  const { name, email, password, passwordConfirm } = req.body;

  // Validate required fields
  if (!name || !email || !password || !passwordConfirm) {
    return next(new AppError('Please provide all required fields', 400));
  }

  // Validate email
  if (!validator.isEmail(email)) {
    return next(new AppError('Invalid email format', 400));
  }

  // Create super admin
  const superAdmin = await Admin.create({
    name,
    email,
    password,
    passwordConfirm,
    role: 'admin',
    adminLevel: 'super',
    department: 'executive',
    permissions: {
      manageUsers: true,
      manageCreators: true,
      manageProducts: true,
      manageBales: true,
      manageOrders: true,
      managePayouts: true,
      managePromotions: true,
      manageCategories: true,
      viewReports: true,
      manageSettings: true,
      manageAdmins: true
    }
  });

  // Generate JWT token
  createSendToken(superAdmin, 201, req, res);
});




