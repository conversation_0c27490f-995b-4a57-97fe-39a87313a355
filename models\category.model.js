const mongoose = require('mongoose');
const slugify = require('slugify');

const categorySchema = new mongoose.Schema(
  {
    name: {
      type: String,
      required: [true, 'A category must have a name'],
      trim: true,
      maxlength: [1000, 'A category name must have less or equal than 100 characters']
    },
    slug: String,
    description: {
      type: String,
      trim: true
    },
    parent: {
      type: mongoose.Schema.ObjectId,
      ref: 'Category',
      default: null
    },
    image: String,
    icon: String,
    featured: {
      type: Boolean,
      default: false
    },
    order: {
      type: Number,
      default: 0
    },
    active: {
      type: Boolean,
      default: true
    }
  },
  {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true }
  }
);

// Indexes
categorySchema.index({ slug: 1 });
// categorySchema.index({ parent: 1 });
// Compound index to ensure uniqueness of name within a parent
categorySchema.index({ name: 1 }, { unique: true });

// Virtual populate for subcategories
categorySchema.virtual('subcategories', {
  ref: 'Category',
  foreignField: 'parent',
  localField: '_id'
});

// Virtual populate for products
categorySchema.virtual('products', {
  ref: 'Product',
  foreignField: 'category',
  localField: '_id'
});

// Document middleware: runs before .save() and .create()
categorySchema.pre('save', function(next) {
  this.slug = slugify(this.name, { lower: true });
  next();
});

// Query middleware
categorySchema.pre(/^find/, function(next) {
  this.find({ active: { $ne: false } });

  if (this._mongooseOptions.populate && this._mongooseOptions.populate.parent) {
    this.populate({
      path: 'parent',
      select: 'name description'
    });
  }

  next();
});

const Category = mongoose.model('Category', categorySchema);

module.exports = Category;
