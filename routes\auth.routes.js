const express = require('express');
const authController = require('../controllers/auth.controller');
const authMiddleware = require('../middleware/auth.middleware');


const router = express.Router();

// Super admin initialization (should be removed or secured in production)
// router.post('/init/super-admin', authController.initializeSuperAdmin);

// Registration routes
router.post('/register/buyer', authController.registerBuyer);
router.post('/register/creator', authController.registerCreator);

// For backward compatibility
router.post('/signup', authController.signup);

// Authentication routes
router.post('/login', authController.login);
router.get('/logout', authController.logout);


// Email verification
router.get('/verify-email/:token', authController.verifyEmail);
router.post('/resend-verification', authController.resendEmailVerification);

// Password management
router.post('/forgot-password', authController.forgotPassword);
router.patch('/reset-password/:token', authController.resetPassword);



// Social authentication routes
// Buyer Routes
router.get('/google/buyer', authController.googleBuyerAuth);
router.get('/google/buyer/callback', authController.googleBuyerCallback);

// Creator Routes
router.get('/google/creator', authController.googleCreatorAuth);
router.get('/google/creator/callback', authController.googleCreatorCallback);

// Facebook Buyer
router.get('/facebook/buyer', authController.facebookBuyerAuth);
router.get('/facebook/buyer/callback', authController.facebookBuyerCallback);

// Facebook Creator
router.get('/facebook/creator', authController.facebookCreatorAuth);
router.get('/facebook/creator/callback', authController.facebookCreatorCallback);

// Protect all routes after this middleware
router.use(authMiddleware.protect);

// Protected routes
router.patch('/update-Password', authController.updatePassword);
router.get('/me', authController.getMe);
// Admin registration requires super admin privileges  
// Temporarily comment out until we have proper admin authentication
router.post('/register/admin', authMiddleware.restrictToAdminLevel('super'), authController.registerAdmin);
// router.post('/register/admin', authController.registerAdmin);

module.exports = router;
