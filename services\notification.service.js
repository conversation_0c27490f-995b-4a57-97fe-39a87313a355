const Notification = require('../models/notification.model');
const BaseUser = require('../models/baseUser.model');
const mongoose = require('mongoose');

/**
 * Notification Service
 * Handles all notification-related operations
 */
class NotificationService {
  /**
   * Create a notification
   * @param {Object} notificationData - Notification data
   * @returns {Promise<Object>} - Created notification
   */
  static async createNotification(notificationData) {
    try {
      // Check if recipient exists
      const recipient = await BaseUser.findById(notificationData.recipient);
      if (!recipient) {
        throw new Error(`Recipient not found: ${notificationData.recipient}`);
      }

      // Set delivery channels based on recipient preferences
      const deliveryChannels = {
        inApp: true, // Always send in-app notifications
        email: false,
        push: false,
        sms: false
      };

      // Check recipient type and set delivery channels accordingly
      if (recipient.role === 'buyer') {
        try {
          const Buyer = mongoose.model('Buyer');
          const buyer = await Buyer.findOne({ _id: recipient._id });

          if (buyer && buyer.preferences && buyer.preferences.notificationPreferences) {
            const prefs = buyer.preferences.notificationPreferences;

            // Set email preference
            if (prefs.email) {
              deliveryChannels.email = true;
            }

            // Set push preference
            if (prefs.push) {
              deliveryChannels.push = true;
            }

            // Check notification type-specific preferences
            if (notificationData.type.includes('order') && !prefs.orderUpdates) {
              deliveryChannels.email = false;
              deliveryChannels.push = false;
            } else if (notificationData.type.includes('promotion') && !prefs.promotions) {
              deliveryChannels.email = false;
              deliveryChannels.push = false;
            } else if (notificationData.type === 'price_drop' && !prefs.priceDrops) {
              deliveryChannels.email = false;
              deliveryChannels.push = false;
            } else if (notificationData.type.includes('new_') && !prefs.newArrivals) {
              deliveryChannels.email = false;
              deliveryChannels.push = false;
            }
          }
        } catch (error) {
          console.error('Error getting buyer preferences:', error);
        }
      } else if (recipient.role === 'creator') {
        try {
          const Creator = mongoose.model('Creator');
          const creator = await Creator.findOne({ _id: recipient._id });

          if (creator && creator.notificationPreferences) {
            const prefs = creator.notificationPreferences;

            // Set email preference
            if (prefs.email) {
              deliveryChannels.email = true;
            }

            // Set push preference
            if (prefs.push) {
              deliveryChannels.push = true;
            }

            // Check notification type-specific preferences
            if (notificationData.type.includes('order') && !prefs.newOrders) {
              deliveryChannels.email = false;
              deliveryChannels.push = false;
            } else if (notificationData.type.includes('stock') && !prefs.lowStock) {
              deliveryChannels.email = false;
              deliveryChannels.push = false;
            } else if (notificationData.type.includes('review') && !prefs.reviews) {
              deliveryChannels.email = false;
              deliveryChannels.push = false;
            } else if (notificationData.type.includes('payout') && !prefs.payouts) {
              deliveryChannels.email = false;
              deliveryChannels.push = false;
            }
          }
        } catch (error) {
          console.error('Error getting creator preferences:', error);
        }
      } else if (recipient.role === 'admin') {
        try {
          const Admin = mongoose.model('Admin');
          const admin = await Admin.findOne({ _id: recipient._id });

          if (admin && admin.notificationPreferences) {
            const prefs = admin.notificationPreferences;

            // Set email preference
            if (prefs.email) {
              deliveryChannels.email = true;
            }

            // Set push preference
            if (prefs.push) {
              deliveryChannels.push = true;
            }

            // Check notification type-specific preferences
            if (notificationData.type.includes('creator_') && !prefs.creatorVerifications) {
              deliveryChannels.email = false;
              deliveryChannels.push = false;
            } else if (notificationData.type.includes('order') && !prefs.highValueOrders) {
              deliveryChannels.email = false;
              deliveryChannels.push = false;
            } else if (notificationData.type.includes('system') && !prefs.systemAlerts) {
              deliveryChannels.email = false;
              deliveryChannels.push = false;
            }
          }
        } catch (error) {
          console.error('Error getting admin preferences:', error);
        }
      }

      // Create notification with delivery channels
      const notification = await Notification.create({
        ...notificationData,
        deliveryChannels
      });

      // Send notification through appropriate channels
      this.sendNotification(notification);

      return notification;
    } catch (error) {
      console.error('Error creating notification:', error);
      throw error;
    }
  }

  /**
   * Send notification through appropriate channels
   * @param {Object} notification - Notification object
   * @returns {Promise<void>}
   */
  static async sendNotification(notification) {
    try {
      // In-app notification is already sent by creating the notification

      // Send email notification if enabled
      if (notification.deliveryChannels.email) {
        try {
          await this.sendEmailNotification(notification);
          await notification.markAsEmailSent();
        } catch (error) {
          console.error('Error sending email notification:', error);
        }
      }

      // Send push notification if enabled
      if (notification.deliveryChannels.push) {
        try {
          await this.sendPushNotification(notification);
          await notification.markAsPushSent();
        } catch (error) {
          console.error('Error sending push notification:', error);
        }
      }

      // Send SMS notification if enabled
      if (notification.deliveryChannels.sms) {
        try {
          await this.sendSMSNotification(notification);
          await notification.markAsSMSSent();
        } catch (error) {
          console.error('Error sending SMS notification:', error);
        }
      }
    } catch (error) {
      console.error('Error sending notification:', error);
    }
  }

  /**
   * Send email notification
   * @param {Object} notification - Notification object
   * @returns {Promise<void>}
   */
  static async sendEmailNotification(notification) {
    try {
      // Get recipient email
      const recipient = await BaseUser.findById(notification.recipient);
      if (!recipient || !recipient.email) {
        throw new Error('Recipient email not found');
      }

      // Use EmailService to send the email
      const EmailService = require('./email.service');
      await EmailService.sendNotificationEmail(notification, recipient.email);

      return Promise.resolve();
    } catch (error) {
      console.error('Error sending email notification:', error);
      throw error;
    }
  }

  /**
   * Send push notification
   * @param {Object} notification - Notification object
   * @returns {Promise<void>}
   */
  static async sendPushNotification(notification) {
    try {
      // Use PushService to send the push notification
      const PushService = require('./push.service');

      // Prepare notification payload
      const pushPayload = {
        title: notification.title,
        body: notification.message,
        data: {
          notificationId: notification._id.toString(),
          type: notification.type,
          url: notification.data?.url || '',
          ...notification.data?.additionalData
        }
      };

      // Send to user
      await PushService.sendToUser(notification.recipient, pushPayload);

      return Promise.resolve();
    } catch (error) {
      console.error('Error sending push notification:', error);
      throw error;
    }
  }

  /**
   * Send SMS notification
   * @param {Object} notification - Notification object
   * @returns {Promise<void>}
   */
  static async sendSMSNotification(notification) {
    try {
      // Get recipient phone number
      const recipient = await BaseUser.findById(notification.recipient);
      if (!recipient) {
        throw new Error('Recipient not found');
      }

      // Get phone number from user
      let phoneNumber;

      if (recipient.role === 'buyer') {
        try {
          const Buyer = mongoose.model('Buyer');
          const buyer = await Buyer.findById(recipient._id);
          phoneNumber = buyer?.phone || buyer?.shippingAddress?.phone;
        } catch (err) {
          console.error('Error getting buyer phone number:', err);
        }
      } else if (recipient.role === 'creator') {
        try {
          const Creator = mongoose.model('Creator');
          const creator = await Creator.findById(recipient._id);
          phoneNumber = creator?.phone || creator?.shopInfo?.contact || creator?.businessInfo?.phone;
        } catch (err) {
          console.error('Error getting creator phone number:', err);
        }
      }

      if (!phoneNumber) {
        throw new Error('Recipient phone number not found');
      }

      // Use SMSService to send the SMS
      const SMSService = require('./sms.service');
      await SMSService.sendNotificationSMS(notification, phoneNumber);

      return Promise.resolve();
    } catch (error) {
      console.error('Error sending SMS notification:', error);
      throw error;
    }
  }

  /**
   * Get notifications for a user
   * @param {String} userId - User ID
   * @param {Object} options - Options for pagination, filtering, etc.
   * @returns {Promise<Object>} - Notifications and pagination info
   */
  static async getNotifications(userId, options = {}) {
    try {
      return await Notification.getForUser(userId, options);
    } catch (error) {
      console.error('Error getting notifications:', error);
      throw error;
    }
  }

  /**
   * Get unread notifications count for a user
   * @param {String} userId - User ID
   * @returns {Promise<Number>} - Unread notifications count
   */
  static async getUnreadCount(userId) {
    try {
      return await Notification.getUnreadCount(userId);
    } catch (error) {
      console.error('Error getting unread count:', error);
      throw error;
    }
  }

  /**
   * Mark a notification as read
   * @param {String} notificationId - Notification ID
   * @param {String} userId - User ID (for security check)
   * @returns {Promise<Object>} - Updated notification
   */
  static async markAsRead(notificationId, userId) {
    try {
      const notification = await Notification.findOne({
        _id: notificationId,
        recipient: userId
      });

      if (!notification) {
        throw new Error('Notification not found or not authorized');
      }

      return await notification.markAsRead();
    } catch (error) {
      console.error('Error marking notification as read:', error);
      throw error;
    }
  }

  /**
   * Mark all notifications as read for a user
   * @param {String} userId - User ID
   * @returns {Promise<Number>} - Number of notifications marked as read
   */
  static async markAllAsRead(userId) {
    try {
      return await Notification.markAllAsRead(userId);
    } catch (error) {
      console.error('Error marking all notifications as read:', error);
      throw error;
    }
  }

  /**
   * Delete a notification
   * @param {String} notificationId - Notification ID
   * @param {String} userId - User ID (for security check)
   * @returns {Promise<Object>} - Deletion result
   */
  static async deleteNotification(notificationId, userId) {
    try {
      const result = await Notification.deleteOne({
        _id: notificationId,
        recipient: userId
      });

      if (result.deletedCount === 0) {
        throw new Error('Notification not found or not authorized');
      }

      return { success: true, message: 'Notification deleted successfully' };
    } catch (error) {
      console.error('Error deleting notification:', error);
      throw error;
    }
  }

  /**
   * Create order notification
   * @param {Object} orderData - Order data
   * @returns {Promise<Object>} - Created notification
   */
  static async createOrderNotification(orderData) {
    try {
      const { order, type, recipient } = orderData;

      let title, message;

      switch (type) {
        case 'order_placed':
          title = 'New Order Received';
          message = `You have received a new order #${order.orderNumber} for ${order.total.toFixed(2)} ${order.currency || 'GHS'}`;
          break;
        case 'order_status_update':
          title = 'Order Status Updated';
          message = `Your order #${order.orderNumber} status has been updated to ${order.status}`;
          break;
        case 'order_shipped':
          title = 'Order Shipped';
          message = `Your order #${order.orderNumber} has been shipped`;
          break;
        case 'order_delivered':
          title = 'Order Delivered';
          message = `Your order #${order.orderNumber} has been delivered`;
          break;
        case 'order_cancelled':
          title = 'Order Cancelled';
          message = `Your order #${order.orderNumber} has been cancelled`;
          break;
        case 'order_refunded':
          title = 'Order Refunded';
          message = `Your order #${order.orderNumber} has been refunded`;
          break;
        default:
          title = 'Order Update';
          message = `Your order #${order.orderNumber} has been updated`;
      }

      return await this.createNotification({
        recipient,
        type,
        title,
        message,
        data: {
          order: order._id,
          url: `/orders/${order._id}`
        },
        priority: type === 'order_cancelled' || type === 'order_refunded' ? 'high' : 'medium'
      });
    } catch (error) {
      console.error('Error creating order notification:', error);
      throw error;
    }
  }

  /**
   * Create payment notification
   * @param {Object} paymentData - Payment data
   * @returns {Promise<Object>} - Created notification
   */
  static async createPaymentNotification(paymentData) {
    try {
      const { payment, type, recipient } = paymentData;

      let title, message;

      switch (type) {
        case 'payment_received':
          title = 'Payment Received';
          message = `Your payment of ${payment.amount.toFixed(2)} ${payment.currency || 'GHS'} has been received`;
          break;
        case 'payment_failed':
          title = 'Payment Failed';
          message = `Your payment of ${payment.amount.toFixed(2)} ${payment.currency || 'GHS'} has failed`;
          break;
        case 'payment_refunded':
          title = 'Payment Refunded';
          message = `Your payment of ${payment.amount.toFixed(2)} ${payment.currency || 'GHS'} has been refunded`;
          break;
        default:
          title = 'Payment Update';
          message = `Your payment of ${payment.amount.toFixed(2)} ${payment.currency || 'GHS'} has been updated`;
      }

      return await this.createNotification({
        recipient,
        type,
        title,
        message,
        data: {
          payment: payment._id,
          order: payment.order,
          url: `/orders/${payment.order}`
        },
        priority: type === 'payment_failed' ? 'high' : 'medium'
      });
    } catch (error) {
      console.error('Error creating payment notification:', error);
      throw error;
    }
  }

  /**
   * Create product notification
   * @param {Object} productData - Product data
   * @returns {Promise<Object>} - Created notification
   */
  static async createProductNotification(productData) {
    try {
      const { product, type, recipient } = productData;

      let title, message;

      switch (type) {
        case 'product_approved':
          title = 'Product Approved';
          message = `Your product "${product.name}" has been approved`;
          break;
        case 'product_rejected':
          title = 'Product Rejected';
          message = `Your product "${product.name}" has been rejected`;
          break;
        case 'low_stock':
          title = 'Low Stock Alert';
          message = `Your product "${product.name}" is running low on stock`;
          break;
        case 'out_of_stock':
          title = 'Out of Stock Alert';
          message = `Your product "${product.name}" is now out of stock`;
          break;
        case 'price_drop':
          title = 'Price Drop Alert';
          message = `The price of "${product.name}" has dropped`;
          break;
        case 'product_featured':
          title = 'Product Featured';
          message = `Your product "${product.name}" has been featured`;
          break;
        default:
          title = 'Product Update';
          message = `Your product "${product.name}" has been updated`;
      }

      return await this.createNotification({
        recipient,
        type,
        title,
        message,
        data: {
          product: product._id,
          url: `/products/${product._id}`
        },
        priority: type === 'out_of_stock' ? 'high' : 'medium'
      });
    } catch (error) {
      console.error('Error creating product notification:', error);
      throw error;
    }
  }

  /**
   * Create wallet notification
   * @param {Object} walletData - Wallet data
   * @returns {Promise<Object>} - Created notification
   */
  static async createWalletNotification(walletData) {
    try {
      const { wallet, transaction, type, recipient, amount } = walletData;

      let title, message;

      switch (type) {
        case 'wallet_transaction':
          const transactionType = transaction.amount > 0 ? 'credit' : 'debit';
          title = `Wallet ${transactionType.charAt(0).toUpperCase() + transactionType.slice(1)}`;
          message = `Your wallet has been ${transactionType}ed with ${Math.abs(transaction.amount).toFixed(2)} ${wallet.currency || 'GHS'}`;
          break;
        case 'wallet_low_balance':
          title = 'Low Wallet Balance';
          message = `Your wallet balance is low. Current balance: ${wallet.balance.toFixed(2)} ${wallet.currency || 'GHS'}`;
          break;
        case 'wallet_adjustment':
          title = 'Wallet Adjustment';
          message = `Your wallet has been adjusted by ${amount.toFixed(2)} ${wallet.currency || 'GHS'}`;
          break;
        default:
          title = 'Wallet Update';
          message = `Your wallet has been updated. Current balance: ${wallet.balance.toFixed(2)} ${wallet.currency || 'GHS'}`;
      }

      return await this.createNotification({
        recipient,
        type,
        title,
        message,
        data: {
          wallet: wallet._id,
          transaction: transaction ? transaction._id : undefined,
          url: `/wallet`
        },
        priority: type === 'wallet_low_balance' ? 'high' : 'medium'
      });
    } catch (error) {
      console.error('Error creating wallet notification:', error);
      throw error;
    }
  }

  /**
   * Create payout notification
   * @param {Object} payoutData - Payout data
   * @returns {Promise<Object>} - Created notification
   */
  static async createPayoutNotification(payoutData) {
    try {
      const { payout, type, recipient } = payoutData;

      let title, message;

      switch (type) {
        case 'payout_processed':
          title = 'Payout Processed';
          message = `Your payout of ${payout.amount.toFixed(2)} ${payout.currency || 'GHS'} has been processed`;
          break;
        case 'payout_failed':
          title = 'Payout Failed';
          message = `Your payout of ${payout.amount.toFixed(2)} ${payout.currency || 'GHS'} has failed`;
          break;
        case 'payout_initiated':
          title = 'Payout Initiated';
          message = `Your payout of ${payout.amount.toFixed(2)} ${payout.currency || 'GHS'} has been initiated`;
          break;
        default:
          title = 'Payout Update';
          message = `Your payout of ${payout.amount.toFixed(2)} ${payout.currency || 'GHS'} has been updated`;
      }

      return await this.createNotification({
        recipient,
        type,
        title,
        message,
        data: {
          payout: payout._id,
          url: `/payouts/${payout._id}`
        },
        priority: type === 'payout_failed' ? 'high' : 'medium'
      });
    } catch (error) {
      console.error('Error creating payout notification:', error);
      throw error;
    }
  }
}

module.exports = NotificationService;
