const express = require('express');
const checkoutController = require('../../controllers/buyers/checkout.controller');
const authMiddleware = require('../../middleware/auth.middleware');

const router = express.Router();

// Protect routes (except verify-payment which is a webhook)
router.use(authMiddleware.protect);
router.use(authMiddleware.restrictTo('buyer'));

// Checkout routes
router.get('/', checkoutController.getCheckoutSummary);
router.post('/order', checkoutController.createOrder);
router.post('/payment', checkoutController.processPayment);

module.exports = router;
