const Order = require('../../models/order.model');
const Product = require('../../models/product.model');
const { Creator, Buyer } = require('../../models/user.model');
const AppError = require('../../utils/appError');
const catchAsync = require('../../utils/catchAsync');

/**
 * Get all orders
 * @route GET /api/v1/admin/orders
 * @access Private (Admin only)
 */
exports.getAllOrders = catchAsync(async (req, res, next) => {
  // Build query
  const queryObj = { ...req.query };
  const excludedFields = ['page', 'sort', 'limit', 'fields', 'search'];
  excludedFields.forEach(el => delete queryObj[el]);

  // Advanced filtering
  let queryStr = JSON.stringify(queryObj);
  queryStr = queryStr.replace(/\b(gte|gt|lte|lt)\b/g, match => `$${match}`);
  
  let query = Order.find(JSON.parse(queryStr));

  // Search functionality
  if (req.query.search) {
    const searchRegex = new RegExp(req.query.search, 'i');
    query = query.find({
      $or: [
        { trackingNumber: searchRegex },
        { 'shippingAddress.name': searchRegex },
        { 'shippingAddress.city': searchRegex }
      ]
    });
  }

  // Filter by status if specified
  if (req.query.status) {
    query = query.find({ status: req.query.status });
  }

  // Filter by payment status if specified
  if (req.query.isPaid) {
    query = query.find({ isPaid: req.query.isPaid === 'true' });
  }

  // Filter by delivery status if specified
  if (req.query.isDelivered) {
    query = query.find({ isDelivered: req.query.isDelivered === 'true' });
  }

  // Count total before applying pagination
  const total = await Order.countDocuments(query);

  // Sorting
  if (req.query.sort) {
    const sortBy = req.query.sort.split(',').join(' ');
    query = query.sort(sortBy);
  } else {
    query = query.sort('-createdAt');
  }

  // Field limiting
  if (req.query.fields) {
    const fields = req.query.fields.split(',').join(' ');
    query = query.select(fields);
  } else {
    query = query.select('-__v');
  }

  // Pagination
  const page = req.query.page * 1 || 1;
  const limit = req.query.limit * 1 || 20;
  const skip = (page - 1) * limit;

  query = query.skip(skip).limit(limit);

  // Execute query
  const orders = await query;

  res.status(200).json({
    status: 'success',
    results: orders.length,
    total,
    page,
    limit,
    data: {
      orders
    }
  });
});

/**
 * Get order by ID
 * @route GET /api/v1/admin/orders/:id
 * @access Private (Admin only)
 */
exports.getOrder = catchAsync(async (req, res, next) => {
  const order = await Order.findById(req.params.id);

  if (!order) {
    return next(new AppError('No order found with that ID', 404));
  }

  res.status(200).json({
    status: 'success',
    data: {
      order
    }
  });
});

/**
 * Update order status
 * @route PATCH /api/v1/admin/orders/:id/status
 * @access Private (Admin only)
 */
exports.updateOrderStatus = catchAsync(async (req, res, next) => {
  // Validate request body
  if (!req.body.status || !['pending', 'processing', 'shipped', 'delivered', 'cancelled'].includes(req.body.status)) {
    return next(new AppError('Please provide a valid status (pending, processing, shipped, delivered, cancelled)', 400));
  }

  // Find order
  const order = await Order.findById(req.params.id);

  if (!order) {
    return next(new AppError('No order found with that ID', 404));
  }

  // Update order status
  order.status = req.body.status;

  // Update delivery status if order is delivered
  if (req.body.status === 'delivered') {
    order.isDelivered = true;
    order.deliveredAt = Date.now();
  }

  // Add notes if provided
  if (req.body.notes) {
    order.notes = req.body.notes;
  }

  // Save the updated order
  await order.save();

  // If order is cancelled, restore product stock
  if (req.body.status === 'cancelled') {
    for (const item of order.items) {
      if (item.product) {
        const product = await Product.findById(item.product);
        if (product) {
          // Find the specific variation
          const variationIndex = product.variations.findIndex(
            v => v.color === item.color && v.size === item.size
          );

          if (variationIndex !== -1) {
            // Restore stock
            product.variations[variationIndex].quantity += item.quantity;
            await product.save();
          }
        }
      }
    }
  }

  res.status(200).json({
    status: 'success',
    data: {
      order
    }
  });
});

/**
 * Update order tracking information
 * @route PATCH /api/v1/admin/orders/:id/tracking
 * @access Private (Admin only)
 */
exports.updateOrderTracking = catchAsync(async (req, res, next) => {
  // Validate request body
  if (!req.body.trackingNumber) {
    return next(new AppError('Please provide a tracking number', 400));
  }

  // Find order
  const order = await Order.findById(req.params.id);

  if (!order) {
    return next(new AppError('No order found with that ID', 404));
  }

  // Update tracking information
  order.trackingNumber = req.body.trackingNumber;
  
  // Update status to shipped if not already shipped or delivered
  if (order.status !== 'shipped' && order.status !== 'delivered') {
    order.status = 'shipped';
  }

  // Add shipping carrier if provided
  if (req.body.shippingCarrier) {
    order.shippingCarrier = req.body.shippingCarrier;
  }

  // Add estimated delivery date if provided
  if (req.body.estimatedDeliveryDate) {
    order.estimatedDeliveryDate = new Date(req.body.estimatedDeliveryDate);
  }

  // Save the updated order
  await order.save();

  res.status(200).json({
    status: 'success',
    data: {
      order
    }
  });
});

/**
 * Mark order as paid
 * @route PATCH /api/v1/admin/orders/:id/mark-paid
 * @access Private (Admin only)
 */
exports.markOrderAsPaid = catchAsync(async (req, res, next) => {
  // Find order
  const order = await Order.findById(req.params.id);

  if (!order) {
    return next(new AppError('No order found with that ID', 404));
  }

  // Update payment status
  order.isPaid = true;
  order.paidAt = Date.now();

  // Add payment result if provided
  if (req.body.paymentResult) {
    order.paymentResult = {
      id: req.body.paymentResult.id || 'manual',
      status: req.body.paymentResult.status || 'completed',
      update_time: req.body.paymentResult.update_time || new Date().toISOString(),
      email_address: req.body.paymentResult.email_address || ''
    };
  } else {
    order.paymentResult = {
      id: 'manual',
      status: 'completed',
      update_time: new Date().toISOString(),
      email_address: ''
    };
  }

  // Save the updated order
  await order.save();

  res.status(200).json({
    status: 'success',
    data: {
      order
    }
  });
});

/**
 * Get order statistics
 * @route GET /api/v1/admin/orders/stats
 * @access Private (Admin only)
 */
exports.getOrderStats = catchAsync(async (req, res, next) => {
  // Get time period from query params (default to 30 days)
  const period = req.query.period || '30';
  
  // Calculate date ranges
  const now = new Date();
  let startDate;
  
  switch (period) {
    case '7':
      startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
      break;
    case '90':
      startDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
      break;
    case 'all':
      startDate = new Date(0); // Beginning of time
      break;
    default: // 30 days
      startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
  }

  // Get order statistics
  const stats = await Order.aggregate([
    {
      $facet: {
        statusStats: [
          {
            $group: {
              _id: '$status',
              count: { $sum: 1 },
              total: { $sum: '$total' }
            }
          }
        ],
        timeStats: [
          {
            $match: {
              createdAt: { $gte: startDate }
            }
          },
          {
            $group: {
              _id: { $dateToString: { format: '%Y-%m-%d', date: '$createdAt' } },
              count: { $sum: 1 },
              total: { $sum: '$total' }
            }
          },
          {
            $sort: { _id: 1 }
          }
        ],
        paymentStats: [
          {
            $group: {
              _id: '$isPaid',
              count: { $sum: 1 },
              total: { $sum: '$total' }
            }
          }
        ],
        deliveryStats: [
          {
            $group: {
              _id: '$isDelivered',
              count: { $sum: 1 }
            }
          }
        ],
        totalStats: [
          {
            $group: {
              _id: null,
              count: { $sum: 1 },
              total: { $sum: '$total' },
              avgOrderValue: { $avg: '$total' }
            }
          }
        ]
      }
    }
  ]);

  // Get counts by status
  const pendingOrders = await Order.countDocuments({ status: 'pending' });
  const processingOrders = await Order.countDocuments({ status: 'processing' });
  const shippedOrders = await Order.countDocuments({ status: 'shipped' });
  const deliveredOrders = await Order.countDocuments({ status: 'delivered' });
  const cancelledOrders = await Order.countDocuments({ status: 'cancelled' });

  // Get payment and delivery counts
  const paidOrders = await Order.countDocuments({ isPaid: true });
  const unpaidOrders = await Order.countDocuments({ isPaid: false });
  const deliveredOrdersCount = await Order.countDocuments({ isDelivered: true });
  const undeliveredOrdersCount = await Order.countDocuments({ isDelivered: false });

  res.status(200).json({
    status: 'success',
    data: {
      stats: stats[0],
      summary: {
        byStatus: {
          pending: pendingOrders,
          processing: processingOrders,
          shipped: shippedOrders,
          delivered: deliveredOrders,
          cancelled: cancelledOrders
        },
        byPayment: {
          paid: paidOrders,
          unpaid: unpaidOrders
        },
        byDelivery: {
          delivered: deliveredOrdersCount,
          undelivered: undeliveredOrdersCount
        }
      },
      period: {
        days: period === 'all' ? 'all time' : period,
        startDate,
        endDate: now
      }
    }
  });
});
