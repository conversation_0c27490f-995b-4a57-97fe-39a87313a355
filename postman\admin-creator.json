{"info": {"_postman_id": "d8e7e5f2-6c9d-7e5f-9f8a-9b4c2d3e1a0e", "name": "Flashy - Admin Creator Management", "description": "A collection for testing the admin creator management endpoints", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Creator Management", "item": [{"name": "Get All Creators", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{BASE_URL}}/api/v1/admin/creators", "host": ["{{BASE_URL}}"], "path": ["api", "v1", "admin", "creators"]}, "description": "Get all creators with optional filtering and pagination"}, "response": []}, {"name": "Get Creator", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{BASE_URL}}/api/v1/admin/creators/{{creatorId}}", "host": ["{{BASE_URL}}"], "path": ["api", "v1", "admin", "creators", "{{creatorId}}"]}, "description": "Get a specific creator by ID"}, "response": []}, {"name": "Update Verification Status", "request": {"method": "PATCH", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"isVerified\": true,\n    \"verificationNotes\": \"Creator has been verified after reviewing all documents.\"\n}"}, "url": {"raw": "{{BASE_URL}}/api/v1/admin/creators/{{creatorId}}/verification", "host": ["{{BASE_URL}}"], "path": ["api", "v1", "admin", "creators", "{{creatorId}}", "verification"]}, "description": "Update the verification status of a creator"}, "response": []}, {"name": "Update Business Verification", "request": {"method": "PATCH", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"isVerified\": true,\n    \"verificationNotes\": \"Business documents have been verified and approved.\"\n}"}, "url": {"raw": "{{BASE_URL}}/api/v1/admin/creators/{{creatorId}}/business-verification", "host": ["{{BASE_URL}}"], "path": ["api", "v1", "admin", "creators", "{{creatorId}}", "business-verification"]}, "description": "Update the business verification status of a creator"}, "response": []}], "description": "Endpoints for managing creators"}, {"name": "Creator Statistics", "item": [{"name": "Get Verification Stats", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{BASE_URL}}/api/v1/admin/creators/verification-stats", "host": ["{{BASE_URL}}"], "path": ["api", "v1", "admin", "creators", "verification-stats"]}, "description": "Get statistics about creator verification"}, "response": []}, {"name": "Get Onboarding Stats", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{BASE_URL}}/api/v1/admin/creators/onboarding-stats", "host": ["{{BASE_URL}}"], "path": ["api", "v1", "admin", "creators", "onboarding-stats"]}, "description": "Get statistics about creator onboarding"}, "response": []}], "description": "Endpoints for creator statistics"}, {"name": "Creator Content", "item": [{"name": "Get Creator Products", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{BASE_URL}}/api/v1/admin/creators/{{creatorId}}/products", "host": ["{{BASE_URL}}"], "path": ["api", "v1", "admin", "creators", "{{creatorId}}", "products"]}, "description": "Get all products by a specific creator"}, "response": []}, {"name": "Get Creator Bales", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{BASE_URL}}/api/v1/admin/creators/{{creatorId}}/bales", "host": ["{{BASE_URL}}"], "path": ["api", "v1", "admin", "creators", "{{creatorId}}", "bales"]}, "description": "Get all bales by a specific creator"}, "response": []}, {"name": "Get Creator Orders", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{BASE_URL}}/api/v1/admin/creators/{{creatorId}}/orders", "host": ["{{BASE_URL}}"], "path": ["api", "v1", "admin", "creators", "{{creatorId}}", "orders"]}, "description": "Get all orders containing items from a specific creator"}, "response": [], "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["// Test basic response structure", "pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response has correct structure\", function () {", "    const responseData = pm.response.json();", "    pm.expect(responseData).to.be.an('object');", "    pm.expect(responseData.status).to.eql('success');", "    pm.expect(responseData.data).to.be.an('object');", "    pm.expect(responseData.data.orders).to.be.an('array');", "    pm.expect(responseData.total).to.be.a('number');", "    pm.expect(responseData.page).to.be.a('number');", "    pm.expect(responseData.limit).to.be.a('number');", "    pm.expect(responseData.totalPages).to.be.a('number');", "});", "", "// If orders exist, test their structure", "pm.test(\"Orders have correct structure\", function () {", "    const responseData = pm.response.json();", "    if (responseData.data.orders.length > 0) {", "        const order = responseData.data.orders[0];", "        pm.expect(order).to.have.property('_id');", "        pm.expect(order).to.have.property('user');", "        pm.expect(order).to.have.property('creatorItems');", "        pm.expect(order).to.have.property('status');", "        pm.expect(order).to.have.property('total');", "        pm.expect(order).to.have.property('creatorTotal');", "        pm.expect(order.creatorItems).to.be.an('array');", "    }", "});", "", "// Store the total count for use in other tests", "if (pm.response.json().total > 0) {", "    pm.environment.set(\"totalOrders\", pm.response.json().total);", "}"]}}]}, {"name": "Get Creator Orders - With Filters", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{BASE_URL}}/api/v1/admin/creators/{{creatorId}}/orders?status=delivered&isPaid=true&page=1&limit=10", "host": ["{{BASE_URL}}"], "path": ["api", "v1", "admin", "creators", "{{creatorId}}", "orders"], "query": [{"key": "status", "value": "delivered"}, {"key": "isPaid", "value": "true"}, {"key": "page", "value": "1"}, {"key": "limit", "value": "10"}]}, "description": "Get delivered and paid orders from a specific creator with pagination"}, "response": [], "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Filters are applied correctly\", function () {", "    const responseData = pm.response.json();", "    ", "    // Check if any orders were returned", "    if (responseData.data.orders.length > 0) {", "        // Verify all returned orders match the filter criteria", "        responseData.data.orders.forEach(order => {", "            pm.expect(order.status).to.eql('delivered');", "            pm.expect(order.isPaid).to.be.true;", "        });", "    }", "    ", "    // Verify pagination", "    pm.expect(responseData.page).to.eql(1);", "    pm.expect(responseData.limit).to.eql(10);", "});"]}}]}, {"name": "Get Creator Orders - Date Range", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{BASE_URL}}/api/v1/admin/creators/{{creatorId}}/orders?startDate=2023-01-01&endDate=2023-12-31&sort=-createdAt", "host": ["{{BASE_URL}}"], "path": ["api", "v1", "admin", "creators", "{{creatorId}}", "orders"], "query": [{"key": "startDate", "value": "2023-01-01"}, {"key": "endDate", "value": "2023-12-31"}, {"key": "sort", "value": "-createdAt"}]}, "description": "Get orders from a specific creator within a date range, sorted by creation date (newest first)"}, "response": [], "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Date range filter is applied correctly\", function () {", "    const responseData = pm.response.json();", "    const startDate = new Date('2023-01-01').getTime();", "    const endDate = new Date('2023-12-31').getTime();", "    ", "    // Check if any orders were returned", "    if (responseData.data.orders.length > 0) {", "        // Verify all returned orders are within the date range", "        responseData.data.orders.forEach(order => {", "            const orderDate = new Date(order.createdAt).getTime();", "            pm.expect(orderDate).to.be.at.least(startDate);", "            pm.expect(orderDate).to.be.at.most(endDate);", "        });", "        ", "        // Verify sorting (newest first)", "        for (let i = 0; i < responseData.data.orders.length - 1; i++) {", "            const currentDate = new Date(responseData.data.orders[i].createdAt).getTime();", "            const nextDate = new Date(responseData.data.orders[i+1].createdAt).getTime();", "            pm.expect(currentDate).to.be.at.least(nextDate);", "        }", "    }", "});"]}}]}, {"name": "Get Creator Orders - Search", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{BASE_URL}}/api/v1/admin/creators/{{creatorId}}/orders?search=Ghana", "host": ["{{BASE_URL}}"], "path": ["api", "v1", "admin", "creators", "{{creatorId}}", "orders"], "query": [{"key": "search", "value": "Ghana"}]}, "description": "Search for orders from a specific creator containing 'Ghana' in tracking number, notes, or shipping address"}, "response": [], "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Search functionality works correctly\", function () {", "    const responseData = pm.response.json();", "    const searchTerm = 'Ghana';", "    const searchRegex = new RegExp(searchTerm, 'i');", "    ", "    // Check if any orders were returned", "    if (responseData.data.orders.length > 0) {", "        // Verify at least one order matches the search criteria", "        const hasMatch = responseData.data.orders.some(order => {", "            return (", "                (order.trackingNumber && searchRegex.test(order.trackingNumber)) ||", "                (order.notes && searchRegex.test(order.notes)) ||", "                (order.shippingAddress && order.shippingAddress.country && searchRegex.test(order.shippingAddress.country)) ||", "                (order.shippingAddress && order.shippingAddress.city && searchRegex.test(order.shippingAddress.city)) ||", "                (order.shippingAddress && order.shippingAddress.state && searchRegex.test(order.shippingAddress.state)) ||", "                (order.shippingAddress && order.shippingAddress.name && searchRegex.test(order.shippingAddress.name))", "            );", "        });", "        ", "        pm.expect(hasMatch).to.be.true;", "    }", "});"]}}]}, {"name": "Get Creator Reviews", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{BASE_URL}}/api/v1/admin/creators/{{creatorId}}/reviews", "host": ["{{BASE_URL}}"], "path": ["api", "v1", "admin", "creators", "{{creatorId}}", "reviews"]}, "description": "Get all reviews for products by a specific creator"}, "response": [], "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["// Test basic response structure", "pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response has correct structure\", function () {", "    const responseData = pm.response.json();", "    pm.expect(responseData).to.be.an('object');", "    pm.expect(responseData.status).to.eql('success');", "    pm.expect(responseData.data).to.be.an('object');", "    pm.expect(responseData.data.reviews).to.be.an('array');", "    pm.expect(responseData.totalReviews).to.be.a('number');", "    pm.expect(responseData.currentPage).to.be.a('number');", "    pm.expect(responseData.totalPages).to.be.a('number');", "});", "", "// If reviews exist, test their structure", "pm.test(\"Reviews have correct structure\", function () {", "    const responseData = pm.response.json();", "    if (responseData.data.reviews.length > 0) {", "        const review = responseData.data.reviews[0];", "        pm.expect(review).to.have.property('_id');", "        pm.expect(review).to.have.property('title');", "        pm.expect(review).to.have.property('review');", "        pm.expect(review).to.have.property('rating');", "        pm.expect(review).to.have.property('user');", "        pm.expect(review.rating).to.be.within(1, 5);", "    }", "});", "", "// Store the total count for use in other tests", "if (pm.response.json().totalReviews > 0) {", "    pm.environment.set(\"totalReviews\", pm.response.json().totalReviews);", "}"]}}]}, {"name": "Get Creator Reviews - With Filters", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{BASE_URL}}/api/v1/admin/creators/{{creatorId}}/reviews?rating=5&verified=true&page=1&limit=10", "host": ["{{BASE_URL}}"], "path": ["api", "v1", "admin", "creators", "{{creatorId}}", "reviews"], "query": [{"key": "rating", "value": "5"}, {"key": "verified", "value": "true"}, {"key": "page", "value": "1"}, {"key": "limit", "value": "10"}]}, "description": "Get 5-star verified reviews for a specific creator with pagination"}, "response": [], "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Filters are applied correctly\", function () {", "    const responseData = pm.response.json();", "    ", "    // Check if any reviews were returned", "    if (responseData.data.reviews.length > 0) {", "        // Verify all returned reviews match the filter criteria", "        responseData.data.reviews.forEach(review => {", "            pm.expect(review.rating).to.eql(5);", "            pm.expect(review.verified).to.be.true;", "        });", "    }", "    ", "    // Verify pagination", "    pm.expect(responseData.currentPage).to.eql(1);", "});"]}}]}, {"name": "Get Creator Reviews - Date Range", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{BASE_URL}}/api/v1/admin/creators/{{creatorId}}/reviews?startDate=2023-01-01&endDate=2023-12-31&sort=-createdAt", "host": ["{{BASE_URL}}"], "path": ["api", "v1", "admin", "creators", "{{creatorId}}", "reviews"], "query": [{"key": "startDate", "value": "2023-01-01"}, {"key": "endDate", "value": "2023-12-31"}, {"key": "sort", "value": "-createdAt"}]}, "description": "Get reviews for a specific creator within a date range, sorted by creation date (newest first)"}, "response": [], "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Date range filter is applied correctly\", function () {", "    const responseData = pm.response.json();", "    const startDate = new Date('2023-01-01').getTime();", "    const endDate = new Date('2023-12-31').getTime();", "    ", "    // Check if any reviews were returned", "    if (responseData.data.reviews.length > 0) {", "        // Verify all returned reviews are within the date range", "        responseData.data.reviews.forEach(review => {", "            const reviewDate = new Date(review.createdAt).getTime();", "            pm.expect(reviewDate).to.be.at.least(startDate);", "            pm.expect(reviewDate).to.be.at.most(endDate);", "        });", "        ", "        // Verify sorting (newest first)", "        for (let i = 0; i < responseData.data.reviews.length - 1; i++) {", "            const currentDate = new Date(responseData.data.reviews[i].createdAt).getTime();", "            const nextDate = new Date(responseData.data.reviews[i+1].createdAt).getTime();", "            pm.expect(currentDate).to.be.at.least(nextDate);", "        }", "    }", "});"]}}]}, {"name": "Get Creator Reviews - Search", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{BASE_URL}}/api/v1/admin/creators/{{creatorId}}/reviews?search=excellent", "host": ["{{BASE_URL}}"], "path": ["api", "v1", "admin", "creators", "{{creatorId}}", "reviews"], "query": [{"key": "search", "value": "excellent"}]}, "description": "Search for reviews containing 'excellent' in title or review content"}, "response": [], "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Search functionality works correctly\", function () {", "    const responseData = pm.response.json();", "    const searchTerm = 'excellent';", "    const searchRegex = new RegExp(searchTerm, 'i');", "    ", "    // Check if any reviews were returned", "    if (responseData.data.reviews.length > 0) {", "        // Verify at least one review matches the search criteria", "        const hasMatch = responseData.data.reviews.some(review => {", "            return (", "                (review.title && searchRegex.test(review.title)) ||", "                (review.review && searchRegex.test(review.review))", "            );", "        });", "        ", "        pm.expect(hasMatch).to.be.true;", "    }", "});"]}}]}, {"name": "Get Creator Reviews - Field Selection", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{BASE_URL}}/api/v1/admin/creators/{{creatorId}}/reviews?fields=title,rating,createdAt", "host": ["{{BASE_URL}}"], "path": ["api", "v1", "admin", "creators", "{{creatorId}}", "reviews"], "query": [{"key": "fields", "value": "title,rating,createdAt"}]}, "description": "Get only specific fields (title, rating, createdAt) from reviews"}, "response": [], "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Field selection works correctly\", function () {", "    const responseData = pm.response.json();", "    ", "    // Check if any reviews were returned", "    if (responseData.data.reviews.length > 0) {", "        const review = responseData.data.reviews[0];", "        ", "        // These fields should be present", "        pm.expect(review).to.have.property('_id'); // _id is always returned", "        pm.expect(review).to.have.property('title');", "        pm.expect(review).to.have.property('rating');", "        pm.expect(review).to.have.property('createdAt');", "        ", "        // These fields should not be present", "        pm.expect(review).to.not.have.property('review');", "        pm.expect(review).to.not.have.property('images');", "        pm.expect(review).to.not.have.property('helpful');", "    }", "});"]}}]}, {"name": "Get Creator Earnings", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{BASE_URL}}/api/v1/admin/creators/{{creatorId}}/earnings?startDate=2023-01-01&endDate=2023-12-31", "host": ["{{BASE_URL}}"], "path": ["api", "v1", "admin", "creators", "{{creatorId}}", "earnings"], "query": [{"key": "startDate", "value": "2023-01-01"}, {"key": "endDate", "value": "2023-12-31"}]}, "description": "Get earnings statistics for a specific creator with optional date range"}, "response": []}], "description": "Endpoints for managing creator content"}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "variable": [{"key": "BASE_URL", "value": "http://localhost:5000", "type": "string"}, {"key": "token", "value": "your-admin-jwt-token-here", "type": "string"}, {"key": "creatorId", "value": "creator-id-here", "type": "string"}]}