# Flashy API Backend Plan

This folder contains detailed documentation for the Flashy API backend architecture, including endpoints, models, and functionality for each user type.

## Contents

- [Auth](./AUTH.md) - Authentication and authorization endpoints and flows
- [Buyers](./BUYERS.md) - Buyer-specific endpoints and functionality
- [Creators](./CREATORS.md) - Creator-specific endpoints and functionality
- [Admin](./ADMIN.md) - Admin-specific endpoints and functionality

## System Overview

Flashy is a multi-vendor fashion platform with the following key features:

- Multi-user roles (buyers, creators/sellers, admins)
- Product and bale management
- Flash sales functionality
- Order processing and management
- Payment processing via Paystack
- Wallet and earnings tracking
- Reviews and ratings
- Wishlist and cart functionality
- Notifications system

## API Structure

The API follows a RESTful architecture with the following base URL structure:

```
/api/v1/{user-type}/{resource}
```

For example:
- `/api/v1/auth/login` - Authentication endpoint
- `/api/v1/buyers/orders` - Buyer's orders
- `/api/v1/creators/products` - Creator's products
- `/api/v1/admin/dashboard` - Admin dashboard data

## Data Models

The system uses MongoDB with Mongoose for data modeling. Key models include:

- User models (<PERSON><PERSON><PERSON>, Buyer, Creator, Admin)
- Product and Bale models
- Order model
- Payment and Payout models
- Wallet model
- Review model
- Wishlist and Cart models
- Notification model

See individual documentation files for detailed model structures and relationships.
