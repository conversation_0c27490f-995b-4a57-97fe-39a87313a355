const Review = require('../../models/review.model');
const AppError = require('../../utils/appError');
const catchAsync = require('../../utils/catchAsync');

/**
 * Get all reviews
 * @route GET /api/v1/admin/reviews
 * @access Private (Admin only)
 */
exports.getAllReviews = catchAsync(async (req, res, next) => {
  // Build query
  const queryObj = { ...req.query };
  const excludedFields = ['page', 'sort', 'limit', 'fields', 'search'];
  excludedFields.forEach(el => delete queryObj[el]);

  // Advanced filtering
  let queryStr = JSON.stringify(queryObj);
  queryStr = queryStr.replace(/\b(gte|gt|lte|lt)\b/g, match => `$${match}`);
  
  let query = Review.find(JSON.parse(queryStr));

  // Search functionality
  if (req.query.search) {
    const searchRegex = new RegExp(req.query.search, 'i');
    query = query.find({
      $or: [
        { title: searchRegex },
        { review: searchRegex }
      ]
    });
  }

  // Filter by hidden status if specified
  if (req.query.hidden) {
    query = query.find({ hidden: req.query.hidden === 'true' });
  }

  // Filter by rating if specified
  if (req.query.rating) {
    query = query.find({ rating: req.query.rating });
  }

  // Count total before applying pagination
  const total = await Review.countDocuments(query);

  // Sorting
  if (req.query.sort) {
    const sortBy = req.query.sort.split(',').join(' ');
    query = query.sort(sortBy);
  } else {
    query = query.sort('-createdAt');
  }

  // Field limiting
  if (req.query.fields) {
    const fields = req.query.fields.split(',').join(' ');
    query = query.select(fields);
  } else {
    query = query.select('-__v');
  }

  // Pagination
  const page = req.query.page * 1 || 1;
  const limit = req.query.limit * 1 || 20;
  const skip = (page - 1) * limit;

  query = query.skip(skip).limit(limit);

  // Execute query
  const reviews = await query;

  res.status(200).json({
    status: 'success',
    results: reviews.length,
    total,
    page,
    limit,
    data: {
      reviews
    }
  });
});

/**
 * Toggle review hidden status
 * @route PATCH /api/v1/admin/reviews/:id/toggle-hidden
 * @access Private (Admin only)
 */
exports.toggleReviewHidden = catchAsync(async (req, res, next) => {
  // Find review
  const review = await Review.findById(req.params.id);

  if (!review) {
    return next(new AppError('No review found with that ID', 404));
  }

  // Toggle hidden status
  review.hidden = !review.hidden;

  // Add admin notes if provided
  if (req.body.adminNotes) {
    review.adminNotes = req.body.adminNotes;
  }

  // Save the updated review
  await review.save();

  res.status(200).json({
    status: 'success',
    data: {
      review
    }
  });
});

/**
 * Get review statistics
 * @route GET /api/v1/admin/reviews/stats
 * @access Private (Admin only)
 */
exports.getReviewStats = catchAsync(async (req, res, next) => {
  const stats = await Review.aggregate([
    {
      $group: {
        _id: '$rating',
        count: { $sum: 1 },
        avgHelpfulVotes: { $avg: '$helpfulVotes.count' }
      }
    },
    {
      $sort: { _id: -1 }
    }
  ]);

  const totalReviews = await Review.countDocuments();
  const hiddenReviews = await Review.countDocuments({ hidden: true });
  const visibleReviews = await Review.countDocuments({ hidden: false });
  const avgRating = await Review.aggregate([
    {
      $group: {
        _id: null,
        avgRating: { $avg: '$rating' }
      }
    }
  ]);

  res.status(200).json({
    status: 'success',
    data: {
      stats,
      summary: {
        total: totalReviews,
        hidden: hiddenReviews,
        visible: visibleReviews,
        avgRating: avgRating.length > 0 ? avgRating[0].avgRating : 0
      }
    }
  });
});
