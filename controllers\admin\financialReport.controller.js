const FinancialReportService = require('../../services/financialReport.service');
const catchAsync = require('../../utils/catchAsync');
const AppError = require('../../utils/appError');

/**
 * Get platform revenue summary
 * @route GET /api/v1/admin/reports/revenue
 * @access Private (Admin only)
 */
exports.getPlatformRevenue = catchAsync(async (req, res, next) => {
  const { startDate, endDate } = req.query;
  
  if (!startDate || !endDate) {
    return next(new AppError('Please provide startDate and endDate', 400));
  }
  
  const revenue = await FinancialReportService.getPlatformRevenue(startDate, endDate);
  
  res.status(200).json({
    status: 'success',
    data: {
      revenue
    }
  });
});

/**
 * Get revenue breakdown by day/week/month
 * @route GET /api/v1/admin/reports/revenue/breakdown
 * @access Private (Admin only)
 */
exports.getRevenueBreakdown = catchAsync(async (req, res, next) => {
  const { startDate, endDate, interval = 'day' } = req.query;
  
  if (!startDate || !endDate) {
    return next(new AppError('Please provide startDate and endDate', 400));
  }
  
  if (!['day', 'week', 'month'].includes(interval)) {
    return next(new AppError('Interval must be one of: day, week, month', 400));
  }
  
  const breakdown = await FinancialReportService.getRevenueBreakdown(startDate, endDate, interval);
  
  res.status(200).json({
    status: 'success',
    results: breakdown.length,
    data: {
      interval,
      breakdown
    }
  });
});

/**
 * Get creator earnings
 * @route GET /api/v1/admin/reports/creators/earnings
 * @access Private (Admin only)
 */
exports.getCreatorEarnings = catchAsync(async (req, res, next) => {
  const { startDate, endDate, creatorId } = req.query;
  
  if (!startDate || !endDate) {
    return next(new AppError('Please provide startDate and endDate', 400));
  }
  
  const earnings = await FinancialReportService.getCreatorEarnings(startDate, endDate, creatorId);
  
  res.status(200).json({
    status: 'success',
    results: earnings.length,
    data: {
      earnings
    }
  });
});

/**
 * Get shipping statistics
 * @route GET /api/v1/admin/reports/shipping
 * @access Private (Admin only)
 */
exports.getShippingStats = catchAsync(async (req, res, next) => {
  const { startDate, endDate } = req.query;
  
  if (!startDate || !endDate) {
    return next(new AppError('Please provide startDate and endDate', 400));
  }
  
  const shippingStats = await FinancialReportService.getShippingStats(startDate, endDate);
  
  res.status(200).json({
    status: 'success',
    results: shippingStats.length,
    data: {
      shippingStats
    }
  });
});

/**
 * Get payment method statistics
 * @route GET /api/v1/admin/reports/payments
 * @access Private (Admin only)
 */
exports.getPaymentMethodStats = catchAsync(async (req, res, next) => {
  const { startDate, endDate } = req.query;
  
  if (!startDate || !endDate) {
    return next(new AppError('Please provide startDate and endDate', 400));
  }
  
  const paymentStats = await FinancialReportService.getPaymentMethodStats(startDate, endDate);
  
  res.status(200).json({
    status: 'success',
    results: paymentStats.length,
    data: {
      paymentStats
    }
  });
});

/**
 * Get payout summary
 * @route GET /api/v1/admin/reports/payouts
 * @access Private (Admin only)
 */
exports.getPayoutSummary = catchAsync(async (req, res, next) => {
  const { startDate, endDate } = req.query;
  
  if (!startDate || !endDate) {
    return next(new AppError('Please provide startDate and endDate', 400));
  }
  
  const payoutSummary = await FinancialReportService.getPayoutSummary(startDate, endDate);
  
  res.status(200).json({
    status: 'success',
    results: payoutSummary.length,
    data: {
      payoutSummary
    }
  });
});

/**
 * Get overall financial summary
 * @route GET /api/v1/admin/reports/summary
 * @access Private (Admin only)
 */
exports.getFinancialSummary = catchAsync(async (req, res, next) => {
  const { startDate, endDate } = req.query;
  
  if (!startDate || !endDate) {
    return next(new AppError('Please provide startDate and endDate', 400));
  }
  
  const summary = await FinancialReportService.getFinancialSummary(startDate, endDate);
  
  res.status(200).json({
    status: 'success',
    data: {
      summary
    }
  });
});
