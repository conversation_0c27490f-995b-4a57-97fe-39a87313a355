const mongoose = require('mongoose');
const { Creator } = require('../../models/user.model');
const Product = require('../../models/product.model');
const Order = require('../../models/order.model');
const Payout = require('../../models/payout.model');
const AppError = require('../../utils/appError');
const catchAsync = require('../../utils/catchAsync');


/**
 * Get creator profile
 * @route GET /api/v1/creators/profile
 * @access Private (Creator only)
 */
exports.getProfile = catchAsync(async (req, res, next) => {
  const creator = await Creator.findById(req.user.id)
    .select('-__v -password -passwordChangedAt -passwordResetToken -passwordResetExpires');

  if (!creator) {
    return next(new AppError('Creator not found', 404));
  }

  res.status(200).json({
    status: 'success',
    data: {
      creator
    }
  });
});

/**
 * Update creator profile
 * @route PATCH /api/v1/creators/profile
 * @access Private (Creator only)
 */
exports.updateProfile = catchAsync(async (req, res, next) => {
  // Create a filtered body to prevent unwanted fields
  const filteredBody = {};
  const allowedFields = ['name', 'email', 'phone', 'gender', 'dateOfBirth'];

  Object.keys(req.body).forEach(key => {
    if (allowedFields.includes(key)) {
      filteredBody[key] = req.body[key];
    }
  });

  // Handle profile photo if uploaded
  if (req.file) {
    filteredBody.photo = req.file.path.replace(/\\/g, '/');
  }

  // Update creator profile
  const updatedCreator = await Creator.findByIdAndUpdate(
    req.user.id,
    filteredBody,
    {
      new: true,
      runValidators: true
    }
  ).select('-__v -password -passwordChangedAt -passwordResetToken -passwordResetExpires');

  res.status(200).json({
    status: 'success',
    data: {
      creator: updatedCreator
    }
  });
});



/**
 * Update profile photo
 * @route PATCH /api/v1/creators/profile/photo
 * @access Private (Creator only)
 */
exports.updateProfilePhoto = catchAsync(async (req, res, next) => {
  // Check if file was uploaded
  if (!req.file) {
    return next(new AppError('Please upload a profile photo', 400));
  }

  // Convert backslashes to forward slashes for consistency
  const photoPath = req.file.path.replace(/\\/g, '/');

  // Update creator profile with new photo
  const updatedCreator = await Creator.findByIdAndUpdate(
    req.user.id,
    { photo: photoPath },
    {
      new: true,
      runValidators: true
    }
  ).select('-__v -password -passwordChangedAt -passwordResetToken -passwordResetExpires');

  res.status(200).json({
    status: 'success',
    data: {
      creator: updatedCreator
    }
  });
});

/**
 * Get creator dashboard statistics
 * @route GET /api/v1/creators/profile/dashboard
 * @access Private (Creator only)
 */
exports.getDashboardStats = catchAsync(async (req, res, next) => {
  // Get creator metrics
  const creator = await Creator.findById(req.user.id).select('metrics');

  // Get recent products
  const recentProducts = await Product.find({ creator: req.user.id })
    .sort('-createdAt')
    .limit(5)
    .select('name basePrice images status createdAt');

  // Get recent bales (now stored as products with type: 'bale')
  const recentBales = await Product.find({ creator: req.user.id, type: 'bale' })
    .sort('-createdAt')
    .limit(5)
    .select('name basePrice images status createdAt');

  // Get recent orders
  const recentOrders = await Order.aggregate([
    {
      $match: {
        'items.creator': new mongoose.Types.ObjectId(req.user.id)
      }
    },
    {
      $addFields: {
        creatorItems: {
          $filter: {
            input: '$items',
            as: 'item',
            cond: { $eq: ['$$item.creator', new mongoose.Types.ObjectId(req.user.id)] }
          }
        }
      }
    },
    {
      $project: {
        _id: 1,
        user: 1,
        creatorItems: 1,
        status: 1,
        total: 1,
        isPaid: 1,
        createdAt: 1,
        creatorTotal: {
          $sum: {
            $map: {
              input: '$creatorItems',
              as: 'item',
              in: { $multiply: ['$$item.price', '$$item.quantity'] }
            }
          }
        }
      }
    },
    {
      $sort: { createdAt: -1 }
    },
    {
      $limit: 5
    }
  ]);

  // Get recent payouts
  const recentPayouts = await Payout.find({ creator: req.user.id })
    .sort('-createdAt')
    .limit(5)
    .select('amount status createdAt');

  res.status(200).json({
    status: 'success',
    data: {
      metrics: creator.metrics,
      recentProducts,
      recentBales,
      recentOrders,
      recentPayouts
    }
  });
});
