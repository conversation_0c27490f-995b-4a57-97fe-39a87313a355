const express = require('express');
const authMiddleware = require('../middleware/auth.middleware');

// Import buyer sub-routes
const profileRoutes = require('./buyers/profile.routes');
const orderRoutes = require('./buyers/order.routes');
const reviewRoutes = require('./buyers/review.routes');
const cartRoutes = require('./buyers/cart.routes');
const wishlistRoutes = require('./buyers/wishlist.routes');
const checkoutRoutes = require('./buyers/checkout.routes');
const searchRoutes = require('./buyers/search.routes');
const productRoutes = require('./buyers/product.routes');
const walletRoutes = require('./buyers/wallet.routes');
const followRoutes = require('./buyers/follow.routes');

const router = express.Router();

// Protect all routes
router.use(authMiddleware.protect);
router.use(authMiddleware.restrictTo('buyer'));

// Use buyer sub-routes
router.use('/products', productRoutes); //✅
router.use('/wishlist', wishlistRoutes);

router.use('/profile', profileRoutes);
router.use('/orders', orderRoutes);
router.use('/reviews', reviewRoutes);
router.use('/cart', cartRoutes);
router.use('/checkout', checkoutRoutes);
router.use('/wallet', walletRoutes);
router.use('/follow', followRoutes);



module.exports = router;

