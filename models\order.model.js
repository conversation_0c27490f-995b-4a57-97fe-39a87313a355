const mongoose = require('mongoose');
const FeeConfig = require('./feeConfig.model');
const ShippingFee = require('./shippingFee.model');

// Create a separate schema for order items to improve organization
const orderItemSchema = new mongoose.Schema({
  product: {
    type: mongoose.Schema.ObjectId,
    ref: 'Product'
  },
  bale: {
    type: mongoose.Schema.ObjectId,
    ref: 'Bale'
  },
  type: {
    type: String,
    enum: ['product', 'bale'],
    required: true
  },
  variationId: String,
  quantity: {
    type: Number,
    required: [true, 'Order item must have a quantity'],
    min: [1, 'Quantity must be at least 1']
  },
  price: {
    type: Number,
    required: [true, 'Order item must have a price']
  },
  creator: {
    type: mongoose.Schema.ObjectId,
    ref: 'User'
  },
  commission: {
    rate: Number,
    amount: Number
  },
  promotion: {
    id: {
      type: mongoose.Schema.ObjectId,
      ref: 'Promotion'
    },
    name: String,
    discountValue: Number,
    discountType: {
      type: String,
      enum: ['percentage', 'fixed']
    }
  },
  // 🆕 Tracking and fulfillment status
  status: {
    type: String,
    enum: ['pending', 'processing', 'shipped', 'delivered', 'cancelled', 'refunded'],
    default: 'pending'
  },
  trackingNumber: String,
  carrier: String,
  shippedAt: Date,
  deliveredAt: Date,
  estimatedDelivery: Date,
  statusHistory: [
    {
      status: {
        type: String,
        enum: ['pending', 'processing', 'shipped', 'delivered', 'cancelled', 'refunded']
      },
      timestamp: {
        type: Date,
        default: Date.now
      },
      note: String,
      updatedBy: {
        type: mongoose.Schema.ObjectId,
        ref: 'User'
      }
    }
  ]
});

const orderSchema = new mongoose.Schema(
  {
    orderNumber: {
      type: String,
    },
    user: {
      type: mongoose.Schema.ObjectId,
      ref: 'User',
      required: [true, 'Order must belong to a user']
    },
    items: [orderItemSchema],
    shippingAddress: {
      name: String,
      addressLine1: String,
      additionalInfo: String,
      city: String,
      region: String,
      country: String,
      phone: String,
      otherPhone: String,
    },
    paymentMethod: {
      type: String,
      enum: ['paystack'],
      required: [true, 'Order must have a payment method']
    },
    paymentResult: {
      reference: String,        // e.g., "psk_xyz123"
      status: String,           // e.g., "success"
      channel: String,          // e.g., "mobile_money"
      paidAt: Date,             // Date from Paystack
      gatewayResponse: String   // e.g., "Approved"
    },
    // Price breakdown
    subtotal: {
      type: Number,
      required: [true, 'Order must have a subtotal']
    },
    // Detailed fee breakdown
    fees: {
      // Platform fee (commission)
      platform: {
        percentage: {
          type: Number,
          default: 5 // Default platform fee percentage
        },
        amount: {
          type: Number,
          default: 0
        }
      },
      // Payment processing fee
      processing: {
        percentage: {
          type: Number,
          default: 1.5 // Default payment processing fee percentage
        },

        totalAmount: {
          type: Number,
          default: 0
        }
      },
      // Shipping fee
      shipping: {
        baseFee: {
          type: Number,
          default: 0
        },
        additionalFee: {
          type: Number,
          default: 0 // Additional fee based on distance, weight, etc.
        },
        totalAmount: {
          type: Number,
          default: 0
        }
      },
      // Total of all fees
      total: {
        type: Number,
        default: 0
      }
    },
    // Order status
    status: {
      type: String,
      enum: ['pending', 'processing', 'shipped', 'delivered', 'cancelled', 'refunded'],
      default: 'pending'
    },
    statusHistory: [
      {
        status: {
          type: String,
          enum: ['pending', 'processing', 'shipped', 'delivered', 'cancelled', 'refunded']
        },
        timestamp: {
          type: Date,
          default: Date.now
        },
        note: String,
        updatedBy: {
          type: mongoose.Schema.ObjectId,
          ref: 'User'
        }
      }
    ],
    isPaid: {
      type: Boolean,
      default: false
    },
    paidAt: Date,
    isDelivered: {
      type: Boolean,
      default: false
    },
    deliveredAt: Date,
    trackingNumber: String,
    carrier: String,
    estimatedDelivery: Date,
    notes: String,

    // Exchange history
    exchanges: [{
      oldItem: {
        id: mongoose.Schema.ObjectId,
        type: String,
        name: String,
        price: Number,
        quantity: Number
      },
      newItem: {
        type: String,
        name: String,
        price: Number,
        quantity: Number
      },
      priceDifference: Number,
      reason: String,
      exchangedAt: Date,
      exchangedBy: {
        type: mongoose.Schema.ObjectId,
        ref: 'User'
      }
    }],
    // For analytics and reporting
    createdAt: {
      type: Date,
      default: Date.now
    }
  },
  {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true }
  }
);

// Create indexes for common query patterns
orderSchema.index({ user: 1, createdAt: -1 });
orderSchema.index({ 'items.creator': 1, createdAt: -1 });
orderSchema.index({ orderNumber: 1 }, { unique: true });
orderSchema.index({ status: 1, createdAt: -1 });
orderSchema.index({ isPaid: 1, createdAt: -1 });

// Pre-save hook to generate order number if not provided
orderSchema.pre('save', async function(next) {
  if (!this.orderNumber) {
    // Generate order number with format: EF-YYYYMMDD-TIMESTAMP
    const date = new Date();
    const dateStr = date.getFullYear().toString() +
      (date.getMonth() + 1).toString().padStart(2, '0') +
      date.getDate().toString().padStart(2, '0');

    // Use current timestamp for guaranteed uniqueness
    const timestamp = Date.now().toString();

    // Create order number with prefix EF (EveryFash)
    this.orderNumber = `EF-${dateStr}-${timestamp}`;
  }

  // Update isPaid based on paymentResult
  if (this.paymentResult && this.paymentResult.status === 'success' && !this.isPaid) {
    this.isPaid = true;
    this.paidAt = this.paymentResult.paidAt || Date.now();
  }

  // Update isDelivered based on status
  if (this.status === 'delivered' && !this.isDelivered) {
    this.isDelivered = true;
    this.deliveredAt = Date.now();
  }

  // Add status change to history if status changed
  if (this.isModified('status')) {
    // Check if the last status in history is different from current status
    const lastStatus = this.statusHistory.length > 0
      ? this.statusHistory[this.statusHistory.length - 1].status
      : null;

    if (lastStatus !== this.status) {
      this.statusHistory.push({
        status: this.status,
        timestamp: Date.now(),
        note: `Order status changed to ${this.status}`
      });
    }
  }

  next();
});



// Virtual for total number of items
orderSchema.virtual('totalItems').get(function() {
  return this.items.reduce((sum, item) => sum + item.quantity, 0);
});

// Virtual for order age in days
orderSchema.virtual('ageInDays').get(function() {
  const now = new Date();
  const created = new Date(this.createdAt);
  const diffTime = Math.abs(now - created);
  return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
});

// Virtual for formatted order date
orderSchema.virtual('formattedOrderDate').get(function() {
  return new Date(this.createdAt).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });
});

// Virtual to group items by creator
orderSchema.virtual('itemsByCreator').get(function() {
  const groupedItems = {};

  this.items.forEach(item => {
    const creatorId = item.creator ? item.creator.toString() : 'unknown';

    if (!groupedItems[creatorId]) {
      groupedItems[creatorId] = {
        creator: item.creator,
        items: [],
        subtotal: 0
      };
    }

    groupedItems[creatorId].items.push(item);
    groupedItems[creatorId].subtotal += item.price * item.quantity;
  });

  return Object.values(groupedItems);
});

// Virtual to check if order is overdue (more than 7 days in processing)
orderSchema.virtual('isOverdue').get(function() {
  if (this.status !== 'processing') return false;

  const processingStart = this.statusHistory.find(h => h.status === 'processing');
  if (!processingStart) return false;

  const now = new Date();
  const startDate = new Date(processingStart.timestamp);
  const diffTime = Math.abs(now - startDate);
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

  return diffDays > 7;
});

// Method to update order status
orderSchema.methods.updateStatus = async function(status, note = '', updatedBy = null) {
  // Validate status transition
  const validTransitions = {
    'pending': ['processing', 'cancelled'],
    'processing': ['shipped', 'cancelled'],
    'shipped': ['delivered', 'cancelled', 'refunded'],
    'delivered': ['refunded']
  };

  if (validTransitions[this.status] && !validTransitions[this.status].includes(status)) {
    throw new Error(`Cannot transition from ${this.status} to ${status}`);
  }

  // Update the status
  this.status = status;

  // Add to status history
  this.statusHistory.push({
    status,
    timestamp: Date.now(),
    note,
    updatedBy
  });

  // Update related fields
  if (status === 'delivered') {
    this.isDelivered = true;
    this.deliveredAt = Date.now();
  }

  // Save the order first
  await this.save();

  // Send notifications about status change
  try {
    const NotificationService = require('../services/notification.service');

    // Determine notification type based on status
    let notificationType = 'order_status_update';
    if (status === 'shipped') notificationType = 'order_shipped';
    if (status === 'delivered') notificationType = 'order_delivered';
    if (status === 'cancelled') notificationType = 'order_cancelled';
    if (status === 'refunded') notificationType = 'order_refunded';

    // Send notification to buyer
    await NotificationService.createOrderNotification({
      order: this,
      type: notificationType,
      recipient: this.user
    });

    // Send notifications to creators of items in the order
    const creatorIds = [...new Set(this.items.map(item => item.creator?.toString()).filter(Boolean))];

    for (const creatorId of creatorIds) {
      await NotificationService.createOrderNotification({
        order: this,
        type: notificationType,
        recipient: creatorId
      });
    }
  } catch (error) {
    // Log error but don't stop the status update
    console.error('Error sending order status notification:', error);
  }

  return this;
};

// Method to update item status
orderSchema.methods.updateItemStatus = async function(itemId, status, note = '', updatedBy = null) {
  const item = this.items.id(itemId);
  if (!item) {
    throw new Error('Item not found');
  }

  item.status = status;

  // Add to item status history
  item.statusHistory.push({
    status,
    timestamp: Date.now(),
    note,
    updatedBy
  });

  // Update related fields
  if (status === 'shipped') {
    item.shippedAt = Date.now();
  } else if (status === 'delivered') {
    item.deliveredAt = Date.now();
  }

  // Update overall order status based on items
  this._updateOrderStatusFromItems();

  return this.save();
};

// Helper method to update order status based on item statuses
orderSchema.methods._updateOrderStatusFromItems = function() {
  // Get all items that are not exchanged (original items) or are replacements from exchanges
  // An exchanged item is one that was replaced, but the replacement still needs to be delivered
  const relevantItems = this.items.filter(item => {
    // Include items that are not marked as exchanged
    // OR items that were added as part of an exchange (these will have a status history entry with a note containing 'Exchanged from')
    return item.status !== 'exchanged' ||
           (item.statusHistory && item.statusHistory.some(h => h.note && h.note.includes('Exchanged from')));
  });

  const statuses = relevantItems.map(item => item.status);

  if (statuses.every(status => status === 'delivered')) {
    this.status = 'delivered';
    this.isDelivered = true;
    this.deliveredAt = Date.now();
  } else if (statuses.every(status => status === 'cancelled')) {
    this.status = 'cancelled';
  } else if (statuses.every(status => status === 'refunded')) {
    this.status = 'refunded';
  } else if (statuses.some(status => status === 'shipped')) {
    this.status = 'shipped';
  }
};

// Method to calculate all fees
orderSchema.methods.calculateFees = async function(options = {}) {


  // Get current fee configuration if not provided
  let feeConfig;
  if (options.feeConfig) {
    feeConfig = options.feeConfig;
  } else {
    feeConfig = await FeeConfig.getCurrentConfig();
  }

  // Get shipping fee configuration if not provided
  let shippingFeeConfig;
  if (options.shippingFeeConfig) {
    shippingFeeConfig = options.shippingFeeConfig;
  } else if (this.shippingAddress) {
    shippingFeeConfig = await ShippingFee.findFeeForLocation(
      this.shippingAddress.country,
      this.shippingAddress.region,
      this.shippingAddress.city
    );
  }

  // Extract fee percentages
  const platformFeePercentage = feeConfig.platformFee.percentage;
  const processingFeePercentage = feeConfig.processingFee.percentage;

  // Calculate shipping fee
  let shippingBaseRate = 0;
  let shippingAdditionalFee = 0;

  if (shippingFeeConfig) {
    // Determine if express delivery was selected
    const isExpress = this.shippingMethod === 'express';

    // Calculate shipping fee based on weight and delivery type
    const totalWeight = this.totalWeight || 0;
    const calculatedShippingFee = shippingFeeConfig.calculateFee(totalWeight, isExpress);

    // Split into base and additional for tracking
    shippingBaseRate = isExpress ? shippingFeeConfig.expressFee : shippingFeeConfig.standardFee;
    shippingAdditionalFee = calculatedShippingFee - shippingBaseRate;
  } else {
    // Use provided values or existing values as fallback
    shippingBaseRate = options.shippingBaseRate || this.fees?.shipping?.baseFee || 0;
    shippingAdditionalFee = options.shippingAdditionalFee || this.fees?.shipping?.additionalFee || 0;
  }

  // Initialize fees object if it doesn't exist
  if (!this.fees) {
    this.fees = {
      platform: { percentage: platformFeePercentage, amount: 0 },
      processing: { percentage: processingFeePercentage, totalAmount: 0 },
      shipping: { baseFee: shippingBaseRate, additionalFee: shippingAdditionalFee, totalAmount: 0 },
      total: 0
    };
  }

  // Calculate platform fee
  const platformFeeAmount = (this.subtotal * platformFeePercentage) / 100;
  this.fees.platform.percentage = platformFeePercentage;
  this.fees.platform.amount = parseFloat(platformFeeAmount.toFixed(2));

  // Calculate processing fee (percentage only, no fixed amount)
  const processingFeeAmount = (this.total * processingFeePercentage) / 100;
  this.fees.processing.percentage = processingFeePercentage;
  this.fees.processing.totalAmount = parseFloat(processingFeeAmount.toFixed(2));

  // Calculate shipping fee
  const shippingFeeAmount = shippingBaseRate + shippingAdditionalFee;
  this.fees.shipping.baseFee = shippingBaseRate;
  this.fees.shipping.additionalFee = shippingAdditionalFee;
  this.fees.shipping.totalAmount = parseFloat(shippingFeeAmount.toFixed(2));

  // Calculate total fees
  this.fees.total = parseFloat((platformFeeAmount + processingFeeAmount + shippingFeeAmount).toFixed(2));


  return this.fees;
};

// Method to mark order as paid
orderSchema.methods.markAsPaid = async function(paymentResult) {
  this.isPaid = true;
  this.paidAt = Date.now();
  this.paymentResult = paymentResult;

  // Calculate fees if they haven't been calculated yet
  if (!this.fees || !this.fees.total) {
    await this.calculateFees();
  }

  // Update status to processing if currently pending
  if (this.status === 'pending') {
    this.status = 'processing';
    this.statusHistory.push({
      status: 'processing',
      timestamp: Date.now(),
      note: 'Payment received, order processing'
    });
  }

  // Process financial transactions if Wallet model is available
  try {
    const Wallet = mongoose.model('Wallet');
    await Wallet.processOrderTransactions(this);
  } catch (error) {
    // If Wallet model is not available or there's an error, just log it
    // This ensures the order can still be marked as paid even if wallet processing fails
    console.log('Wallet processing skipped or failed:', error.message);
  }

  return this.save();
};

// Method to calculate subtotal
orderSchema.methods.calculateSubtotal = function() {
  this.subtotal = this.items.reduce((total, item) => {
    return total + (item.price * item.quantity);
  }, 0);

  // Format to 2 decimal places
  this.subtotal = parseFloat(this.subtotal.toFixed(2));

  return this.subtotal;
};

// Method to add tracking information
orderSchema.methods.addTracking = async function(trackingNumber, carrier, estimatedDelivery = null) {
  this.trackingNumber = trackingNumber;
  this.carrier = carrier;

  if (estimatedDelivery) {
    this.estimatedDelivery = new Date(estimatedDelivery);
  }

  // Update status to shipped if not already
  if (this.status !== 'shipped' && this.status !== 'delivered') {
    this.status = 'shipped';
    this.statusHistory.push({
      status: 'shipped',
      timestamp: Date.now(),
      note: `Shipped via ${carrier}, tracking: ${trackingNumber}`
    });
  }

  return this.save();
};

// Method to calculate subtotal
orderSchema.methods.calculateSubtotal = function() {
  this.subtotal = this.items.reduce((total, item) => {
    return total + (item.price * item.quantity);
  }, 0);

  return this.subtotal;
};

/**
 * Method to exchange items in an order
 * @param {Object} exchangeData - Exchange data
 * @param {String} exchangeData.itemId - ID of the item to exchange
 * @param {Object} exchangeData.newItem - New item data
 * @param {String} exchangeData.reason - Reason for the exchange
 * @param {String} exchangeData.initiatedBy - User ID of admin who initiated the exchange
 * @returns {Promise<Object>} - Updated order
 */
orderSchema.methods.exchangeItem = async function(exchangeData) {
  // Validate exchange data
  if (!exchangeData.itemId) {
    throw new Error('Item ID is required for exchange');
  }

  if (!exchangeData.newItem) {
    throw new Error('New item data is required for exchange');
  }

  // Find the item to exchange
  const itemToExchange = this.items.id(exchangeData.itemId);
  if (!itemToExchange) {
    throw new Error(`Item with ID ${exchangeData.itemId} not found in order`);
  }

  // Validate new item data
  const requiredFields = ['type', 'price', 'quantity'];
  for (const field of requiredFields) {
    if (!exchangeData.newItem[field]) {
      throw new Error(`${field} is required for new item`);
    }
  }

  // Check if this is an exchange between variations of the same product
  let isSameProductVariation = false;
  if (itemToExchange.type === 'product' && exchangeData.newItem.type === 'product') {
    if (exchangeData.newItem.product) {
      // If product ID is provided, check if it matches the original item
      isSameProductVariation = itemToExchange.product.toString() === exchangeData.newItem.product.toString();
    } else if (exchangeData.newItem.variationId) {
      // If only variationId is provided, we need to check if it belongs to the same product
      const Product = mongoose.model('Product');
      const product = await Product.findById(itemToExchange.product);
      if (product) {
        const variation = product.variations.id(exchangeData.newItem.variationId);
        if (variation) {
          isSameProductVariation = true;
          // Set the product ID to match the original item
          exchangeData.newItem.product = itemToExchange.product;
          // Set color and size from the variation
          exchangeData.newItem.color = variation.color;
          exchangeData.newItem.size = variation.size;
          // Set price from the variation if not provided
          if (!exchangeData.newItem.price) {
            exchangeData.newItem.price = variation.price || product.price;
          }
        }
      }
    }
  } else if (itemToExchange.type === 'bale' && exchangeData.newItem.type === 'bale') {
    if (exchangeData.newItem.bale) {
      // If bale ID is provided, check if it matches the original item
      isSameProductVariation = itemToExchange.bale.toString() === exchangeData.newItem.bale.toString();
    } else if (exchangeData.newItem.variationId) {
      // If only variationId is provided, we need to check if it belongs to the same bale
      const Bale = mongoose.model('Bale');
      const bale = await Bale.findById(itemToExchange.bale);
      if (bale) {
        const variation = bale.variations.id(exchangeData.newItem.variationId);
        if (variation) {
          isSameProductVariation = true;
          // Set the bale ID to match the original item
          exchangeData.newItem.bale = itemToExchange.bale;
          // Set size from the variation
          exchangeData.newItem.size = variation.size;
          // Set price from the variation if not provided
          if (!exchangeData.newItem.price) {
            exchangeData.newItem.price = variation.price || bale.price;
          }
        }
      }
    }
  }

  // Validate product/bale exists and has enough stock
  let newItemProduct;
  if (exchangeData.newItem.type === 'product') {
    if (!exchangeData.newItem.product) {
      throw new Error('Product ID is required for new item');
    }

    const Product = mongoose.model('Product');
    newItemProduct = await Product.findById(exchangeData.newItem.product);

    if (!newItemProduct) {
      throw new Error(`Product with ID ${exchangeData.newItem.product} not found`);
    }

    if (newItemProduct.status !== 'active') {
      throw new Error(`Product ${newItemProduct.name} is not available for exchange`);
    }

    // Check variation stock if applicable
    if (exchangeData.newItem.color && exchangeData.newItem.size) {
      const variation = newItemProduct.variations.find(
        v => v.color === exchangeData.newItem.color && v.size === exchangeData.newItem.size
      );

      if (!variation) {
        throw new Error(`Variation for product ${newItemProduct.name} not found`);
      }

      if (variation.quantity < exchangeData.newItem.quantity) {
        throw new Error(`Not enough stock for ${newItemProduct.name}. Only ${variation.quantity} available.`);
      }

      // Update product stock
      variation.quantity -= exchangeData.newItem.quantity;
      await newItemProduct.save();
    }
  } else if (exchangeData.newItem.type === 'bale') {
    if (!exchangeData.newItem.bale) {
      throw new Error('Bale ID is required for new item');
    }

    const Bale = mongoose.model('Bale');
    newItemProduct = await Bale.findById(exchangeData.newItem.bale);

    if (!newItemProduct) {
      throw new Error(`Bale with ID ${exchangeData.newItem.bale} not found`);
    }

    if (newItemProduct.status !== 'active') {
      throw new Error(`Bale ${newItemProduct.name} is not available for exchange`);
    }

    // Check variation stock if applicable
    if (exchangeData.newItem.size) {
      const variation = newItemProduct.variations.find(v => v.size === exchangeData.newItem.size);

      if (!variation) {
        throw new Error(`Variation for bale ${newItemProduct.name} not found`);
      }

      if (variation.quantity < exchangeData.newItem.quantity) {
        throw new Error(`Not enough stock for ${newItemProduct.name}. Only ${variation.quantity} available.`);
      }

      // Update bale stock
      variation.quantity -= exchangeData.newItem.quantity;
      await newItemProduct.save();
    }
  } else {
    throw new Error(`Invalid item type: ${exchangeData.newItem.type}`);
  }

  // Return the exchanged item to inventory
  try {
    if (itemToExchange.type === 'product' && itemToExchange.product) {
      const Product = mongoose.model('Product');
      const product = await Product.findById(itemToExchange.product);

      if (product) {
        const variation = product.variations.find(
          v => v.color === itemToExchange.color && v.size === itemToExchange.size
        );

        if (variation) {
          variation.quantity += itemToExchange.quantity;
          await product.save();
        }
      }
    } else if (itemToExchange.type === 'bale' && itemToExchange.bale) {
      const Bale = mongoose.model('Bale');
      const bale = await Bale.findById(itemToExchange.bale);

      if (bale) {
        const variation = bale.variations.find(v => v.size === itemToExchange.size);

        if (variation) {
          variation.quantity += itemToExchange.quantity;
          await bale.save();
        }
      }
    }
  } catch (error) {
    console.error('Error returning item to inventory:', error);
    // Continue with exchange even if inventory update fails
  }

  // Calculate price difference
  const oldItemTotal = itemToExchange.price * itemToExchange.quantity;
  const newItemTotal = exchangeData.newItem.price * exchangeData.newItem.quantity;
  const priceDifference = newItemTotal - oldItemTotal;

  // Create new item object with all necessary fields
  const newItem = {
    type: exchangeData.newItem.type,
    price: exchangeData.newItem.price,
    quantity: exchangeData.newItem.quantity,
    status: 'processing',
    statusHistory: [{
      status: 'processing',
      timestamp: Date.now(),
      note: `Exchanged from item ${itemToExchange._id}`,
      updatedBy: exchangeData.initiatedBy
    }]
  };

  // Add type-specific fields
  if (exchangeData.newItem.type === 'product') {
    newItem.product = exchangeData.newItem.product;
    newItem.color = exchangeData.newItem.color;
    newItem.size = exchangeData.newItem.size;
    newItem.name = newItemProduct.name;
    newItem.image = newItemProduct.images[0];
    newItem.creator = newItemProduct.creator;
  } else if (exchangeData.newItem.type === 'bale') {
    newItem.bale = exchangeData.newItem.bale;
    newItem.size = exchangeData.newItem.size;
    newItem.name = newItemProduct.name;
    newItem.image = newItemProduct.images[0];
    newItem.creator = newItemProduct.creator;
  }

  // Update the item to be exchanged
  itemToExchange.status = 'exchanged';
  itemToExchange.statusHistory.push({
    status: 'exchanged',
    timestamp: Date.now(),
    note: exchangeData.reason || 'Item exchanged',
    updatedBy: exchangeData.initiatedBy
  });

  // Add the new item to the order
  this.items.push(newItem);

  // Update order totals
  this.calculateSubtotal();
  this.total = this.subtotal + this.shippingCost;

  // Add exchange record to order history
  if (!this.exchanges) {
    this.exchanges = [];
  }

  this.exchanges.push({
    oldItem: {
      id: itemToExchange._id,
      type: itemToExchange.type,
      name: itemToExchange.name,
      price: itemToExchange.price,
      quantity: itemToExchange.quantity
    },
    newItem: {
      type: newItem.type,
      name: newItem.name,
      price: newItem.price,
      quantity: newItem.quantity
    },
    priceDifference,
    reason: exchangeData.reason || 'No reason provided',
    exchangedAt: Date.now(),
    exchangedBy: exchangeData.initiatedBy
  });

  // Process financial transactions if needed
  // No financial transactions needed if it's the same product variation with the same price
  if (priceDifference !== 0 || !isSameProductVariation) {
    try {
      const Wallet = mongoose.model('Wallet');
      await Wallet.processExchange(this, {
        oldItem: itemToExchange,
        newItem,
        priceDifference,
        sameCreator: itemToExchange.creator && newItem.creator &&
                    itemToExchange.creator.toString() === newItem.creator.toString(),
        sameProductVariation: isSameProductVariation
      });
    } catch (error) {
      console.error('Error processing exchange transactions:', error);
      // Continue with exchange even if wallet processing fails
    }
  }

  return this.save();
};

const Order = mongoose.model('Order', orderSchema);

module.exports = Order;








