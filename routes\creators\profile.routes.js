const express = require('express');
const profileController = require('../../controllers/creators/profile.controller');
const uploadMiddleware = require('../../middleware/cloudinaryUpload.middleware');

const router = express.Router();

// Profile routes
router.get('/', profileController.getProfile);
router.patch('/',
  uploadMiddleware.uploadProfilePhoto,
  uploadMiddleware.processCloudinaryFiles,
  uploadMiddleware.handleUploadError,
  profileController.updateProfile
);

// Profile photo route
router.patch('/photo',
  uploadMiddleware.uploadProfilePhoto,
  uploadMiddleware.processCloudinaryFiles,
  uploadMiddleware.handleUploadError,
  profileController.updateProfilePhoto
);
router.get('/dashboard', profileController.getDashboardStats);

module.exports = router;
