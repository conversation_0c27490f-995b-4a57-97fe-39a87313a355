# Authentication Error Messages Documentation

## Overview
This document outlines the improved, user-friendly error messages for all authentication-related operations. The messages are designed to be clear, specific, and actionable.

## Login Errors

### User Not Found
**Before:** `"Incorrect email or password"`
**After:** `"No account found with this email address. Please check your email or sign up for a new account."`

**When it occurs:** User enters an email that doesn't exist in the system.

### Incorrect Password
**Before:** `"Incorrect email or password"`
**After:** `"Incorrect password. Please check your password and try again."`

**When it occurs:** User enters correct email but wrong password.

### Account Deactivated
**Message:** `"Your account has been deactivated. Please contact support for assistance."`

**When it occurs:** User account exists but has been deactivated by admin.

### Email Not Verified
**Message:** `"Your email address is not verified. We've sent a new verification link to your email address. Please check your inbox and click the verification link to complete your account setup."`

**Response includes:**
```json
{
  "status": "fail",
  "message": "Your email address is not verified...",
  "emailSent": true,
  "userEmail": "<EMAIL>",
  "errorType": "email_not_verified"
}
```

**When it occurs:** User tries to login with unverified email address.

## Registration Errors

### Email Already Exists
**Before:** `"Email is already registered"`
**After:** `"An account with this email address already exists. Please use a different email or try logging in instead."`

**When it occurs:** User tries to register with an email that's already in use.

### Weak Password
**Before:** `"Password must be at least 8 characters, include at least one letter and one number."`
**After:** `"Password is too weak. Please create a password that is at least 8 characters long and includes at least one letter and one number."`

**When it occurs:** User enters a password that doesn't meet strength requirements.

### Password Mismatch
**Before:** `"Passwords do not match"`
**After:** `"Passwords do not match. Please make sure both password fields are identical."`

**When it occurs:** Password and password confirmation fields don't match.

### Invalid Email Format
**Message:** `"Invalid email format"`

**When it occurs:** User enters an email that doesn't follow valid email format.

## Password Reset Errors

### User Not Found (Forgot Password)
**Before:** `"There is no user with that email address."`
**After:** `"No account found with this email address. Please check your email or create a new account."`

**When it occurs:** User requests password reset for non-existent email.

### Email Sending Failed
**Before:** `"There was an error sending the email. Try again later!"`
**After:** `"We encountered an issue sending the password reset email. Please try again in a few minutes or contact support if the problem persists."`

**When it occurs:** Email service fails to send password reset email.

### Invalid/Expired Reset Token
**Before:** `"Token is invalid or has expired"`
**After:** `"Password reset link is invalid or has expired. Please request a new password reset link."`

**When it occurs:** User clicks on expired or invalid password reset link.

### Missing Password Fields
**Before:** `"Please provide password and password confirmation"`
**After:** `"Please provide both a new password and password confirmation."`

**When it occurs:** User submits reset form without required fields.

### Password Mismatch (Reset)
**Before:** `"Passwords do not match"`
**After:** `"New passwords do not match. Please make sure both password fields are identical."`

**When it occurs:** New password and confirmation don't match during reset.

## Email Verification Errors

### Invalid/Expired Verification Token
**Before:** `"Token is invalid or has expired"`
**After:** `"Email verification link is invalid or has expired. Please request a new verification email."`

**When it occurs:** User clicks on expired or invalid email verification link.

### Already Verified
**Before:** `"Email is already verified"`
**After:** `"Your email address is already verified. You can proceed to log in."`

**When it occurs:** User tries to verify an already verified email.

### User Not Found (Resend Verification)
**Before:** `"There is no user with that email address."`
**After:** `"No account found with this email address. Please check your email or create a new account."`

**When it occurs:** User requests verification email for non-existent account.

### Email Sending Failed (Verification)
**Before:** `"There was an error sending the verification email. Please try again later!"`
**After:** `"We encountered an issue sending the verification email. Please try again in a few minutes or contact support if the problem persists."`

**When it occurs:** Email service fails to send verification email.

## Password Update Errors

### Missing Fields
**Before:** `"Please provide your current password and the new password + confirmation."`
**After:** `"Please provide your current password, new password, and password confirmation."`

**When it occurs:** User doesn't provide all required fields for password update.

### Incorrect Current Password
**Before:** `"Your current password is wrong."`
**After:** `"Your current password is incorrect. Please enter your current password correctly."`

**When it occurs:** User enters wrong current password when updating.

### New Password Mismatch
**Before:** `"Your new passwords do not match."`
**After:** `"Your new passwords do not match. Please make sure both new password fields are identical."`

**When it occurs:** New password and confirmation don't match.

## Frontend Integration

### Error Handling Example

```javascript
const handleAuthError = (error) => {
  const { message, errorType, emailSent, userEmail } = error;
  
  switch (errorType) {
    case 'email_not_verified':
      showEmailVerificationMessage(message, userEmail);
      if (emailSent) {
        showSuccessMessage('Verification email sent!');
      }
      break;
    
    default:
      showErrorMessage(message);
  }
};

// Usage in login
try {
  const response = await login(email, password);
  // Handle success
} catch (error) {
  handleAuthError(error.response.data);
}
```

### Error Message Display

```jsx
const AuthErrorMessage = ({ error }) => {
  const getErrorIcon = (message) => {
    if (message.includes('not found') || message.includes('No account')) {
      return '👤'; // User not found
    }
    if (message.includes('password')) {
      return '🔒'; // Password related
    }
    if (message.includes('email') && message.includes('verify')) {
      return '📧'; // Email verification
    }
    return '⚠️'; // General error
  };

  return (
    <div className="error-message">
      <span className="error-icon">{getErrorIcon(error.message)}</span>
      <span className="error-text">{error.message}</span>
    </div>
  );
};
```

## Benefits of Improved Messages

1. **Clarity**: Users know exactly what went wrong
2. **Actionability**: Messages suggest what users should do next
3. **User-Friendly**: No technical jargon or generic messages
4. **Security**: Still secure - doesn't reveal sensitive system information
5. **Consistency**: All messages follow the same helpful tone and format

## Security Considerations

- Messages are specific enough to be helpful but don't reveal sensitive system details
- User enumeration is minimized while still being user-friendly
- Error types help frontend provide appropriate UI responses
- All messages maintain professional, helpful tone

## Testing Error Messages

Use these test cases to verify error messages:

1. **Login with non-existent email** → Should show user not found message
2. **Login with wrong password** → Should show incorrect password message
3. **Register with existing email** → Should show email already exists message
4. **Use expired reset token** → Should show expired token message
5. **Try to verify already verified email** → Should show already verified message
