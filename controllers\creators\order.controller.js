const Order = require('../../models/order.model');
const AppError = require('../../utils/appError');
const catchAsync = require('../../utils/catchAsync');
const mongoose = require('mongoose');

/**
 * Get all orders for the logged-in creator
 * @route GET /api/v1/creators/orders
 * @access Private (Creator only)
 */
exports.getMyOrders = catchAsync(async (req, res, next) => {
  // Find orders where the creator has items
  const orders = await Order.aggregate([
    {
      $match: {
        'items.creator': new mongoose.Types.ObjectId(req.user.id)
      }
    },
    {
      $addFields: {
        creatorItems: {
          $filter: {
            input: '$items',
            as: 'item',
            cond: { $eq: ['$$item.creator', new mongoose.Types.ObjectId(req.user.id)] }
          }
        }
      }
    },
    {
      $project: {
        _id: 1,
        user: 1,
        creatorItems: 1,
        status: 1,
        total: 1,
        isPaid: 1,
        paidAt: 1,
        isDelivered: 1,
        deliveredAt: 1,
        createdAt: 1,
        updatedAt: 1,
        creatorTotal: {
          $sum: {
            $map: {
              input: '$creatorItems',
              as: 'item',
              in: { $multiply: ['$$item.price', '$$item.quantity'] }
            }
          }
        }
      }
    },
    {
      $sort: { createdAt: -1 }
    }
  ]);

  // Apply pagination
  const page = req.query.page * 1 || 1;
  const limit = req.query.limit * 1 || 20;
  const skip = (page - 1) * limit;

  const paginatedOrders = orders.slice(skip, skip + limit);

  res.status(200).json({
    status: 'success',
    results: paginatedOrders.length,
    total: orders.length,
    page,
    limit,
    data: {
      orders: paginatedOrders
    }
  });
});

/**
 * Get order by ID for the logged-in creator
 * @route GET /api/v1/creators/orders/:id
 * @access Private (Creator only)
 */
exports.getMyOrder = catchAsync(async (req, res, next) => {
  // Find order
  const order = await Order.findById(req.params.id);

  if (!order) {
    return next(new AppError('No order found with that ID', 404));
  }

  // Check if creator has items in the order
  const creatorItems = order.items.filter(
    item => item.creator && item.creator.toString() === req.user.id
  );

  if (creatorItems.length === 0) {
    return next(new AppError('You do not have any items in this order', 403));
  }

  // Calculate creator's total for this order
  const creatorTotal = creatorItems.reduce(
    (total, item) => total + (item.price * item.quantity),
    0
  );

  res.status(200).json({
    status: 'success',
    data: {
      order,
      creatorItems,
      creatorTotal
    }
  });
});

/**
 * Update order item status for the logged-in creator
 * @route PATCH /api/v1/creators/orders/:id/items/:itemId
 * @access Private (Creator only)
 */
exports.updateOrderItemStatus = catchAsync(async (req, res, next) => {
  // Validate request body
  if (!req.body.status || !['processing', 'shipped', 'delivered', 'cancelled'].includes(req.body.status)) {
    return next(new AppError('Please provide a valid status (processing, shipped, delivered, cancelled)', 400));
  }

  // Find order
  const order = await Order.findById(req.params.id);

  if (!order) {
    return next(new AppError('No order found with that ID', 404));
  }

  // Find item in order
  const item = order.items.id(req.params.itemId);

  if (!item) {
    return next(new AppError('No item found with that ID', 404));
  }

  // Check if item belongs to creator
  if (!item.creator || item.creator.toString() !== req.user.id) {
    return next(new AppError('You do not own this item', 403));
  }

  // Check if order is in a state that allows item status updates
  if (!['processing', 'shipped'].includes(order.status)) {
    return next(new AppError(`Cannot update item status when order is in ${order.status} status`, 400));
  }

  // Update item status
  item.status = req.body.status;

  // Add notes if provided
  if (req.body.notes) {
    item.notes = req.body.notes;
  }

  // Save the updated order
  await order.save();

  res.status(200).json({
    status: 'success',
    data: {
      item
    }
  });
});

/**
 * Mark order item as shipped for the logged-in creator
 * @route PATCH /api/v1/creators/orders/:id/items/:itemId/ship
 * @access Private (Creator only)
 */
exports.shipOrderItem = catchAsync(async (req, res, next) => {
  // Validate request body
  if (!req.body.trackingNumber) {
    return next(new AppError('Please provide a tracking number', 400));
  }

  // Find order
  const order = await Order.findById(req.params.id);

  if (!order) {
    return next(new AppError('No order found with that ID', 404));
  }

  // Find item in order
  const item = order.items.id(req.params.itemId);

  if (!item) {
    return next(new AppError('No item found with that ID', 404));
  }

  // Check if item belongs to creator
  if (!item.creator || item.creator.toString() !== req.user.id) {
    return next(new AppError('You do not own this item', 403));
  }

  // Check if order is in a state that allows shipping
  if (!['processing', 'shipped'].includes(order.status)) {
    return next(new AppError(`Cannot ship item when order is in ${order.status} status`, 400));
  }

  // Update item status and tracking information
  item.status = 'shipped';
  item.tracking = {
    trackingNumber: req.body.trackingNumber,
    carrier: req.body.carrier || 'Default Carrier',
    shippedAt: Date.now()
  };

  // Add notes if provided
  if (req.body.notes) {
    item.notes = req.body.notes;
  }

  // Save the updated order
  await order.save();

  res.status(200).json({
    status: 'success',
    data: {
      item
    }
  });
});

/**
 * Get order statistics for the logged-in creator
 * @route GET /api/v1/creators/orders/stats
 * @access Private (Creator only)
 */
exports.getOrderStats = catchAsync(async (req, res, next) => {
  // Get time period from query params (default to 30 days)
  const period = req.query.period || '30';

  // Calculate date ranges
  const now = new Date();
  let startDate;

  switch (period) {
    case '7':
      startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
      break;
    case '90':
      startDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
      break;
    case 'all':
      startDate = new Date(0); // Beginning of time
      break;
    default: // 30 days
      startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
  }

  // Get order statistics
  const stats = await Order.aggregate([
    {
      $match: {
        'items.creator': new mongoose.Types.ObjectId(req.user.id),
        createdAt: { $gte: startDate }
      }
    },
    {
      $unwind: '$items'
    },
    {
      $match: {
        'items.creator': new mongoose.Types.ObjectId(req.user.id)
      }
    },
    {
      $group: {
        _id: '$items.status',
        count: { $sum: 1 },
        total: { $sum: { $multiply: ['$items.price', '$items.quantity'] } }
      }
    },
    {
      $sort: { _id: 1 }
    }
  ]);

  // Get time-based statistics
  const timeStats = await Order.aggregate([
    {
      $match: {
        'items.creator': new mongoose.Types.ObjectId(req.user.id),
        createdAt: { $gte: startDate }
      }
    },
    {
      $unwind: '$items'
    },
    {
      $match: {
        'items.creator': new mongoose.Types.ObjectId(req.user.id)
      }
    },
    {
      $group: {
        _id: { $dateToString: { format: '%Y-%m-%d', date: '$createdAt' } },
        count: { $sum: 1 },
        total: { $sum: { $multiply: ['$items.price', '$items.quantity'] } }
      }
    },
    {
      $sort: { _id: 1 }
    }
  ]);

  // Get product statistics
  const productStats = await Order.aggregate([
    {
      $match: {
        'items.creator': new mongoose.Types.ObjectId(req.user.id),
        createdAt: { $gte: startDate }
      }
    },
    {
      $unwind: '$items'
    },
    {
      $match: {
        'items.creator': new mongoose.Types.ObjectId(req.user.id)
      }
    },
    {
      $group: {
        _id: {
          type: '$items.type',
          id: { $cond: [{ $eq: ['$items.type', 'product'] }, '$items.product', '$items.bale'] }
        },
        name: { $first: '$items.name' },
        count: { $sum: 1 },
        quantity: { $sum: '$items.quantity' },
        total: { $sum: { $multiply: ['$items.price', '$items.quantity'] } }
      }
    },
    {
      $sort: { total: -1 }
    },
    {
      $limit: 10
    }
  ]);

  res.status(200).json({
    status: 'success',
    data: {
      stats,
      timeStats,
      productStats,
      period: {
        days: period === 'all' ? 'all time' : period,
        startDate,
        endDate: now
      }
    }
  });
});
