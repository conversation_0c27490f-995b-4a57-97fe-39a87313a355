const mongoose = require('mongoose');

const payoutSchema = new mongoose.Schema({
  reference: {
    type: String,
    required: true,
    unique: true
  },
  recipient: {
    type: mongoose.Schema.ObjectId,
    ref: 'User',
    required: true
  },
  amount: {
    type: Number,
    required: true
  },
  status: {
    type: String,
    enum: ['pending', 'processing', 'completed', 'failed'],
    default: 'pending'
  },
  method: {
    type: String,
    default: 'bank_transfer'
  },
  processedAt: Date,
  gatewayResponse: String,
  meta: mongoose.Schema.Types.Mixed
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes for common query patterns
payoutSchema.index({ recipient: 1, status: 1 });
// Note: reference field already has unique: true in schema definition, so no need for explicit index
payoutSchema.index({ createdAt: -1 });
payoutSchema.index({ status: 1 });

// Virtual for formatted amount (default to GHS)
payoutSchema.virtual('formattedAmount').get(function() {
  return `₵${this.amount.toFixed(2)}`;
});

// Virtual for status color (for UI)
payoutSchema.virtual('statusColor').get(function() {
  const statusColors = {
    'pending': 'yellow',
    'processing': 'blue',
    'completed': 'green',
    'failed': 'red'
  };

  return statusColors[this.status] || 'gray';
});

// Virtual for time ago
payoutSchema.virtual('timeAgo').get(function() {
  const now = new Date();
  const created = new Date(this.createdAt);
  const diffTime = Math.abs(now - created);
  const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));

  if (diffDays === 0) {
    const diffHours = Math.floor(diffTime / (1000 * 60 * 60));
    if (diffHours === 0) {
      const diffMinutes = Math.floor(diffTime / (1000 * 60));
      return `${diffMinutes} minute${diffMinutes !== 1 ? 's' : ''} ago`;
    }
    return `${diffHours} hour${diffHours !== 1 ? 's' : ''} ago`;
  } else if (diffDays < 7) {
    return `${diffDays} day${diffDays !== 1 ? 's' : ''} ago`;
  } else {
    const diffWeeks = Math.floor(diffDays / 7);
    return `${diffWeeks} week${diffWeeks !== 1 ? 's' : ''} ago`;
  }
});

// Method to mark payout as completed
payoutSchema.methods.markAsCompleted = async function(processDetails = {}) {
  this.status = 'completed';
  this.processedAt = processDetails.processedAt || Date.now();
  this.gatewayResponse = processDetails.gatewayResponse || 'Payout completed';

  if (processDetails.meta) {
    this.meta = processDetails.meta;
  }

  return this.save();
};

// Method to mark payout as failed
payoutSchema.methods.markAsFailed = async function(failureDetails = {}) {
  this.status = 'failed';
  this.gatewayResponse = failureDetails.gatewayResponse || 'Payout failed';

  if (failureDetails.meta) {
    this.meta = failureDetails.meta;
  }

  return this.save();
};

// Static method to create a new payout
payoutSchema.statics.createPayout = async function(payoutData) {
  const reference = payoutData.reference || `PO-${Date.now()}-${Math.floor(Math.random() * 1000000)}`;

  // Validate required fields
  if (!payoutData.recipient) {
    throw new Error('Recipient is required');
  }

  if (!payoutData.amount || payoutData.amount <= 0) {
    throw new Error('Amount must be greater than 0');
  }

  // Create payout object
  const payout = await this.create({
    reference,
    recipient: payoutData.recipient,
    amount: parseFloat(payoutData.amount.toFixed(2)),
    method: payoutData.method || 'bank_transfer',
    meta: payoutData.meta || {}
  });

  return payout;
};

// Static method to get payout statistics
payoutSchema.statics.getStatistics = async function(startDate = null, endDate = null) {
  const matchStage = {};

  if (startDate || endDate) {
    matchStage.createdAt = {};
    if (startDate) matchStage.createdAt.$gte = new Date(startDate);
    if (endDate) matchStage.createdAt.$lte = new Date(endDate);
  }

  const stats = await this.aggregate([
    { $match: matchStage },
    {
      $group: {
        _id: null,
        totalPayouts: { $sum: 1 },
        totalAmount: { $sum: '$amount' },
        averageAmount: { $avg: '$amount' },
        completedPayouts: {
          $sum: { $cond: [{ $eq: ['$status', 'completed'] }, 1, 0] }
        },
        failedPayouts: {
          $sum: { $cond: [{ $eq: ['$status', 'failed'] }, 1, 0] }
        },
        pendingPayouts: {
          $sum: { $cond: [{ $eq: ['$status', 'pending'] }, 1, 0] }
        }
      }
    }
  ]);

  return stats.length > 0 ? stats[0] : {
    totalPayouts: 0,
    totalAmount: 0,
    averageAmount: 0,
    completedPayouts: 0,
    failedPayouts: 0,
    pendingPayouts: 0
  };
};

const Payout = mongoose.model('Payout', payoutSchema);

module.exports = Payout;
