const express = require('express');
const orderController = require('../../controllers/buyers/order.controller');
const authMiddleware = require('../../middleware/auth.middleware');

const router = express.Router();

// Protect all routes
router.use(authMiddleware.protect);
router.use(authMiddleware.restrictTo('buyer'));

// Order routes
router.get('/', orderController.getMyOrders);
router.get('/:id', orderController.getMyOrder);

module.exports = router;
