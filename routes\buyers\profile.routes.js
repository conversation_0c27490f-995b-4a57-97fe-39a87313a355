const express = require('express');
const profileController = require('../../controllers/buyers/profile.controller');
const authMiddleware = require('../../middleware/auth.middleware');
const uploadMiddleware = require('../../middleware/cloudinaryUpload.middleware');

const router = express.Router();

// Protect all routes
router.use(authMiddleware.protect);
router.use(authMiddleware.restrictTo('buyer'));

// Profile routes
router.get('/', profileController.getProfile);
router.patch('/',
  uploadMiddleware.uploadProfilePhoto,
  uploadMiddleware.processCloudinaryFiles,
  uploadMiddleware.handleUploadError,
  profileController.updateProfile
);
router.patch('/shipping-address', profileController.updateShippingAddress);
router.patch('/password', profileController.updatePassword);
router.patch('/preferences', profileController.updatePreferences);
router.get('/dashboard', profileController.getDashboardStats);

module.exports = router;
