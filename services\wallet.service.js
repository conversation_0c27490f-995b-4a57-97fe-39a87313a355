const Wallet = require('../models/wallet.model');
const Order = require('../models/order.model');
const Payment = require('../models/payment.model');
const Payout = require('../models/payout.model');
const mongoose = require('mongoose');

/**
 * Wallet Service
 * Handles all wallet-related operations
 */
class WalletService {
  /**
   * Process an order's financial transactions
   * @param {String} orderId - Order ID
   * @returns {Promise<Object>} - Result of processing
   */
  static async processOrderTransactions(orderId) {
    try {
      // Find the order
      const order = await Order.findById(orderId);
      if (!order) {
        throw new Error(`Order not found: ${orderId}`);
      }

      // Check if order is paid
      if (!order.isPaid) {
        throw new Error(`Order is not paid: ${orderId}`);
      }

      // Process order transactions
      return await Wallet.processOrderTransactions(order);
    } catch (error) {
      console.error('Error processing order transactions:', error);
      throw error;
    }
  }

  /**
   * Process a payout
   * @param {String} payoutId - Payout ID
   * @returns {Promise<Object>} - Result of processing
   */
  static async processPayout(payoutId) {
    try {
      // Find the payout
      const payout = await Payout.findById(payoutId);
      if (!payout) {
        throw new Error(`Payout not found: ${payoutId}`);
      }

      // Check if payout is paid
      if (payout.status !== 'paid') {
        throw new Error(`Payout is not paid: ${payoutId}`);
      }

      // Process payout
      return await Wallet.processPayout(payout);
    } catch (error) {
      console.error('Error processing payout:', error);
      throw error;
    }
  }

  /**
   * Process a refund
   * @param {String} orderId - Order ID
   * @param {Object} refundData - Refund data
   * @returns {Promise<Object>} - Result of processing
   */
  static async processRefund(orderId, refundData) {
    try {
      // Find the order
      const order = await Order.findById(orderId);
      if (!order) {
        throw new Error(`Order not found: ${orderId}`);
      }

      // Process refund
      return await Wallet.processRefund(order, refundData);
    } catch (error) {
      console.error('Error processing refund:', error);
      throw error;
    }
  }

  /**
   * Get wallet for a user
   * @param {String} userId - User ID
   * @param {String} type - Wallet type
   * @returns {Promise<Object>} - Wallet
   */
  static async getWallet(userId, type) {
    try {
      switch (type) {
        case 'platform':
          return await Wallet.getPlatformWallet();
        case 'creator':
          return await Wallet.getCreatorWallet(userId);
        case 'buyer':
          return await Wallet.getBuyerWallet(userId);
        default:
          throw new Error(`Invalid wallet type: ${type}`);
      }
    } catch (error) {
      console.error('Error getting wallet:', error);
      throw error;
    }
  }

  /**
   * Get wallet balance for a user
   * @param {String} userId - User ID
   * @param {String} type - Wallet type
   * @returns {Promise<Object>} - Wallet balance
   */
  static async getWalletBalance(userId, type) {
    try {
      const wallet = await this.getWallet(userId, type);
      return {
        balance: wallet.balance,
        availableBalance: wallet.availableBalance,
        pendingBalance: wallet.pendingBalance,
        onHoldBalance: wallet.onHoldBalance,
        formattedBalance: wallet.formattedBalance,
        formattedAvailableBalance: wallet.formattedAvailableBalance,
        currency: wallet.currency
      };
    } catch (error) {
      console.error('Error getting wallet balance:', error);
      throw error;
    }
  }

  /**
   * Get wallet transactions for a user
   * @param {String} userId - User ID
   * @param {String} type - Wallet type
   * @param {Object} options - Additional options (limit, skip, etc.)
   * @returns {Promise<Array>} - Wallet transactions
   */
  static async getWalletTransactions(userId, type, options = {}) {
    try {
      const wallet = await this.getWallet(userId, type);
      
      // Apply filters
      let transactions = wallet.transactions;
      
      if (options.type) {
        transactions = transactions.filter(t => t.type === options.type);
      }
      
      if (options.status) {
        transactions = transactions.filter(t => t.status === options.status);
      }
      
      if (options.startDate) {
        const startDate = new Date(options.startDate);
        transactions = transactions.filter(t => new Date(t.createdAt) >= startDate);
      }
      
      if (options.endDate) {
        const endDate = new Date(options.endDate);
        transactions = transactions.filter(t => new Date(t.createdAt) <= endDate);
      }
      
      // Sort by date (newest first)
      transactions = transactions.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));
      
      // Apply pagination
      const limit = options.limit || 10;
      const skip = options.skip || 0;
      transactions = transactions.slice(skip, skip + limit);
      
      return transactions;
    } catch (error) {
      console.error('Error getting wallet transactions:', error);
      throw error;
    }
  }

  /**
   * Get wallet transaction summary for a user
   * @param {String} userId - User ID
   * @param {String} type - Wallet type
   * @returns {Promise<Object>} - Wallet transaction summary
   */
  static async getWalletTransactionSummary(userId, type) {
    try {
      const wallet = await this.getWallet(userId, type);
      return wallet.getTransactionSummary();
    } catch (error) {
      console.error('Error getting wallet transaction summary:', error);
      throw error;
    }
  }

  /**
   * Add a manual adjustment to a wallet
   * @param {String} userId - User ID
   * @param {String} type - Wallet type
   * @param {Object} adjustmentData - Adjustment data
   * @param {String} adminId - Admin ID
   * @returns {Promise<Object>} - Updated wallet
   */
  static async addManualAdjustment(userId, type, adjustmentData, adminId) {
    try {
      const wallet = await this.getWallet(userId, type);
      
      return await wallet.addTransaction({
        type: 'adjustment',
        amount: adjustmentData.amount,
        description: adjustmentData.description || 'Manual adjustment',
        reference: {
          type: 'manual',
          id: new mongoose.Types.ObjectId(),
          meta: adjustmentData.meta || {}
        },
        initiatedBy: adminId
      });
    } catch (error) {
      console.error('Error adding manual adjustment:', error);
      throw error;
    }
  }

  /**
   * Place a hold on a wallet
   * @param {String} userId - User ID
   * @param {String} type - Wallet type
   * @param {Number} amount - Amount to place on hold
   * @param {String} reason - Reason for the hold
   * @returns {Promise<Object>} - Updated wallet
   */
  static async placeHold(userId, type, amount, reason) {
    try {
      const wallet = await this.getWallet(userId, type);
      return await wallet.placeHold(amount, reason);
    } catch (error) {
      console.error('Error placing hold:', error);
      throw error;
    }
  }

  /**
   * Release a hold on a wallet
   * @param {String} userId - User ID
   * @param {String} type - Wallet type
   * @param {Number} amount - Amount to release from hold
   * @param {String} reason - Reason for releasing the hold
   * @returns {Promise<Object>} - Updated wallet
   */
  static async releaseHold(userId, type, amount, reason) {
    try {
      const wallet = await this.getWallet(userId, type);
      return await wallet.releaseHold(amount, reason);
    } catch (error) {
      console.error('Error releasing hold:', error);
      throw error;
    }
  }

  /**
   * Calculate available balance for a creator
   * @param {String} creatorId - Creator ID
   * @returns {Promise<Object>} - Available balance
   */
  static async calculateCreatorAvailableBalance(creatorId) {
    try {
      // Get total earnings from delivered orders
      const totalEarnings = await Order.aggregate([
        {
          $match: { 
            'items.creator': mongoose.Types.ObjectId(creatorId), 
            status: 'delivered',
            isPaid: true
          }
        },
        {
          $unwind: '$items'
        },
        {
          $match: { 'items.creator': mongoose.Types.ObjectId(creatorId) }
        },
        {
          $group: {
            _id: null,
            total: { $sum: { $multiply: ['$items.price', '$items.quantity'] } }
          }
        }
      ]);

      // Get platform fee percentage from the most recent order
      const recentOrder = await Order.findOne({ 
        'items.creator': creatorId,
        isPaid: true
      }).sort({ createdAt: -1 });

      const platformFeePercentage = recentOrder?.fees?.platform?.percentage || 5;

      // Calculate platform fees
      const platformFees = totalEarnings.length > 0 
        ? (totalEarnings[0].total * platformFeePercentage) / 100
        : 0;

      // Get total paid out
      const totalPaidOut = await Payout.aggregate([
        {
          $match: { 
            recipient: mongoose.Types.ObjectId(creatorId), 
            recipientType: 'creator',
            status: { $in: ['paid', 'processing', 'pending'] }
          }
        },
        {
          $group: {
            _id: null,
            total: { $sum: '$amount' }
          }
        }
      ]);

      // Calculate available balance
      const earnings = totalEarnings.length > 0 ? totalEarnings[0].total : 0;
      const paidOut = totalPaidOut.length > 0 ? totalPaidOut[0].total : 0;
      const availableBalance = earnings - platformFees - paidOut;

      return {
        totalEarnings: earnings,
        platformFees,
        totalPaidOut: paidOut,
        availableBalance
      };
    } catch (error) {
      console.error('Error calculating creator available balance:', error);
      throw error;
    }
  }

  /**
   * Sync wallet balance with calculated balance
   * @param {String} userId - User ID
   * @param {String} type - Wallet type
   * @returns {Promise<Object>} - Updated wallet
   */
  static async syncWalletBalance(userId, type) {
    try {
      const wallet = await this.getWallet(userId, type);
      
      if (type === 'creator') {
        const calculatedBalance = await this.calculateCreatorAvailableBalance(userId);
        
        // If there's a discrepancy, add an adjustment
        if (wallet.balance !== calculatedBalance.availableBalance) {
          const adjustment = calculatedBalance.availableBalance - wallet.balance;
          
          await wallet.addTransaction({
            type: 'adjustment',
            amount: adjustment,
            description: 'Balance sync adjustment',
            reference: {
              type: 'manual',
              id: new mongoose.Types.ObjectId(),
              meta: {
                reason: 'balance_sync',
                calculatedBalance: calculatedBalance.availableBalance,
                previousBalance: wallet.balance
              }
            }
          });
        }
      }
      
      return wallet;
    } catch (error) {
      console.error('Error syncing wallet balance:', error);
      throw error;
    }
  }
}

module.exports = WalletService;
