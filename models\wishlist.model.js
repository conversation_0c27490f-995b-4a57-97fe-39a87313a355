const mongoose = require('mongoose');

const wishlistSchema = new mongoose.Schema(
  {
    user: {
      type: mongoose.Schema.ObjectId,
      ref: 'User',
      required: [true, 'Wishlist must belong to a user']
    },
    products: [
      {
        product: {
          type: mongoose.Schema.ObjectId,
          ref: 'Product',
          required: [true, 'Product ID is required']
        },
        variationId: String,
        quantity: {
          type: Number,
          default: 1,
          min: [1, 'Quantity must be at least 1']
        },
        addedAt: {
          type: Date,
          default: Date.now
        }
      }
    ]
  },
  {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true }
  }
);

// Indexes
wishlistSchema.index({ user: 1 });


// Virtual to get variation details for each wishlist item
wishlistSchema.virtual('itemsWithVariationDetails').get(function() {
  const items = [];
  
  // Process products with variation details
  for (const item of this.products) {
    if (!item.product) continue;
    
    const variation = item.product.variations.id(item.variationId);
    if (!variation) continue;
    
    // Calculate discount percentage if on sale
    let discountPercentage = 0;
    if (variation.salePrice && variation.price) {
      discountPercentage = Math.round(((variation.price - variation.salePrice) / variation.price) * 100);
    }
    
    // Calculate time left for sale if applicable
    let timeLeft = null;
    if (variation.saleEndDate && variation.salePrice) {
      const now = new Date();
      const endDate = new Date(variation.saleEndDate);
      if (endDate > now) {
        const diffTime = Math.abs(endDate - now);
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
        timeLeft = diffDays;
      }
    }
    
    items.push({
      _id: item._id,
      type: 'product',
      product: {
        _id: item.product._id,
        name: item.product.name,
        images: item.product.images,
        status: item.product.status
      },
      variation: {
        _id: variation._id,
        color: variation.color,
        size: variation.size,
        price: variation.price,
        salePrice: variation.salePrice,
        currentPrice: variation.salePrice || variation.price,
        hasDiscount: !!variation.salePrice,
        discountPercentage,
        timeLeft,
        quantity: variation.quantity,
        images: variation.images || item.product.images
      },
      creator: item.product.creator,
      quantity: item.quantity,
      addedAt: item.addedAt
    });
  }
  
  // Note: Bales are now handled as products with type: 'bale'
  // No separate processing needed

  
  return items;
});

// Method to add product to wishlist
wishlistSchema.methods.addProduct = function(productId, variationId, quantity = 1) {
  // Check if product already exists with the same variation
  const existingProductIndex = this.products.findIndex(
    item =>
      item.product.toString() === productId.toString() &&
      item.variationId === variationId
  );

  if (existingProductIndex === -1) {
    // Add new product with variation and quantity
    this.products.push({
      product: productId,
      variationId,
      quantity
    });
  } else {
    // Update quantity if product with same variation already exists
    this.products[existingProductIndex].quantity = quantity;
  }

  return this.save();
};

// Method to remove product from wishlist
wishlistSchema.methods.removeProduct = function(productId, variationId) {
  const productIdStr = productId.toString();
  this.products = this.products.filter(item => {
    const itemProductId = item.product._id ? item.product._id.toString() : item.product.toString();
    return !(itemProductId === productIdStr && item.variationId?.toString() === variationId.toString());
  });
  return this.save();
};

// Method to update product quantity
wishlistSchema.methods.updateProductQuantity = function(productId, variationId, quantity) {
  const productIdStr = productId.toString();
  const product = this.products.find(item => {
    const itemProductId = item.product._id ? item.product._id.toString() : item.product.toString();
    return itemProductId === productIdStr && item.variationId?.toString() === variationId.toString();
  });

  if (product) {
    product.quantity = quantity;
  }

  return this.save();
};

// Note: Bale functionality is now handled through the addProduct method
// since bales are now products with type: 'bale'

// Method to check if product exists in wishlist
wishlistSchema.methods.hasProduct = function(productId, variationId) {
  const productIdStr = productId.toString();
  return this.products.some(item => {
    const itemProductId = item.product._id ? item.product._id.toString() : item.product.toString();
    return itemProductId === productIdStr && item.variationId?.toString() === variationId.toString();
  });
};

// Note: Bale checking is now handled through the hasProduct method
// since bales are now products with type: 'bale'

// Method to get total items count
wishlistSchema.methods.getTotalCount = function() {
  return this.products.length;
};

// Method to clear wishlist
wishlistSchema.methods.clear = function() {
  this.products = [];
  this.bales = [];
  return this.save();
};


const Wishlist = mongoose.model('Wishlist', wishlistSchema);
module.exports = Wishlist;


















