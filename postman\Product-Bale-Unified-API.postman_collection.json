{"info": {"name": "Product-Bale Unified API Tests", "description": "Test collection for the unified product model that supports both products and bales", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{authToken}}", "type": "string"}]}, "variable": [{"key": "baseUrl", "value": "http://localhost:5000/api/v1"}, {"key": "authToken", "value": ""}, {"key": "productId", "value": ""}, {"key": "baleId", "value": ""}, {"key": "variationId", "value": ""}], "item": [{"name": "Authentication", "item": [{"name": "C<PERSON> Login", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    pm.collectionVariables.set('authToken', response.token);", "    pm.test('Login successful', () => {", "        pm.expect(response.status).to.eql('success');", "        pm.expect(response.token).to.exist;", "    });", "}"]}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"password123\"\n}"}, "url": {"raw": "{{baseUrl}}/auth/login", "host": ["{{baseUrl}}"], "path": ["auth", "login"]}}}]}, {"name": "Product Management", "item": [{"name": "Create Product", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 201) {", "    const response = pm.response.json();", "    pm.collectionVariables.set('productId', response.data.product._id);", "    pm.test('Product created successfully', () => {", "        pm.expect(response.status).to.eql('success');", "        pm.expect(response.data.product.type).to.eql('product');", "        pm.expect(response.data.product.name).to.exist;", "    });", "}"]}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"type\": \"product\",\n  \"name\": \"Test T-Shirt\",\n  \"brand\": \"Test Brand\",\n  \"description\": \"A comfortable cotton t-shirt\",\n  \"basePrice\": 25.99,\n  \"category\": \"60f1b2b3c4d5e6f7a8b9c0d1\",\n  \"gender\": \"Unisex\",\n  \"highlights\": [\"100% Cotton\", \"Machine Washable\"],\n  \"variations\": [\n    {\n      \"color\": \"Red\",\n      \"size\": \"M\",\n      \"quantity\": 50,\n      \"price\": 25.99\n    },\n    {\n      \"color\": \"Blue\",\n      \"size\": \"L\",\n      \"quantity\": 30,\n      \"price\": 25.99\n    }\n  ]\n}"}, "url": {"raw": "{{baseUrl}}/creators/products", "host": ["{{baseUrl}}"], "path": ["creators", "products"]}}}, {"name": "Create <PERSON><PERSON>", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 201) {", "    const response = pm.response.json();", "    pm.collectionVariables.set('baleId', response.data.bale._id);", "    pm.test('<PERSON><PERSON> created successfully', () => {", "        pm.expect(response.status).to.eql('success');", "        pm.expect(response.data.bale.type).to.eql('bale');", "        pm.expect(response.data.bale.country).to.exist;", "        pm.expect(response.data.bale.totalItems).to.exist;", "    });", "}"]}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"type\": \"bale\",\n  \"name\": \"Mixed Clothing Bale\",\n  \"description\": \"Assorted clothing items from various brands\",\n  \"basePrice\": 150.00,\n  \"country\": \"Ghana\",\n  \"totalItems\": 50,\n  \"weight\": 25.5,\n  \"condition\": \"Good\",\n  \"dimensions\": {\n    \"length\": 100,\n    \"width\": 80,\n    \"height\": 60\n  },\n  \"variations\": [\n    {\n      \"identifier\": \"BALE-001\",\n      \"quantity\": 1,\n      \"price\": 150.00\n    },\n    {\n      \"identifier\": \"BALE-002\",\n      \"quantity\": 2,\n      \"price\": 145.00\n    }\n  ]\n}"}, "url": {"raw": "{{baseUrl}}/creators/products", "host": ["{{baseUrl}}"], "path": ["creators", "products"]}}}, {"name": "Get My Products (All Types)", "request": {"method": "GET", "url": {"raw": "{{baseUrl}}/creators/products", "host": ["{{baseUrl}}"], "path": ["creators", "products"]}}}, {"name": "Get My Products (Products Only)", "request": {"method": "GET", "url": {"raw": "{{baseUrl}}/creators/products?type=product", "host": ["{{baseUrl}}"], "path": ["creators", "products"], "query": [{"key": "type", "value": "product"}]}}}, {"name": "Get My Products (Bales Only)", "request": {"method": "GET", "url": {"raw": "{{baseUrl}}/creators/products?type=bale", "host": ["{{baseUrl}}"], "path": ["creators", "products"], "query": [{"key": "type", "value": "bale"}]}}}, {"name": "Get Product Counts (All)", "request": {"method": "GET", "url": {"raw": "{{baseUrl}}/creators/products/counts", "host": ["{{baseUrl}}"], "path": ["creators", "products", "counts"]}}}, {"name": "Get Product Counts (Products Only)", "request": {"method": "GET", "url": {"raw": "{{baseUrl}}/creators/products/counts?type=product", "host": ["{{baseUrl}}"], "path": ["creators", "products", "counts"], "query": [{"key": "type", "value": "product"}]}}}, {"name": "Get Product Counts (Bales Only)", "request": {"method": "GET", "url": {"raw": "{{baseUrl}}/creators/products/counts?type=bale", "host": ["{{baseUrl}}"], "path": ["creators", "products", "counts"], "query": [{"key": "type", "value": "bale"}]}}}]}, {"name": "Product Updates", "item": [{"name": "Update Product Basic Info", "request": {"method": "PATCH", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Updated T-Shirt Name\",\n  \"brand\": \"Updated Brand\",\n  \"description\": \"Updated description\",\n  \"basePrice\": 29.99,\n  \"gender\": \"Female\"\n}"}, "url": {"raw": "{{baseUrl}}/creators/products/{{productId}}/basic-info", "host": ["{{baseUrl}}"], "path": ["creators", "products", "{{productId}}", "basic-info"]}}}, {"name": "Update Bale Basic Info", "request": {"method": "PATCH", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Updated Bale Name\",\n  \"description\": \"Updated bale description\",\n  \"basePrice\": 175.00,\n  \"country\": \"Nigeria\",\n  \"totalItems\": 60,\n  \"weight\": 30.0,\n  \"condition\": \"Excellent\"\n}"}, "url": {"raw": "{{baseUrl}}/creators/products/{{baleId}}/basic-info", "host": ["{{baseUrl}}"], "path": ["creators", "products", "{{baleId}}", "basic-info"]}}}, {"name": "Update Product Specifications", "request": {"method": "PATCH", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"mainMaterial\": \"Cotton Blend\",\n  \"fitType\": \"Regular\",\n  \"pattern\": \"Solid\",\n  \"neckline\": \"Round Neck\",\n  \"sleeveLength\": \"Short Sleeve\"\n}"}, "url": {"raw": "{{baseUrl}}/creators/products/{{productId}}/specifications", "host": ["{{baseUrl}}"], "path": ["creators", "products", "{{productId}}", "specifications"]}}}, {"name": "Update Bale Specifications (Should Fail)", "event": [{"listen": "test", "script": {"exec": ["pm.test('Bale specifications update should fail', () => {", "    pm.expect(pm.response.code).to.eql(400);", "    const response = pm.response.json();", "    pm.expect(response.message).to.include('not applicable to bales');", "});"]}}], "request": {"method": "PATCH", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"mainMaterial\": \"Cotton\"\n}"}, "url": {"raw": "{{baseUrl}}/creators/products/{{baleId}}/specifications", "host": ["{{baseUrl}}"], "path": ["creators", "products", "{{baleId}}", "specifications"]}}}, {"name": "Update Product SEO", "request": {"method": "PATCH", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"metaTitle\": \"Best T-Shirt Ever\",\n  \"metaDescription\": \"High quality cotton t-shirt for everyday wear\",\n  \"keywords\": [\"t-shirt\", \"cotton\", \"casual\", \"comfortable\"]\n}"}, "url": {"raw": "{{baseUrl}}/creators/products/{{productId}}/seo", "host": ["{{baseUrl}}"], "path": ["creators", "products", "{{productId}}", "seo"]}}}, {"name": "Update Bale SEO", "request": {"method": "PATCH", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"metaTitle\": \"Premium Clothing Bale\",\n  \"metaDescription\": \"High quality mixed clothing bale from Ghana\",\n  \"keywords\": [\"bale\", \"clothing\", \"wholesale\", \"ghana\"]\n}"}, "url": {"raw": "{{baseUrl}}/creators/products/{{baleId}}/seo", "host": ["{{baseUrl}}"], "path": ["creators", "products", "{{baleId}}", "seo"]}}}]}, {"name": "Variation Management", "item": [{"name": "Create Product Variation", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 201) {", "    const response = pm.response.json();", "    const variations = response.data.product.variations;", "    if (variations && variations.length > 0) {", "        pm.collectionVariables.set('variationId', variations[variations.length - 1]._id);", "    }", "}"]}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"color\": \"Green\",\n  \"size\": \"XL\",\n  \"quantity\": 25,\n  \"price\": 27.99\n}"}, "url": {"raw": "{{baseUrl}}/creators/products/{{productId}}/variations", "host": ["{{baseUrl}}"], "path": ["creators", "products", "{{productId}}", "variations"]}}}, {"name": "Create Bale Variation", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"identifier\": \"BALE-003\",\n  \"quantity\": 1,\n  \"price\": 140.00\n}"}, "url": {"raw": "{{baseUrl}}/creators/products/{{baleId}}/variations", "host": ["{{baseUrl}}"], "path": ["creators", "products", "{{baleId}}", "variations"]}}}, {"name": "Update Product Variation", "request": {"method": "PATCH", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"price\": 24.99,\n  \"quantity\": 40,\n  \"salePrice\": 19.99,\n  \"saleStartDate\": \"2024-01-01T00:00:00.000Z\",\n  \"saleEndDate\": \"2024-12-31T23:59:59.999Z\"\n}"}, "url": {"raw": "{{baseUrl}}/creators/products/{{productId}}/variations/{{variationId}}", "host": ["{{baseUrl}}"], "path": ["creators", "products", "{{productId}}", "variations", "{{variationId}}"]}}}, {"name": "Update Bale Variation", "request": {"method": "PATCH", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"price\": 135.00,\n  \"quantity\": 2\n}"}, "url": {"raw": "{{baseUrl}}/creators/products/{{baleId}}/variations/{{variationId}}", "host": ["{{baseUrl}}"], "path": ["creators", "products", "{{baleId}}", "variations", "{{variationId}}"]}}}]}, {"name": "Buyer Product Feed", "item": [{"name": "Get Product Feed (All)", "request": {"method": "GET", "url": {"raw": "{{baseUrl}}/buyers/products", "host": ["{{baseUrl}}"], "path": ["buyers", "products"]}}}, {"name": "Get Product Feed (Products Only)", "request": {"method": "GET", "url": {"raw": "{{baseUrl}}/buyers/products?type=product", "host": ["{{baseUrl}}"], "path": ["buyers", "products"], "query": [{"key": "type", "value": "product"}]}}}, {"name": "Get Product Feed (Bales Only)", "request": {"method": "GET", "url": {"raw": "{{baseUrl}}/buyers/products?type=bale", "host": ["{{baseUrl}}"], "path": ["buyers", "products"], "query": [{"key": "type", "value": "bale"}]}}}]}, {"name": "Cart Management", "item": [{"name": "Add Product to Cart", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"productId\": \"{{productId}}\",\n  \"variations\": [\n    {\n      \"variationId\": \"{{variationId}}\",\n      \"quantity\": 2\n    }\n  ]\n}"}, "url": {"raw": "{{baseUrl}}/cart/products", "host": ["{{baseUrl}}"], "path": ["cart", "products"]}}}, {"name": "Add Bale to Cart (Redirects to Product)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"baleId\": \"{{baleId}}\",\n  \"variations\": [\n    {\n      \"variationId\": \"{{variationId}}\",\n      \"quantity\": 1\n    }\n  ]\n}"}, "url": {"raw": "{{baseUrl}}/cart/bales", "host": ["{{baseUrl}}"], "path": ["cart", "bales"]}}}, {"name": "Get Cart", "request": {"method": "GET", "url": {"raw": "{{baseUrl}}/cart", "host": ["{{baseUrl}}"], "path": ["cart"]}}}]}]}