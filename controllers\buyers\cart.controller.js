const Cart = require('../../models/cart.model');
const Product = require('../../models/product.model');
const Promotion = require('../../models/promotion.model');
const AppError = require('../../utils/appError');
const catchAsync = require('../../utils/catchAsync');

/**
 * Get user's cart
 * @route GET /api/v1/cart
 * @access Private
 */
exports.getCart = catchAsync(async (req, res, next) => {
  let cart = await Cart.findOne({ user: req.user.id });

  if (!cart) {
    // Create a new cart if one doesn't exist
    cart = await Cart.create({ user: req.user.id });
  }

  // If cart is empty, return early with empty structure
  if (cart.products.length === 0) {
    return res.status(200).json({
      status: 'success',
      data: {
        cart: {
          _id: cart._id,
          subtotal: 0,
          total: 0,
          itemCount: 0,
          totalQuantity: 0,
          itemsByCreator: [],
          updatedAt: cart.updatedAt
        }
      }
    });
  }

  // Group items by creator for shipping calculation
  const itemsByCreator = {};
  let totalQuantity = 0;
  
  cart.itemsWithVariationDetails.forEach(item => {
    if (!item.creator) return;
    
    // Add to total quantity
    totalQuantity += item.quantity;
    
    const creatorId = item.creator._id.toString();
    if (!itemsByCreator[creatorId]) {
      itemsByCreator[creatorId] = {
        creator: {
          _id: item.creator._id,
          name: item.creator.shopInfo?.name || item.creator.businessInfo?.businessName || item.creator.name,
          logo: item.creator.shopInfo?.logo || item.creator.photo,
          verificationStatus: item.creator.verificationStatus,
          qualityScore: item.creator.metrics?.qualityScore
        },
        items: [],
        subtotal: 0,
        itemCount: 0,
        totalQuantity: 0
      };
    }
    
    itemsByCreator[creatorId].items.push(item);
    itemsByCreator[creatorId].subtotal += item.itemTotal;
    itemsByCreator[creatorId].itemCount += 1;
    itemsByCreator[creatorId].totalQuantity += item.quantity;
  });
  
  // Separate products and bales for backward compatibility
  const products = cart.itemsWithVariationDetails.filter(item => item.product.type === 'product');
  const bales = cart.itemsWithVariationDetails.filter(item => item.product.type === 'bale');

  res.status(200).json({
    status: 'success',
    data: {
      cart: {
        _id: cart._id,
        subtotal: cart.subtotal,
        total: cart.total,
        itemCount: cart.products.length, // Number of unique items
        totalQuantity, // Total quantity including multiples of the same item
        products, // For backward compatibility
        bales, // For backward compatibility
        items: cart.itemsWithVariationDetails, // New unified array
        itemsByCreator: Object.values(itemsByCreator),
        updatedAt: cart.updatedAt
      }
    }
  });
});

/**
 * Add product to cart
 * @route POST /api/v1/cart/products
 * @access Private
 */
exports.addProductToCart = catchAsync(async (req, res, next) => {
  // Validate request body
  if (!req.body.productId) {
    return next(new AppError('Please provide product ID', 400));
  }

  // Check if variations array is provided
  if (!req.body.variations || !Array.isArray(req.body.variations) || req.body.variations.length === 0) {
    return next(new AppError('Please provide at least one variation', 400));
  }

  // Validate product exists
  const product = await Product.findById(req.body.productId);
  if (!product) {
    return next(new AppError('No product found with that ID', 404));
  }

  // Check if product is active
  if (product.status !== 'active') {
    return next(new AppError('This product is not available for purchase', 400));
  }

  // Get user's cart or create a new one
  let cart = await Cart.findOne({ user: req.user.id });
  if (!cart) {
    cart = await Cart.create({ user: req.user.id });
  }

  // Process each variation
  const errors = [];
  const addedVariations = [];

  for (const variationData of req.body.variations) {
    // Validate variation data
    if (!variationData.variationId) {
      errors.push('Variation ID is required');
      continue;
    }

    // Validate variation exists
    const variation = product.variations.id(variationData.variationId);
    if (!variation) {
      errors.push(`Variation with ID ${variationData.variationId} not found`);
      continue;
    }

    // Get quantity
    const quantity = variationData.quantity || 1;
    
    // Validate quantity
    if (quantity < 1) {
      errors.push(`Quantity must be at least 1 for variation ${variationData.variationId}`);
      continue;
    }

    // Check if variation has enough stock
    if (variation.quantity < quantity) {
      errors.push(`Not enough stock for variation ${variationData.variationId}. Only ${variation.quantity} available.`);
      continue;
    }

    // Add product to cart
    try {
      await cart.addProduct({
        productId: req.body.productId,
        variationId: variationData.variationId,
        quantity
      });
      
      addedVariations.push({
        variationId: variationData.variationId,
        quantity
      });
    } catch (err) {
      errors.push(`Failed to add variation ${variationData.variationId}: ${err.message}`);
    }
  }

  // Return response
  res.status(200).json({
    status: 'success',
    message: addedVariations.length > 0 
      ? 'Product variations added to cart' 
      : 'No variations were added to cart',
    data: {
      cart,
      addedVariations,
      errors: errors.length > 0 ? errors : undefined
    }
  });
});


/**
 * Add bale to cart (now handled as product with type: 'bale')
 * @route POST /api/v1/cart/bales
 * @access Private
 */
exports.addBaleToCart = catchAsync(async (req, res, next) => {
  // Redirect to addProductToCart with bale ID as product ID
  req.body.productId = req.body.baleId;
  delete req.body.baleId;

  // Validate bale exists (now stored as product with type: 'bale')
  const bale = await Product.findOne({ _id: req.body.productId, type: 'bale' });
  if (!bale) {
    return next(new AppError('No bale found with that ID', 404));
  }

  // Check if bale is active
  if (bale.status !== 'active') {
    return next(new AppError('This bale is not available for purchase', 400));
  }

  // Use the existing addProductToCart logic
  return exports.addProductToCart(req, res, next);
});


/**
 * Update product quantity in cart
 * @route PATCH /api/v1/cart/products/:productId/:variationId
 * @access Private
 */
exports.updateProductQuantity = catchAsync(async (req, res, next) => {
  // Validate request body
  if (!req.body.quantity) {
    return next(new AppError('Please provide a quantity', 400));
  }

  const quantity = parseInt(req.body.quantity);
  if (quantity < 1) {
    return next(new AppError('Quantity must be at least 1', 400));
  }

  // Get user's cart
  const cart = await Cart.findOne({ user: req.user.id });
  if (!cart) {
    return next(new AppError('Cart not found', 404));
  }

  const productId = req.params.productId;
  const variationId = req.params.variationId;

  // Check if product is in cart
  const productInCart = cart.products.find(
    item => 
      (item.product._id ? item.product._id.toString() : item.product.toString()) === productId && 
      item.variationId.toString() === variationId
  );

  if (!productInCart) {
    return next(new AppError('Product not found in cart', 404));
  }

  // Validate product exists
  const product = await Product.findById(productId);
  if (!product) {
    return next(new AppError('No product found with that ID', 404));
  }

  // Validate variation exists
  const variation = product.variations.id(variationId);
  if (!variation) {
    return next(new AppError('Product variation not found', 404));
  }

  // Check if variation has enough stock
  if (variation.quantity < quantity) {
    return next(new AppError(`Not enough stock. Only ${variation.quantity} available.`, 400));
  }

  // Check if product was added with a promotion
  if (productInCart.promotion && productInCart.promotion.promotionId) {
    const promotion = await Promotion.findById(productInCart.promotion.promotionId);
    if (promotion) {
      const participant = promotion.participants.find(
        p => p.creator.toString() === product.creator.toString()
      );
      
      if (participant) {
        const promoProduct = participant.products.find(
          p => p.product.toString() === productId
        );
        
        if (promoProduct) {
          // Calculate the difference in quantity
          const quantityDiff = quantity - productInCart.quantity;
          
          // Check if there's enough promotional stock
          if (promoProduct.promoStock < quantityDiff) {
            return next(new AppError(`Not enough promotional stock. Only ${promoProduct.promoStock} additional items available at promotional price.`, 400));
          }
          
          // Update promotional stock
          promoProduct.promoStock -= quantityDiff;
          await promotion.save();
        }
      }
    }
  }

  // Update product quantity
  await cart.updateProductQuantity(productId, variationId, quantity);

  res.status(200).json({
    status: 'success',
    message: 'Cart item updated',
    data: {
      cart
    }
  });
});


/**
 * Update bale quantity in cart (now handled as product with type: 'bale')
 * @route PATCH /api/v1/cart/bales/:baleId/:variationId
 * @access Private
 */
exports.updateBaleQuantity = catchAsync(async (req, res, next) => {
  // Redirect to updateProductQuantity with bale ID as product ID
  req.params.productId = req.params.baleId;
  req.params.variationId = req.params.variationId;

  // Use the existing updateProductQuantity logic
  return exports.updateProductQuantity(req, res, next);
});

/**
 * Remove product from cart
 * @route DELETE /api/v1/cart/products/:productId/:variationId
 * @access Private
 */
exports.removeProduct = catchAsync(async (req, res, next) => {
  // Get user's cart
  const cart = await Cart.findOne({ user: req.user.id });
  if (!cart) {
    return next(new AppError('Cart not found', 404));
  }

  const productId = req.params.productId;
  const variationId = req.params.variationId;

  // Check if product is in cart
  const productInCart = cart.products.find(
    item => 
      (item.product._id ? item.product._id.toString() : item.product.toString()) === productId && 
      item.variationId.toString() === variationId
  );

  if (!productInCart) {
    return next(new AppError('Product not found in cart', 404));
  }

  // Check if product was added with a promotion
  if (productInCart.promotion && productInCart.promotion.promotionId) {
    // Restore promotional stock
    const promotion = await Promotion.findById(productInCart.promotion.promotionId);
    if (promotion) {
      const product = await Product.findById(productId);
      if (product) {
        const participant = promotion.participants.find(
          p => p.creator.toString() === product.creator.toString()
        );
        
        if (participant) {
          const promoProduct = participant.products.find(
            p => p.product.toString() === productId
          );
          
          if (promoProduct) {
            promoProduct.promoStock += productInCart.quantity;
            await promotion.save();
          }
        }
      }
    }
  }

  // Remove product from cart
  await cart.removeProduct(productId, variationId);

  res.status(200).json({
    status: 'success',
    message: 'Product removed from cart',
    data: {
      cart
    }
  });
});

/**
 * Remove bale from cart (now handled as product with type: 'bale')
 * @route DELETE /api/v1/cart/bales/:id/:variationId
 * @access Private
 */
exports.removeBaleFromCart = catchAsync(async (req, res, next) => {
  // Redirect to removeProductFromCart with bale ID as product ID
  req.params.productId = req.params.id;
  req.params.variationId = req.params.variationId;

  // Use the existing removeProductFromCart logic
  return exports.removeProductFromCart(req, res, next);
});

/**
 * Clear cart
 * @route DELETE /api/v1/cart
 * @access Private
 */
exports.clearCart = catchAsync(async (req, res, next) => {
  // Get user's cart
  const cart = await Cart.findOne({ user: req.user.id });
  if (!cart) {
    return next(new AppError('Cart not found', 404));
  }

  // Clear cart items
  cart.items = [];
  cart.total = 0;

  // Save cart
  await cart.save();

  res.status(200).json({
    status: 'success',
    message: 'Cart cleared',
    data: {
      cart
    }
  });
});

/**
 * Apply coupon to cart
 * @route POST /api/v1/cart/apply-coupon
 * @access Private
 */
exports.applyCoupon = catchAsync(async (req, res, next) => {
  // Validate request body
  if (!req.body.code) {
    return next(new AppError('Please provide a coupon code', 400));
  }

  // Get user's cart
  const cart = await Cart.findOne({ user: req.user.id });
  if (!cart) {
    return next(new AppError('Cart not found', 404));
  }

  // Find the coupon
  const promotion = await Promotion.findOne({
    code: req.body.code,
    isActive: true,
    startDate: { $lte: new Date() },
    endDate: { $gte: new Date() }
  });

  if (!promotion) {
    return next(new AppError('Invalid or expired coupon code', 400));
  }

  // Check if the coupon is applicable
  if (cart.total < promotion.minPurchase) {
    return next(new AppError(`Minimum purchase amount of ${promotion.minPurchase} required`, 400));
  }

  // Apply coupon to cart
  cart.coupon = {
    code: promotion.code,
    discount: promotion.calculateDiscount(cart.total),
    type: promotion.type
  };

  // Calculate total
  cart.calculateTotal();

  // Save cart
  await cart.save();

  res.status(200).json({
    status: 'success',
    message: 'Coupon applied',
    data: {
      cart
    }
  });
});

/**
 * Remove coupon from cart
 * @route DELETE /api/v1/cart/remove-coupon
 * @access Private
 */
exports.removeCoupon = catchAsync(async (req, res, next) => {
  // Get user's cart
  const cart = await Cart.findOne({ user: req.user.id });
  if (!cart) {
    return next(new AppError('Cart not found', 404));
  }

  // Remove coupon from cart
  cart.coupon = undefined;

  // Calculate total
  cart.calculateTotal();

  // Save cart
  await cart.save();

  res.status(200).json({
    status: 'success',
    message: 'Coupon removed',
    data: {
      cart
    }
  });
});

/**
 * Update cart item quantity
 * @route PATCH /api/v1/cart/items/:id
 * @access Private
 */
exports.updateCartItem = catchAsync(async (req, res, next) => {
  // Validate request body
  if (!req.body.quantity) {
    return next(new AppError('Please provide a quantity', 400));
  }

  const quantity = parseInt(req.body.quantity);
  if (quantity < 1) {
    return next(new AppError('Quantity must be at least 1', 400));
  }

  // Get user's cart
  const cart = await Cart.findOne({ user: req.user.id });
  if (!cart) {
    return next(new AppError('Cart not found', 404));
  }

  // Find the item in the cart
  const item = cart.items.id(req.params.id);
  if (!item) {
    return next(new AppError('Item not found in cart', 404));
  }

  // Check stock availability
  if (item.type === 'product' && item.product) {
    const product = await Product.findById(item.product);
    if (!product) {
      return next(new AppError('Product not found', 404));
    }

    const variation = product.variations.find(
      v => v.color === item.color && v.size === item.size
    );

    if (!variation) {
      return next(new AppError('Product variation not found', 404));
    }

    if (variation.quantity < quantity) {
      return next(new AppError(`Not enough stock. Only ${variation.quantity} available.`, 400));
    }
  }
  // Note: Bale validation is now handled through the product validation above
  // since bales are now products with type: 'bale'

  // Update item quantity
  item.quantity = quantity;

  // Calculate total
  cart.calculateTotal();

  // Save cart
  await cart.save();

  res.status(200).json({
    status: 'success',
    message: 'Cart item updated',
    data: {
      cart
    }
  });
});





