const nodemailer = require('nodemailer');
const ejs = require('ejs');
const path = require('path');
const fs = require('fs');

/**
 * Email Service
 * Handles all email-related operations using Nodemailer
 */
class EmailService {
  constructor() {
    // Create a transporter based on environment
    if (process.env.NODE_ENV === 'production') {
      // Production transporter using SMTP
      this.transporter = nodemailer.createTransport({
        host: process.env.EMAIL_HOST || 'smtp.gmail.com',
        port: process.env.EMAIL_PORT || 587,
        secure: process.env.EMAIL_SECURE === 'true',
        auth: {
          user: process.env.EMAIL_USER,
          pass: process.env.EMAIL_PASS
        }
      });
    } else {
      // Development transporter using Ethereal (fake SMTP service)
      this.createDevTransporter();
    }

    // Set default from address
    this.from = process.env.EMAIL_FROM || '<EMAIL>';

    // Set templates directory
    this.templatesDir = path.join(__dirname, '../views/emails');
  }

  /**
   * Create a development transporter using Ethereal
   * @returns {Promise<void>}
   */
  async createDevTransporter() {
    try {
      // Generate test SMTP service account from ethereal.email
      const testAccount = await nodemailer.createTestAccount();

      // Create a transporter using the test account
      this.transporter = nodemailer.createTransport({
        host: 'smtp.ethereal.email',
        port: 587,
        secure: false,
        auth: {
          user: testAccount.user,
          pass: testAccount.pass
        }
      });

      console.log('Development email account created:', testAccount.user);
    } catch (error) {
      console.error('Failed to create development email account:', error);

      // Fallback to console logging
      this.useMockImplementation = true;
    }
  }

  /**
   * Send an email
   * @param {Object} options - Email options
   * @returns {Promise<Object>} - Email send result
   */
  async send(options) {
    const { to, subject, text, html, template, data } = options;

    // Prepare email content
    let htmlContent = html;
    let textContent = text;

    // If template is provided, render it
    if (template) {
      try {
        const templatePath = path.join(this.templatesDir, `${template}.ejs`);

        // Check if template exists
        if (fs.existsSync(templatePath)) {
          // Render template with data
          htmlContent = await ejs.renderFile(templatePath, data || {});

          // Generate text version if not provided
          if (!textContent) {
            textContent = this.htmlToText(htmlContent);
          }
        } else {
          console.warn(`Email template not found: ${template}`);
        }
      } catch (error) {
        console.error('Error rendering email template:', error);
        throw error;
      }
    }

    // Prepare email options
    const mailOptions = {
      from: options.from || this.from,
      to,
      subject,
      text: textContent || 'Please view this email in a modern email client that supports HTML.',
      html: htmlContent
    };

    // Send email
    try {
      const result = await this.transporter.sendMail(mailOptions);
      return result;
    } catch (error) {
      console.error('Error sending email:', error);
      throw error;
    }
  }

  /**
   * Send a notification email
   * @param {Object} notification - Notification object
   * @param {String} recipientEmail - Recipient email
   * @returns {Promise<Object>} - Email send result
   */
  async sendNotificationEmail(notification, recipientEmail) {
    try {
      // Prepare data for template
      const data = {
        title: notification.title,
        message: notification.message,
        actionUrl: notification.data?.url || '#',
        actionText: 'View Details',
        notification
      };

      // Send email
      return await this.send({
        to: recipientEmail,
        subject: notification.title,
        template: 'notification',
        data
      });
    } catch (error) {
      console.error('Error sending notification email:', error);
      throw error;
    }
  }

  /**
   * Send a welcome email
   * @param {Object} user - User object
   * @returns {Promise<Object>} - Email send result
   */
  async sendWelcomeEmail(user) {
    try {
      // Prepare data for template
      const data = {
        name: user.name,
        email: user.email,
        role: user.role,
        actionUrl: process.env.FRONTEND_URL || 'https://flashy.com',
        actionText: 'Get Started'
      };

      // Send email
      return await this.send({
        to: user.email,
        subject: 'Welcome to Flashy!',
        template: 'welcome',
        data
      });
    } catch (error) {
      console.error('Error sending welcome email:', error);
      throw error;
    }
  }

  /**
   * Send a password reset email
   * @param {Object} user - User object
   * @param {String} resetToken - Reset token
   * @returns {Promise<Object>} - Email send result
   */
  async sendPasswordResetEmail(user, resetToken) {
    try {
      // Prepare reset URL
      const resetURL = `${process.env.FRONTEND_URL || 'https://flashy.com'}/reset-password/${resetToken}`;

      // Prepare data for template
      const data = {
        name: user.name,
        resetURL,
        expiresIn: '10 minutes'
      };

      // Send email
      return await this.send({
        to: user.email,
        subject: 'Password Reset Request',
        template: 'passwordReset',
        data
      });
    } catch (error) {
      console.error('Error sending password reset email:', error);
      throw error;
    }
  }

  /**
   * Send an order confirmation email
   * @param {Object} order - Order object
   * @param {Object} user - User object
   * @returns {Promise<Object>} - Email send result
   */
  async sendOrderConfirmationEmail(order, user) {
    try {
      // Prepare data for template
      const data = {
        name: user.name,
        orderNumber: order.orderNumber,
        orderDate: new Date(order.createdAt).toLocaleDateString(),
        orderTotal: order.total,
        orderItems: order.items,
        shippingAddress: order.shippingAddress,
        paymentMethod: order.paymentMethod,
        actionUrl: `${process.env.FRONTEND_URL || 'https://flashy.com'}/orders/${order._id}`,
        actionText: 'View Order'
      };

      // Send email
      return await this.send({
        to: user.email,
        subject: `Order Confirmation #${order.orderNumber}`,
        template: 'orderConfirmation',
        data
      });
    } catch (error) {
      console.error('Error sending order confirmation email:', error);
      throw error;
    }
  }

  /**
   * Send an order status update email
   * @param {Object} order - Order object
   * @param {Object} user - User object
   * @returns {Promise<Object>} - Email send result
   */
  async sendOrderStatusUpdateEmail(order, user) {
    try {
      // Prepare data for template
      const data = {
        name: user.name,
        orderNumber: order.orderNumber,
        orderStatus: order.status,
        statusDate: new Date().toLocaleDateString(),
        actionUrl: `${process.env.FRONTEND_URL || 'https://flashy.com'}/orders/${order._id}`,
        actionText: 'View Order'
      };

      // Send email
      return await this.send({
        to: user.email,
        subject: `Order #${order.orderNumber} Status Update: ${order.status}`,
        template: 'orderStatusUpdate',
        data
      });
    } catch (error) {
      console.error('Error sending order status update email:', error);
      throw error;
    }
  }

  /**
   * Convert HTML to plain text (simple version)
   * @param {String} html - HTML content
   * @returns {String} - Plain text content
   */
  htmlToText(html) {
    // This is a very simple implementation
    // In a real application, you might want to use a library like html-to-text
    return html
      .replace(/<[^>]*>/g, '') // Remove HTML tags
      .replace(/\s+/g, ' ') // Replace multiple spaces with a single space
      .trim();
  }
}

// Create a singleton instance
const emailService = new EmailService();

module.exports = emailService;
