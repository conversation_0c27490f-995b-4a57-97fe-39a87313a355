{"info": {"name": "Categories Hierarchy API", "description": "Test collection for the categories hierarchy endpoint that returns all parent categories with their immediate children (levels 0 and 1 only)", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Get Categories Hierarchy - Basic", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response has correct structure', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('status', 'success');", "    pm.expect(jsonData).to.have.property('results');", "    pm.expect(jsonData).to.have.property('data');", "    pm.expect(jsonData.data).to.have.property('categories');", "    pm.expect(jsonData.data).to.have.property('totalParents');", "    pm.expect(jsonData.data).to.have.property('totalChildren');", "});", "", "pm.test('Categories are arrays', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.data.categories).to.be.an('array');", "});", "", "pm.test('Each parent category has correct structure', function () {", "    const jsonData = pm.response.json();", "    if (jsonData.data.categories.length > 0) {", "        const category = jsonData.data.categories[0];", "        pm.expect(category).to.have.property('_id');", "        pm.expect(category).to.have.property('name');", "        pm.expect(category).to.have.property('slug');", "        pm.expect(category).to.have.property('immediateChildren');", "        pm.expect(category).to.have.property('childrenCount');", "        pm.expect(category.immediateChildren).to.be.an('array');", "    }", "});", "", "pm.test('Response time is less than 3000ms', function () {", "    pm.expect(pm.response.responseTime).to.be.below(3000);", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/categories/hierarchy", "host": ["{{base_url}}"], "path": ["api", "v1", "categories", "hierarchy"]}}}, {"name": "Get Categories Hierarchy - With Counts", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Categories include count information', function () {", "    const jsonData = pm.response.json();", "    if (jsonData.data.categories.length > 0) {", "        const category = jsonData.data.categories[0];", "        pm.expect(category).to.have.property('productCount');", "        pm.expect(category).to.have.property('baleCount');", "        pm.expect(category).to.have.property('totalCount');", "        ", "        if (category.immediateChildren.length > 0) {", "            const child = category.immediate<PERSON><PERSON><PERSON>n[0];", "            pm.expect(child).to.have.property('productCount');", "            pm.expect(child).to.have.property('baleCount');", "            pm.expect(child).to.have.property('totalCount');", "        }", "    }", "});", "", "pm.test('Response time is less than 3000ms', function () {", "    pm.expect(pm.response.responseTime).to.be.below(3000);", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/categories/hierarchy?includeCounts=true", "host": ["{{base_url}}"], "path": ["api", "v1", "categories", "hierarchy"], "query": [{"key": "includeCounts", "value": "true"}]}}}, {"name": "Get Categories Hierarchy - Featured Only", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('All returned categories are featured', function () {", "    const jsonData = pm.response.json();", "    jsonData.data.categories.forEach(function(category) {", "        pm.expect(category.featured).to.be.true;", "    });", "});", "", "pm.test('Response time is less than 3000ms', function () {", "    pm.expect(pm.response.responseTime).to.be.below(3000);", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/categories/hierarchy?featured=true", "host": ["{{base_url}}"], "path": ["api", "v1", "categories", "hierarchy"], "query": [{"key": "featured", "value": "true"}]}}}, {"name": "Get Categories Hierarchy - Include Inactive", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response includes categories', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.data.categories).to.be.an('array');", "});", "", "pm.test('Response time is less than 3000ms', function () {", "    pm.expect(pm.response.responseTime).to.be.below(3000);", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/categories/hierarchy?includeInactive=true", "host": ["{{base_url}}"], "path": ["api", "v1", "categories", "hierarchy"], "query": [{"key": "includeInactive", "value": "true"}]}}}, {"name": "Get Categories Hierarchy - All Options", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response has all expected properties', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('status', 'success');", "    pm.expect(jsonData.data).to.have.property('categories');", "    pm.expect(jsonData.data).to.have.property('totalParents');", "    pm.expect(jsonData.data).to.have.property('totalChildren');", "});", "", "pm.test('Response time is less than 3000ms', function () {", "    pm.expect(pm.response.responseTime).to.be.below(3000);", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/categories/hierarchy?featured=true&includeCounts=true&includeInactive=true", "host": ["{{base_url}}"], "path": ["api", "v1", "categories", "hierarchy"], "query": [{"key": "featured", "value": "true"}, {"key": "includeCounts", "value": "true"}, {"key": "includeInactive", "value": "true"}]}}}], "variable": [{"key": "base_url", "value": "http://localhost:3000", "type": "string"}]}