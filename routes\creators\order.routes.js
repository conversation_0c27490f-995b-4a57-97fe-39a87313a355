const express = require('express');
const orderController = require('../../controllers/creators/order.controller');

const router = express.Router();


// Order routes
router.get('/', orderController.getMyOrders);
router.get('/stats', orderController.getOrderStats);
router.get('/:id', orderController.getMyOrder);
router.patch('/:id/items/:itemId', orderController.updateOrderItemStatus);
router.patch('/:id/items/:itemId/ship', orderController.shipOrderItem);

module.exports = router;
