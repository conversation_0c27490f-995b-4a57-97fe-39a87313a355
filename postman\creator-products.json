{"info": {"_postman_id": "a1b2c3d4-e5f6-7890-abcd-ef1234567890", "name": "Creator Products API", "description": "API endpoints for creator product management", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Get All Products", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{CREATOR_AUTH_TOKEN}}", "type": "text"}], "url": {"raw": "{{BASE_URL}}/api/v1/creators/products", "host": ["{{BASE_URL}}"], "path": ["api", "v1", "creators", "products"]}, "description": "Get all products for the logged-in creator"}, "response": []}, {"name": "Get Product Stats", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{CREATOR_AUTH_TOKEN}}", "type": "text"}], "url": {"raw": "{{BASE_URL}}/api/v1/creators/products/stats", "host": ["{{BASE_URL}}"], "path": ["api", "v1", "creators", "products", "stats"]}, "description": "Get product statistics for the logged-in creator"}, "response": []}, {"name": "Get Product by ID", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{CREATOR_AUTH_TOKEN}}", "type": "text"}], "url": {"raw": "{{BASE_URL}}/api/v1/creators/products/{{productId}}", "host": ["{{BASE_URL}}"], "path": ["api", "v1", "creators", "products", "{{productId}}"]}, "description": "Get a specific product by ID for the logged-in creator"}, "response": []}, {"name": "Create Product", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{CREATOR_AUTH_TOKEN}}", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "name", "value": "Premium Denim Jeans", "type": "text"}, {"key": "brand", "value": "Fashion Brand", "type": "text"}, {"key": "description", "value": "High-quality denim jeans with premium stitching and comfortable fit.", "type": "text"}, {"key": "basePrice", "value": "89.99", "type": "text"}, {"key": "category", "value": "{{categoryId}}", "type": "text", "description": "ID of the Jeans category"}, {"key": "gender", "value": "Men", "type": "text"}, {"key": "productImages", "type": "file", "src": "/path/to/image1.jpg", "description": "First product image"}, {"key": "productImages", "type": "file", "src": "/path/to/image2.jpg", "description": "Second product image"}, {"key": "highlights", "value": "[\"Premium quality denim\", \"Comfortable fit\", \"Durable stitching\"]", "type": "text"}, {"key": "tags", "value": "[\"jeans\", \"denim\", \"men's fashion\"]", "type": "text"}, {"key": "specifications", "value": "{\"material\":\"100% Cotton Denim\", \"fitType\":\"Regular\", \"care\":\"Machine wash cold\", \"origin\":\"Ghana\", \"season\":[\"All Season\"], \"occasion\":[\"Casual\", \"Everyday\"]}", "type": "text"}, {"key": "variations", "value": "[{\"color\":\"Blue\",\"size\":\"32\",\"quantity\":10,\"price\":89.99,\"salePrice\":79.99,\"saleStartDate\":\"2023-12-01\",\"saleEndDate\":\"2023-12-31\"},{\"color\":\"Black\",\"size\":\"34\",\"quantity\":15,\"price\":89.99}]", "type": "text"}, {"key": "relatedCategories", "value": "[\"{{relatedCategoryId1}}\", \"{{relatedCategoryId2}}\"]", "type": "text", "description": "Optional related categories"}]}, "url": {"raw": "{{BASE_URL}}/api/v1/creators/products", "host": ["{{BASE_URL}}"], "path": ["api", "v1", "creators", "products"]}, "description": "Create a new product with images"}, "response": []}, {"name": "Update Product", "request": {"method": "PATCH", "header": [{"key": "Authorization", "value": "Bearer {{CREATOR_AUTH_TOKEN}}", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "name", "value": "Updated Premium Denim Jeans", "type": "text"}, {"key": "description", "value": "Updated description with more details about the premium jeans.", "type": "text"}, {"key": "basePrice", "value": "99.99", "type": "text"}, {"key": "productImages", "type": "file", "src": "/path/to/new_image.jpg", "description": "New product image"}, {"key": "variations", "value": "[{\"_id\":\"{{variationId}}\",\"quantity\":20},{\"color\":\"Navy\",\"size\":\"36\",\"quantity\":8,\"price\":99.99}]", "type": "text", "description": "Update existing variation and add a new one"}, {"key": "specifications", "value": "{\"fitType\":\"<PERSON>\", \"care\":\"Machine wash cold, tumble dry low\"}", "type": "text"}]}, "url": {"raw": "{{BASE_URL}}/api/v1/creators/products/{{productId}}", "host": ["{{BASE_URL}}"], "path": ["api", "v1", "creators", "products", "{{productId}}"]}, "description": "Update an existing product"}, "response": []}, {"name": "Delete Product", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{CREATOR_AUTH_TOKEN}}", "type": "text"}], "url": {"raw": "{{BASE_URL}}/api/v1/creators/products/{{productId}}", "host": ["{{BASE_URL}}"], "path": ["api", "v1", "creators", "products", "{{productId}}"]}, "description": "Delete a product by ID"}, "response": []}, {"name": "Filter Products by Status", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{CREATOR_AUTH_TOKEN}}", "type": "text"}], "url": {"raw": "{{BASE_URL}}/api/v1/creators/products?status=pending", "host": ["{{BASE_URL}}"], "path": ["api", "v1", "creators", "products"], "query": [{"key": "status", "value": "pending"}]}, "description": "Get products filtered by status (pending, active, inactive, rejected)"}, "response": []}, {"name": "Search Products", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{CREATOR_AUTH_TOKEN}}", "type": "text"}], "url": {"raw": "{{BASE_URL}}/api/v1/creators/products?search=jeans", "host": ["{{BASE_URL}}"], "path": ["api", "v1", "creators", "products"], "query": [{"key": "search", "value": "jeans"}]}, "description": "Search products by name, description, or brand"}, "response": []}, {"name": "Sort Products", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{CREATOR_AUTH_TOKEN}}", "type": "text"}], "url": {"raw": "{{BASE_URL}}/api/v1/creators/products?sort=-createdAt", "host": ["{{BASE_URL}}"], "path": ["api", "v1", "creators", "products"], "query": [{"key": "sort", "value": "-createdAt"}]}, "description": "Sort products by creation date (newest first)"}, "response": []}, {"name": "Paginate Products", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{CREATOR_AUTH_TOKEN}}", "type": "text"}], "url": {"raw": "{{BASE_URL}}/api/v1/creators/products?page=1&limit=10", "host": ["{{BASE_URL}}"], "path": ["api", "v1", "creators", "products"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}]}, "description": "Get paginated products (10 per page)"}, "response": []}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "variable": [{"key": "BASE_URL", "value": "http://localhost:5000", "type": "string"}, {"key": "productId", "value": "", "type": "string", "description": "ID of a product to view, update, or delete"}, {"key": "categoryId", "value": "", "type": "string", "description": "ID of a category for the product"}, {"key": "relatedCategoryId1", "value": "", "type": "string", "description": "ID of a related category"}, {"key": "relatedCategoryId2", "value": "", "type": "string", "description": "ID of another related category"}, {"key": "variationId", "value": "", "type": "string", "description": "ID of a variation to update"}]}