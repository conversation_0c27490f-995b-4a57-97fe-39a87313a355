const Promotion = require('../../models/promotion.model');
const Product = require('../../models/product.model');
const AppError = require('../../utils/appError');
const catchAsync = require('../../utils/catchAsync');

/**
 * Get all active promotions
 * @route GET /api/v1/promotions
 * @access Public
 */
exports.getAllPromotions = catchAsync(async (req, res, next) => {
  // Build query
  const queryObj = { ...req.query };
  const excludedFields = ['page', 'sort', 'limit', 'fields', 'search'];
  excludedFields.forEach(el => delete queryObj[el]);

  // Filter for active promotions by default
  if (!queryObj.isActive) {
    queryObj.isActive = true;
  }

  // Filter for non-expired promotions by default
  if (!queryObj.endDate) {
    queryObj.endDate = { $gte: new Date() };
  }

  // Advanced filtering
  let queryStr = JSON.stringify(queryObj);
  queryStr = queryStr.replace(/\b(gte|gt|lte|lt)\b/g, match => `$${match}`);

  let query = Promotion.find(JSON.parse(queryStr));

  // Search functionality
  if (req.query.search) {
    const searchRegex = new RegExp(req.query.search, 'i');
    query = query.find({
      $or: [
        { name: searchRegex },
        { code: searchRegex },
        { description: searchRegex }
      ]
    });
  }

  // Sorting
  if (req.query.sort) {
    const sortBy = req.query.sort.split(',').join(' ');
    query = query.sort(sortBy);
  } else {
    query = query.sort('-startDate');
  }

  // Field limiting
  if (req.query.fields) {
    const fields = req.query.fields.split(',').join(' ');
    query = query.select(fields);
  } else {
    query = query.select('-__v');
  }

  // Pagination
  const page = req.query.page * 1 || 1;
  const limit = req.query.limit * 1 || 10;
  const skip = (page - 1) * limit;

  query = query.skip(skip).limit(limit);

  // Execute query
  const promotions = await query;

  // Get total count for pagination
  const total = await Promotion.countDocuments(JSON.parse(queryStr));

  res.status(200).json({
    status: 'success',
    results: promotions.length,
    total,
    page,
    limit,
    data: {
      promotions
    }
  });
});

/**
 * Get promotion by ID
 * @route GET /api/v1/promotions/:id
 * @access Public
 */
exports.getPromotion = catchAsync(async (req, res, next) => {
  const promotion = await Promotion.findById(req.params.id);

  if (!promotion) {
    return next(new AppError('No promotion found with that ID', 404));
  }

  res.status(200).json({
    status: 'success',
    data: {
      promotion
    }
  });
});

/**
 * Join a promotion as a creator
 * @route POST /api/v1/promotions/:id/join
 * @access Private (Creator only)
 */
exports.joinPromotion = catchAsync(async (req, res, next) => {
  // 1. Find the promotion
  const promotion = await Promotion.findById(req.params.id);

  if (!promotion) {
    return next(new AppError('No promotion found with that ID', 404));
  }

  // 2. Check if promotion is active and not expired
  if (!promotion.isActive || promotion.isExpired) {
    return next(new AppError('This promotion is not active or has expired', 400));
  }

  // 3. Check if creator is already participating
  const isParticipating = promotion.participants.some(
    participant => participant.creator.toString() === req.user.id
  );

  if (isParticipating) {
    return next(new AppError('You are already participating in this promotion', 400));
  }

  // 4. Validate products
  if (!req.body.products || !Array.isArray(req.body.products) || req.body.products.length === 0) {
    return next(new AppError('Please provide at least one product for the promotion', 400));
  }

  // Validate product structure
  for (const item of req.body.products) {
    if (!item.productId) {
      return next(new AppError('Each product must have a productId', 400));
    }
    if (item.discountValue === undefined || item.discountValue < 0) {
      return next(new AppError('Each product must have a valid discountValue', 400));
    }
    if (item.promoPrice === undefined || item.promoPrice < 0) {
      return next(new AppError('Each product must have a valid promoPrice', 400));
    }
    if (item.promoStock === undefined || item.promoStock < 0) {
      return next(new AppError('Each product must have a valid promoStock', 400));
    }
  }

  // 5. Check if products belong to the creator
  const productIds = req.body.products.map(item => item.productId);
  const products = await Product.find({
    _id: { $in: productIds },
    creator: req.user.id
  });

  if (products.length !== productIds.length) {
    return next(new AppError('Some products do not belong to you or do not exist', 400));
  }

  // 6. Check if products meet the promotion criteria
  const { minDiscount, maxDiscount } = promotion.criteria;

  // Create a map of products for easy access
  const productMap = {};
  products.forEach(product => {
    productMap[product._id.toString()] = product;
  });

  // Check if all products have the required discount
  const invalidProducts = req.body.products.filter(item => {
    const product = productMap[item.productId];
    const originalPrice = product.price;
    const discountPercentage = ((originalPrice - item.promoPrice) / originalPrice) * 100;

    return discountPercentage < minDiscount ||
           (maxDiscount && discountPercentage > maxDiscount);
  });

  if (invalidProducts.length > 0) {
    return next(new AppError(
      `Some products do not meet the promotion criteria. Required discount: ${minDiscount}% - ${maxDiscount || 'unlimited'}%`,
      400
    ));
  }

  // 7. Format products for the promotion
  const formattedProducts = req.body.products.map(item => ({
    product: item.productId,
    discountValue: item.discountValue,
    promoPrice: item.promoPrice,
    promoStock: item.promoStock
  }));

  // 8. Add creator to participants
  promotion.participants.push({
    creator: req.user.id,
    products: formattedProducts,
    status: 'pending'
  });

  await promotion.save();

  res.status(200).json({
    status: 'success',
    message: 'Successfully joined the promotion. Your products are pending approval.',
    data: {
      promotion
    }
  });
});

/**
 * Update creator's products in a promotion
 * @route PATCH /api/v1/promotions/:id/products
 * @access Private (Creator only)
 */
exports.updatePromotionProducts = catchAsync(async (req, res, next) => {
  // 1. Find the promotion
  const promotion = await Promotion.findById(req.params.id);

  if (!promotion) {
    return next(new AppError('No promotion found with that ID', 404));
  }

  // 2. Check if creator is participating
  const participantIndex = promotion.participants.findIndex(
    participant => participant.creator.toString() === req.user.id
  );

  if (participantIndex === -1) {
    return next(new AppError('You are not participating in this promotion', 400));
  }

  // 3. Validate products
  if (!req.body.products || !Array.isArray(req.body.products) || req.body.products.length === 0) {
    return next(new AppError('Please provide at least one product for the promotion', 400));
  }

  // Validate product structure
  for (const item of req.body.products) {
    if (!item.productId) {
      return next(new AppError('Each product must have a productId', 400));
    }
    if (item.discountValue === undefined || item.discountValue < 0) {
      return next(new AppError('Each product must have a valid discountValue', 400));
    }
    if (item.promoPrice === undefined || item.promoPrice < 0) {
      return next(new AppError('Each product must have a valid promoPrice', 400));
    }
    if (item.promoStock === undefined || item.promoStock < 0) {
      return next(new AppError('Each product must have a valid promoStock', 400));
    }
  }

  // 4. Check if products belong to the creator
  const productIds = req.body.products.map(item => item.productId);
  const products = await Product.find({
    _id: { $in: productIds },
    creator: req.user.id
  });

  if (products.length !== productIds.length) {
    return next(new AppError('Some products do not belong to you or do not exist', 400));
  }

  // 5. Check if products meet the promotion criteria
  const { minDiscount, maxDiscount } = promotion.criteria;

  // Create a map of products for easy access
  const productMap = {};
  products.forEach(product => {
    productMap[product._id.toString()] = product;
  });

  // Check if all products have the required discount
  const invalidProducts = req.body.products.filter(item => {
    const product = productMap[item.productId];
    const originalPrice = product.price;
    const discountPercentage = ((originalPrice - item.promoPrice) / originalPrice) * 100;

    return discountPercentage < minDiscount ||
           (maxDiscount && discountPercentage > maxDiscount);
  });

  if (invalidProducts.length > 0) {
    return next(new AppError(
      `Some products do not meet the promotion criteria. Required discount: ${minDiscount}% - ${maxDiscount || 'unlimited'}%`,
      400
    ));
  }

  // 6. Format products for the promotion
  const formattedProducts = req.body.products.map(item => ({
    product: item.productId,
    discountValue: item.discountValue,
    promoPrice: item.promoPrice,
    promoStock: item.promoStock
  }));

  // 7. Update products
  promotion.participants[participantIndex].products = formattedProducts;
  promotion.participants[participantIndex].status = 'pending';

  await promotion.save();

  res.status(200).json({
    status: 'success',
    message: 'Successfully updated promotion products. Your products are pending approval.',
    data: {
      promotion
    }
  });
});

/**
 * Leave a promotion
 * @route DELETE /api/v1/promotions/:id/leave
 * @access Private (Creator only)
 */
exports.leavePromotion = catchAsync(async (req, res, next) => {
  // 1. Find the promotion
  const promotion = await Promotion.findById(req.params.id);

  if (!promotion) {
    return next(new AppError('No promotion found with that ID', 404));
  }

  // 2. Check if creator is participating
  const participantIndex = promotion.participants.findIndex(
    participant => participant.creator.toString() === req.user.id
  );

  if (participantIndex === -1) {
    return next(new AppError('You are not participating in this promotion', 400));
  }

  // 3. Remove creator from participants
  promotion.participants.splice(participantIndex, 1);

  await promotion.save();

  res.status(204).json({
    status: 'success',
    data: null
  });
});

/**
 * Get creator's promotions
 * @route GET /api/v1/promotions/my
 * @access Private (Creator only)
 */
exports.getMyPromotions = catchAsync(async (req, res, next) => {
  // Build query
  let query = Promotion.find({
    'participants.creator': req.user.id
  });

  // Filter by status if provided
  if (req.query.status) {
    query = query.find({
      'participants': {
        $elemMatch: {
          'creator': req.user.id,
          'status': req.query.status
        }
      }
    });
  }

  // Filter for active promotions if requested
  if (req.query.active === 'true') {
    query = query.find({
      isActive: true,
      endDate: { $gte: new Date() }
    });
  }

  // Sorting
  if (req.query.sort) {
    const sortBy = req.query.sort.split(',').join(' ');
    query = query.sort(sortBy);
  } else {
    query = query.sort('-startDate');
  }

  // Pagination
  const page = req.query.page * 1 || 1;
  const limit = req.query.limit * 1 || 10;
  const skip = (page - 1) * limit;

  query = query.skip(skip).limit(limit);

  // Execute query
  const promotions = await query;

  // Get total count for pagination
  const total = await Promotion.countDocuments({
    'participants.creator': req.user.id
  });

  res.status(200).json({
    status: 'success',
    results: promotions.length,
    total,
    page,
    limit,
    data: {
      promotions
    }
  });
});
