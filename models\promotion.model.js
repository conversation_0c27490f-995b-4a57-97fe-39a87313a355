const mongoose = require('mongoose');
const NotificationService = require('../services/notification.service');

const promotionSchema = new mongoose.Schema(
  {
    name: {
      type: String,
      required: [true, 'A promotion must have a name'],
      trim: true,
      maxlength: [100, 'A promotion name must have less or equal than 100 characters']
    },
    code: {
      type: String,
      required: [true, 'A promotion must have a code'],
      unique: true,
      uppercase: true,
      trim: true
    },
    type: {
      type: String,
      enum: ['percentage', 'fixed'],
      required: [true, 'A promotion must have a type']
    },
    startDate: {
      type: Date,
      required: [true, 'A promotion must have a start date']
    },
    endDate: {
      type: Date,
      required: [true, 'A promotion must have an end date']
    },
    isActive: {
      type: Boolean,
      default: true
    },
    usedCount: {
      type: Number,
      default: 0
    },
    // Admin who created the promotion
    admin: {
      type: mongoose.Schema.ObjectId,
      ref: 'User',
      required: [true, 'A promotion must have an admin creator']
    },
    // Participating creators and their products
    participants: [
      {
        creator: {
          type: mongoose.Schema.ObjectId,
          ref: 'User',
          required: true
        },
        products: [
          {
            product: {
              type: mongoose.Schema.ObjectId,
              ref: 'Product',
              required: true
            },
            // For percentage discounts, this is the percentage off
            // For fixed discounts, this is the amount off
            discountValue: {
              type: Number,
              required: true,
              min: [0, 'Discount value cannot be negative']
            },
            // Optional override for specific products (can be different from global value)
            discountType: {
              type: String,
              enum: ['percentage', 'fixed'],
              default: function() {
                // Default to the promotion's global type
                return this.parent().parent().parent().type;
              }
            },
            // Stock allocated for this promotion
            promoStock: {
              type: Number,
              required: true,
              min: [0, 'Promotional stock cannot be negative']
            },
            addedAt: {
              type: Date,
              default: Date.now
            }
          }
        ],
        joinedAt: {
          type: Date,
          default: Date.now
        },
        status: {
          type: String,
          enum: ['pending', 'approved', 'rejected'],
          default: 'pending'
        }
      }
    ],
    // Promotion criteria
    criteria: {
      minDiscount: {
        type: Number,
        min: [0, 'Minimum discount cannot be negative'],
        default: 0
      },
      maxDiscount: {
        type: Number,
        min: [0, 'Maximum discount cannot be negative']
      },
      categories: [
        {
          type: mongoose.Schema.ObjectId,
          ref: 'Category'
        }
      ],
    },
    description: String,
    // Banner image for the promotion
    banners: [{
      type: String,
      required: [true, 'A promotion must have a banner image']
    }]
  },
  {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true }
  }
);

// Indexes
promotionSchema.index({ startDate: 1, endDate: 1 });
promotionSchema.index({ admin: 1 });
promotionSchema.index({ 'participants.creator': 1 });
promotionSchema.index({ isActive: 1 });

// Virtual property to check if promotion is expired
promotionSchema.virtual('isExpired').get(function() {
  return Date.now() > this.endDate;
});

// Virtual property to check if promotion is valid
promotionSchema.virtual('isValid').get(function() {
  return (
    this.isActive &&
    Date.now() >= this.startDate &&
    Date.now() <= this.endDate
  );
});

// Virtual property to check if promotion is upcoming
promotionSchema.virtual('isUpcoming').get(function() {
  return Date.now() < this.startDate;
});

// Virtual property to get promotion status
promotionSchema.virtual('status').get(function() {
  if (!this.isActive) return 'inactive';
  if (this.isExpired) return 'expired';
  if (this.isUpcoming) return 'upcoming';
  return 'active';
});

// Virtual property to get time remaining until expiration
promotionSchema.virtual('timeRemaining').get(function() {
  if (this.isExpired) return '0 days';
  
  const now = new Date();
  const end = new Date(this.endDate);
  const diffTime = Math.abs(end - now);
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  
  if (diffDays < 1) {
    // Less than a day, show hours
    const diffHours = Math.ceil(diffTime / (1000 * 60 * 60));
    return `${diffHours} hour${diffHours !== 1 ? 's' : ''}`;
  }
  
  return `${diffDays} day${diffDays !== 1 ? 's' : ''}`;
});

// Virtual property to get duration of promotion in days
promotionSchema.virtual('durationDays').get(function() {
  const start = new Date(this.startDate);
  const end = new Date(this.endDate);
  const diffTime = Math.abs(end - start);
  return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
});

// Virtual property to get total products in promotion
promotionSchema.virtual('totalProducts').get(function() {
  if (!this.participants || this.participants.length === 0) return 0;
  
  return this.participants.reduce((total, participant) => {
    return total + (participant.products ? participant.products.length : 0);
  }, 0);
});

// Virtual property to get approved participants count
promotionSchema.virtual('approvedParticipantsCount').get(function() {
  if (!this.participants || this.participants.length === 0) return 0;
  
  return this.participants.filter(p => p.status === 'approved').length;
});

// Virtual property to get pending participants count
promotionSchema.virtual('pendingParticipantsCount').get(function() {
  if (!this.participants || this.participants.length === 0) return 0;
  
  return this.participants.filter(p => p.status === 'pending').length;
});



// Pre-save middleware to validate dates
promotionSchema.pre('save', function(next) {
  // Ensure end date is after start date
  if (this.endDate <= this.startDate) {
    return next(new Error('End date must be after start date'));
  }
  
  // Ensure promotion duration is at least 1 day
  const start = new Date(this.startDate);
  const end = new Date(this.endDate);
  const diffTime = Math.abs(end - start);
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  
  if (diffDays < 1) {
    return next(new Error('Promotion must last at least 1 day'));
  }
  
  next();
});

// Pre-save middleware to validate participants
promotionSchema.pre('save', function(next) {
  // Skip validation if participants are not modified
  if (!this.isModified('participants')) return next();
  
  // Ensure each participant has at least one product
  if (this.participants && this.participants.length > 0) {
    const invalidParticipants = this.participants.filter(
      p => !p.products || p.products.length === 0
    );
    
    if (invalidParticipants.length > 0) {
      return next(new Error('Each participant must have at least one product'));
    }
  }
  
  next();
});


// Instance method to add a participant
promotionSchema.methods.addParticipant = async function(creatorId, products = []) {
  // Check if creator is already a participant
  const existingParticipant = this.participants.find(
    p => p.creator?._id.toString() === creatorId.toString()
  );
  
  if (existingParticipant) {
    // Add products to existing participant
    products.forEach(product => {
      // Check if product already exists
      const existingProduct = existingParticipant.products.find(
        p => p.product?._id.toString() === product.product.toString()
      );
      
      if (!existingProduct) {
        existingParticipant.products.push(product);
      }
    });
  } else {
    // Add new participant
    this.participants.push({
      creator: creatorId,
      products,
      status: 'pending'
    });
  }
  
  await this.save();
  return this;
};

// Instance method to approve a participant
promotionSchema.methods.approveParticipant = async function(creatorId) {
  const participant = this.participants.find(
    p => p.creator?._id.toString() === creatorId.toString()
  );
  
  if (!participant) {
    throw new Error('Participant not found');
  }
  
  participant.status = 'approved';
  await this.save();
  return this;
};

// Instance method to reject a participant
promotionSchema.methods.rejectParticipant = async function(creatorId) {
  const participant = this.participants.find(
    p => p.creator?._id.toString() === creatorId.toString()
  );
  
  if (!participant) {
    throw new Error('Participant not found');
  }
  
  participant.status = 'rejected';
  await this.save();
  return this;
};

// Instance method to check if a product is in the promotion
promotionSchema.methods.hasProduct = function(productId) {
  return this.participants.some(participant => 
    participant.products.some(product => 
      product.product?._id.toString() === productId.toString()
    )
  );
};

// Instance method to get product promotion details
promotionSchema.methods.getProductDetails = function(productId) {
  for (const participant of this.participants) {
    for (const product of participant.products) {
      if (product.product?._id.toString() === productId.toString()) {
        return {
          discountValue: product.discountValue,
          discountType: product.discountType,
          promoStock: product.promoStock,
          creator: participant.creator,
          status: participant.status
        };
      }
    }
  }
  return null;
};

const Promotion = mongoose.model('Promotion', promotionSchema);

// Middleware to send notifications on various promotion events
promotionSchema.pre('save', async function(next) {
  try {
    // Check if this is a new promotion
    if (this.isNew) {
      // Notify admin who created the promotion
      if (this.admin) {
        await NotificationService.createPromotionNotification({
          promotion: this,
          type: 'created',
          recipient: this.admin
        });
      }
    }

    // Check for changes in promotion status
    if (this.isModified('isActive')) {
      const status = this.isActive ? 'approved' : 'rejected';
      
      // Notify participants about status change
      for (let participant of this.participants) {
        if (participant.creator) {
          await NotificationService.createPromotionNotification({
            promotion: this,
            type: status,
            recipient: participant.creator
          });
        }
      }
    }

    next();
  } catch (error) {
    console.error('Error in promotion pre-save middleware:', error);
    next(error);
  }
});

// Background task to check and notify about expiring promotions
promotionSchema.statics.checkExpiringPromotions = async function() {
  try {
    // Find promotions expiring within the next 3 days
    const now = new Date();
    const threeDaysLater = new Date(now.getTime() + 3 * 24 * 60 * 60 * 1000);

    const expiringPromotions = await this.find({
      isActive: true,
      endDate: { 
        $gte: now, 
        $lte: threeDaysLater 
      }
    });

    for (let promotion of expiringPromotions) {
      // Notify admin
      if (promotion.admin) {
        await NotificationService.createPromotionNotification({
          promotion,
          type: 'expiring_soon',
          recipient: promotion.admin
        });

      // Notify all participants
      for (let participant of promotion.participants) {
        if (participant.creator) {
          await NotificationService.createPromotionNotification({
            promotion,
            type: 'expiring_soon',
            recipient: participant.creator
          });
        }
      }
    }
  } 
 }catch (error) {
    console.error('Error checking expiring promotions:', error);
  }
};

// Method to add a participant with notification
promotionSchema.methods.addParticipantWithNotification = async function(creatorId, products = []) {
  try {
    // Add participant using existing method
    await this.addParticipant(creatorId, products);

    // Notify admin about new participant
    if (this.admin) {
      await NotificationService.createPromotionNotification({
        promotion: this,
        type: 'participant_joined',
        recipient: this.admin
      });
    }

    return this;
  } catch (error) {
    console.error('Error adding participant with notification:', error);
    throw error;
  }
};

module.exports = Promotion;
