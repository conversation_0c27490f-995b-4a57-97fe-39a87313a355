const express = require('express');
const walletController = require('../../controllers/creators/wallet.controller');
const authMiddleware = require('../../middleware/auth.middleware');

const router = express.Router();

// Protect all routes
router.use(authMiddleware.protect);
router.use(authMiddleware.restrictTo('creator'));

// Wallet routes
router.get('/balance', walletController.getWalletBalance);
router.get('/transactions', walletController.getWalletTransactions);
router.get('/summary', walletController.getWalletTransactionSummary);
router.get('/earnings', walletController.getEarnings);

module.exports = router;
