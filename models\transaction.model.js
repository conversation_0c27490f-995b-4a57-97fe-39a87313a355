const mongoose = require('mongoose');

// ===== TRANSACTION MODEL (Standalone) =====
const transactionSchema = new mongoose.Schema({
  // Basic transaction info
  type: {
    type: String,
    enum: [
      'payment',           // Customer payment
      'earning',           // Creator earning
      'platform_fee',      // Platform commission
      'processing_fee',    // Payment processing fee
      'delivery_fee',      // Delivery/shipping fee
      'payout',            // Creator payout
      'refund',            // Refund to customer
      'adjustment'         // Manual adjustment
    ],
    required: true
  },

  // Amount (positive = credit, negative = debit)
  amount: {
    type: Number,
    required: true,
    validate: {
      validator: function(value) {
        return value !== 0;
      },
      message: 'Transaction amount cannot be zero'
    }
  },

  // Who this affects (null for platform transactions)
  user: {
    type: mongoose.Schema.ObjectId,
    ref: 'User',
    required: function() {
      return this.userType !== 'platform';
    }
  },

  userType: {
    type: String,
    enum: ['buyer', 'creator', 'platform', 'delivery'],
    required: true
  },

  // Status
  status: {
    type: String,
    enum: ['pending', 'completed', 'failed'],
    default: 'completed'
  },

  // Description
  description: {
    type: String,
    required: true,
    trim: true,
    maxlength: [500, 'Description cannot exceed 500 characters']
  },

  // Reference to source (order, payment, payout, etc.)
  sourceType: {
    type: String,
    enum: ['order', 'payment', 'payout', 'manual', 'refund'],
    required: true
  },

  sourceId: {
    type: mongoose.Schema.ObjectId,
    required: function() {
      return this.sourceType !== 'manual';
    }
  },

  // Currency
  currency: {
    type: String,
    default: 'GHS',
    uppercase: true,
    enum: ['GHS', 'USD', 'EUR', 'GBP']
  },

  // Metadata
  meta: mongoose.Schema.Types.Mixed,

  // Processing information
  processedAt: {
    type: Date,
    default: function() {
      return this.status === 'completed' ? Date.now() : undefined;
    }
  },

  // Admin who created manual transactions
  createdBy: {
    type: mongoose.Schema.ObjectId,
    ref: 'User',
    required: function() {
      return this.type === 'adjustment';
    }
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// ===== INDEXES =====
// Indexes for optimal query performance
transactionSchema.index({ user: 1, type: 1 });
transactionSchema.index({ user: 1, status: 1 });
transactionSchema.index({ user: 1, createdAt: -1 });
transactionSchema.index({ sourceType: 1, sourceId: 1 });
transactionSchema.index({ type: 1, status: 1 });
transactionSchema.index({ userType: 1, type: 1 });
transactionSchema.index({ createdAt: -1 });
transactionSchema.index({ status: 1, processedAt: -1 });

// Compound indexes for common queries
transactionSchema.index({ user: 1, type: 1, status: 1, createdAt: -1 });
transactionSchema.index({ userType: 1, type: 1, createdAt: -1 });

// ===== VIRTUAL PROPERTIES =====
transactionSchema.virtual('formattedAmount').get(function() {
  const currencySymbols = {
    'GHS': '₵',
    'USD': '$',
    'EUR': '€',
    'GBP': '£'
  };
  
  const symbol = currencySymbols[this.currency] || this.currency;
  const sign = this.amount >= 0 ? '+' : '';
  return `${sign}${symbol}${Math.abs(this.amount).toFixed(2)}`;
});

transactionSchema.virtual('isCredit').get(function() {
  return this.amount > 0;
});

transactionSchema.virtual('isDebit').get(function() {
  return this.amount < 0;
});

transactionSchema.virtual('timeAgo').get(function() {
  const now = new Date();
  const created = new Date(this.createdAt);
  const diffTime = Math.abs(now - created);
  const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));

  if (diffDays === 0) {
    const diffHours = Math.floor(diffTime / (1000 * 60 * 60));
    if (diffHours === 0) {
      const diffMinutes = Math.floor(diffTime / (1000 * 60));
      return `${diffMinutes} minute${diffMinutes !== 1 ? 's' : ''} ago`;
    }
    return `${diffHours} hour${diffHours !== 1 ? 's' : ''} ago`;
  } else if (diffDays < 7) {
    return `${diffDays} day${diffDays !== 1 ? 's' : ''} ago`;
  } else {
    const diffWeeks = Math.floor(diffDays / 7);
    return `${diffWeeks} week${diffWeeks !== 1 ? 's' : ''} ago`;
  }
});

transactionSchema.virtual('statusColor').get(function() {
  const statusColors = {
    'pending': 'yellow',
    'completed': 'green',
    'failed': 'red'
  };
  
  return statusColors[this.status] || 'gray';
});

// ===== INSTANCE METHODS =====
transactionSchema.methods.markAsCompleted = async function() {
  this.status = 'completed';
  this.processedAt = Date.now();
  return this.save();
};

transactionSchema.methods.markAsFailed = async function(reason) {
  this.status = 'failed';
  if (reason) {
    this.meta = { ...this.meta, failureReason: reason };
  }
  return this.save();
};

// ===== STATIC METHODS =====

// Get user transactions with pagination
transactionSchema.statics.getUserTransactions = async function(userId, options = {}) {
  const { page = 1, limit = 20, type, status, startDate, endDate, userType = 'creator' } = options;
  
  const query = { user: userId, userType };
  
  if (type) query.type = type;
  if (status) query.status = status;
  if (startDate) query.createdAt = { $gte: new Date(startDate) };
  if (endDate) query.createdAt = { ...query.createdAt, $lte: new Date(endDate) };
  
  const skip = (page - 1) * limit;
  
  const [transactions, total] = await Promise.all([
    this.find(query)
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit)
      .populate('user', 'name email')
      .populate('createdBy', 'name email'),
    this.countDocuments(query)
  ]);
  
  return {
    transactions,
    pagination: {
      page,
      limit,
      total,
      pages: Math.ceil(total / limit)
    }
  };
};

// Get user balance from transactions
transactionSchema.statics.getUserBalance = async function(userId, userType = 'creator') {
  const result = await this.aggregate([
    {
      $match: {
        user: new mongoose.Types.ObjectId(userId),
        userType,
        status: 'completed'
      }
    },
    {
      $group: {
        _id: null,
        totalBalance: { $sum: '$amount' },
        totalCredits: {
          $sum: {
            $cond: [{ $gt: ['$amount', 0] }, '$amount', 0]
          }
        },
        totalDebits: {
          $sum: {
            $cond: [{ $lt: ['$amount', 0] }, '$amount', 0]
          }
        },
        transactionCount: { $sum: 1 }
      }
    }
  ]);
  
  return result.length > 0 ? result[0] : {
    totalBalance: 0,
    totalCredits: 0,
    totalDebits: 0,
    transactionCount: 0
  };
};

// Get transaction summary by type
transactionSchema.statics.getTransactionSummary = async function(options = {}) {
  const { startDate, endDate, userType, status = 'completed' } = options;
  
  const matchStage = { status };
  
  if (userType) matchStage.userType = userType;
  if (startDate) matchStage.createdAt = { $gte: new Date(startDate) };
  if (endDate) matchStage.createdAt = { ...matchStage.createdAt, $lte: new Date(endDate) };
  
  const summary = await this.aggregate([
    { $match: matchStage },
    {
      $group: {
        _id: '$type',
        count: { $sum: 1 },
        totalAmount: { $sum: '$amount' },
        averageAmount: { $avg: '$amount' },
        minAmount: { $min: '$amount' },
        maxAmount: { $max: '$amount' }
      }
    },
    { $sort: { totalAmount: -1 } }
  ]);
  
  return summary;
};

// Get platform earnings breakdown
transactionSchema.statics.getPlatformEarnings = async function(options = {}) {
  const { startDate, endDate } = options;
  
  const matchStage = {
    userType: 'platform',
    status: 'completed',
    type: { $in: ['platform_fee', 'processing_fee', 'delivery_fee'] }
  };
  
  if (startDate) matchStage.createdAt = { $gte: new Date(startDate) };
  if (endDate) matchStage.createdAt = { ...matchStage.createdAt, $lte: new Date(endDate) };
  
  const earnings = await this.aggregate([
    { $match: matchStage },
    {
      $group: {
        _id: '$type',
        totalAmount: { $sum: '$amount' },
        count: { $sum: 1 },
        averageAmount: { $avg: '$amount' }
      }
    }
  ]);
  
  const summary = {
    platformFees: 0,
    processingFees: 0,
    deliveryFees: 0,
    totalEarnings: 0
  };
  
  earnings.forEach(item => {
    switch (item._id) {
      case 'platform_fee':
        summary.platformFees = item.totalAmount;
        break;
      case 'processing_fee':
        summary.processingFees = item.totalAmount;
        break;
      case 'delivery_fee':
        summary.deliveryFees = item.totalAmount;
        break;
    }
    summary.totalEarnings += item.totalAmount;
  });
  
  return summary;
};

// Get delivery fee statistics
transactionSchema.statics.getDeliveryStats = async function(options = {}) {
  const { startDate, endDate, groupBy = 'day' } = options;
  
  const query = {
    type: 'delivery_fee',
    status: 'completed'
  };
  
  if (startDate) query.createdAt = { $gte: new Date(startDate) };
  if (endDate) query.createdAt = { ...query.createdAt, $lte: new Date(endDate) };
  
  const groupByFormat = {
    day: { $dateToString: { format: '%Y-%m-%d', date: '$createdAt' } },
    week: { $dateToString: { format: '%Y-W%U', date: '$createdAt' } },
    month: { $dateToString: { format: '%Y-%m', date: '$createdAt' } }
  };
  
  return this.aggregate([
    { $match: query },
    {
      $group: {
        _id: groupByFormat[groupBy],
        totalFees: { $sum: '$amount' },
        orderCount: { $sum: 1 },
        averageFee: { $avg: '$amount' }
      }
    },
    { $sort: { _id: 1 } }
  ]);
};

// Create transaction with automatic wallet update
transactionSchema.statics.createWithWalletUpdate = async function(transactionData) {
  const session = await mongoose.startSession();
  
  try {
    return await session.withTransaction(async () => {
      // Create the transaction
      const transaction = await this.create([transactionData], { session });
      
      // Update wallet balance if user exists
      if (transactionData.user) {
        const Wallet = mongoose.model('Wallet');
        let wallet = await Wallet.findOne({
          user: transactionData.user,
          userType: transactionData.userType
        }).session(session);
        
        if (!wallet) {
          // Create wallet if it doesn't exist
          wallet = await Wallet.create([{
            user: transactionData.user,
            userType: transactionData.userType
          }], { session });
          wallet = wallet[0];
        }
        
        await wallet.updateBalance();
      }
      
      return transaction[0];
    });
  } finally {
    await session.endSession();
  }
};

// Bulk create transactions with wallet updates
transactionSchema.statics.createManyWithWalletUpdate = async function(transactionsData) {
  const session = await mongoose.startSession();
  
  try {
    return await session.withTransaction(async () => {
      // Create all transactions
      const transactions = await this.insertMany(transactionsData, { session });
      
      // Get unique users that need wallet updates
      const walletUpdates = new Map();
      
      transactionsData.forEach(txn => {
        if (txn.user) {
          const key = `${txn.user}_${txn.userType}`;
          walletUpdates.set(key, { user: txn.user, userType: txn.userType });
        }
      });
      
      // Update all affected wallets
      const Wallet = mongoose.model('Wallet');
      for (const walletInfo of walletUpdates.values()) {
        let wallet = await Wallet.findOne({
          user: walletInfo.user,
          userType: walletInfo.userType
        }).session(session);
        
        if (!wallet) {
          wallet = await Wallet.create([walletInfo], { session });
          wallet = wallet[0];
        }
        
        await wallet.updateBalance();
      }
      
      return transactions;
    });
  } finally {
    await session.endSession();
  }
};

// Helper function to process delivery fee refunds
transactionSchema.statics.processDeliveryRefund = async function(orderId, refundAmount, reason) {
  return this.createWithWalletUpdate({
    type: 'refund',
    amount: -refundAmount, // Negative because it's going out of platform
    user: null,
    userType: 'platform',
    description: `Delivery fee refund: ${reason}`,
    sourceType: 'order',
    sourceId: orderId,
    meta: {
      refundReason: reason,
      refundType: 'delivery_fee'
    }
  });
};

// Get transactions by source
transactionSchema.statics.getBySource = async function(sourceType, sourceId) {
  return this.find({ sourceType, sourceId })
    .sort({ createdAt: -1 })
    .populate('user', 'name email')
    .populate('createdBy', 'name email');
};

// Get financial dashboard data
transactionSchema.statics.getDashboardData = async function(options = {}) {
  const { startDate, endDate } = options;
  
  const matchStage = { status: 'completed' };
  
  if (startDate) matchStage.createdAt = { $gte: new Date(startDate) };
  if (endDate) matchStage.createdAt = { ...matchStage.createdAt, $lte: new Date(endDate) };
  
  const [summary, platformEarnings, recentTransactions] = await Promise.all([
    this.getTransactionSummary(options),
    this.getPlatformEarnings(options),
    this.find(matchStage)
      .sort({ createdAt: -1 })
      .limit(10)
      .populate('user', 'name email')
  ]);
  
  return {
    summary,
    platformEarnings,
    recentTransactions
  };
};

// ===== MIDDLEWARE =====

// Pre-save middleware
transactionSchema.pre('save', function(next) {
  // Set processedAt for completed transactions
  if (this.isModified('status') && this.status === 'completed' && !this.processedAt) {
    this.processedAt = Date.now();
  }
  
  // Validate user requirement for non-platform transactions
  if (this.userType !== 'platform' && !this.user) {
    return next(new Error('User is required for non-platform transactions'));
  }
  
  // Validate sourceId requirement for non-manual transactions
  if (this.sourceType !== 'manual' && !this.sourceId) {
    return next(new Error('Source ID is required for non-manual transactions'));
  }
  
  next();
});

// Post-save middleware for logging
transactionSchema.post('save', function(doc) {
  console.log(`Transaction created: ${doc.type} - ${doc.formattedAmount} for ${doc.userType}`);
});

const Transaction = mongoose.model('Transaction', transactionSchema);

module.exports = Transaction;
