const express = require('express');
const productController = require('../../controllers/admin/product.controller');
const authMiddleware = require('../../middleware/auth.middleware');

const router = express.Router();

// Protect all routes
router.use(authMiddleware.protect);
router.use(authMiddleware.restrictTo('admin'));

// Product routes
router.get('/', productController.getAllProducts);
router.get('/stats', productController.getProductStats);
router.get('/:id', productController.getProduct);
router.patch('/:id', productController.updateProduct);
router.delete('/:id', productController.deleteProduct);
router.patch('/:id/status', productController.updateProductStatus);
router.get('/:id/reviews', productController.getProductReviews);
router.get('/creators/:id/products', productController.getCreatorProducts);

module.exports = router;


