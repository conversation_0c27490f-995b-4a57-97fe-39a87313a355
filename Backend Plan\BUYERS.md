# Buyers Backend Architecture

This document outlines the buyer-specific functionality, models, and endpoints for the Flashy API.

## Models

## Endpoints

### Profile Management

| Endpoint | Method | Description | Request Body | Response |
|----------|--------|-------------|--------------|----------|
| `/api/v1/buyers/profile` | GET | Get buyer profile | - | Buyer data |
| `/api/v1/buyers/profile` | PATCH | Update buyer profile | `{ name, photo, etc. }` | Updated buyer data |
| `/api/v1/buyers/profile/addresses` | GET | Get buyer addresses | - | Addresses array |
| `/api/v1/buyers/profile/addresses` | POST | Add new address | Address object | Updated addresses |
| `/api/v1/buyers/profile/addresses/:id` | PATCH | Update address | Address fields | Updated address |
| `/api/v1/buyers/profile/addresses/:id` | DELETE | Delete address | - | Success message |
| `/api/v1/buyers/profile/preferences` | GET | Get buyer preferences | - | Preferences object |
| `/api/v1/buyers/profile/preferences` | PATCH | Update preferences | Preference fields | Updated preferences |
| `/api/v1/buyers/profile/preferences/notification` | GET | Get notification preferences | - | Notification preferences |
| `/api/v1/buyers/profile/preferences/notification` | PATCH | Update notification preferences | Notification preference fields | Updated preferences |

### Product Browsing Endpoints

> **Note:** The main products endpoint supports comprehensive filtering options that eliminate the need for separate endpoints for featured products, products with sales/promotions, or discounted products. Use the appropriate query parameters to filter products as needed. The specialized endpoints for new arrivals, trending products, and category-specific products are maintained for optimized performance.

| Endpoint | Method | Description | Query Parameters | Response |
|----------|--------|-------------|------------------|----------|
| `/api/v1/buyers/products` | GET | Get product feed | `page, limit, sort, filter, category, gender, color, size, priceMin, priceMax, brand, material, style, fit, featured=true/false, onSale=true/false, hasPromotion=true/false, hasDiscount=true/false, minDiscount` | Products array |
| `/api/v1/buyers/products/filter-params` | GET | Get filter parameters | - | Filter options |
| `/api/v1/buyers/products/new-arrivals` | GET | Get new arrivals | `page, limit, days` | New products |
| `/api/v1/buyers/products/by-categories` | GET | Get products by categories | `categories, page, limit` | Products by category |
| `/api/v1/buyers/products/category/:id` | GET | Get products in category | `page, limit, sort, filter` | Products in category |
| `/api/v1/buyers/products/search` | GET | Search products | `query, page, limit, sort, filter` | Search results |
| `/api/v1/buyers/products/:id` | GET | Get product details | `includeReviews, includeRelated` | Product details |
| `/api/v1/buyers/products/:id/variations` | GET | Get product variations | - | Variations array |
| `/api/v1/buyers/products/:id/reviews` | GET | Get product reviews | `page, limit, sort, rating` | Reviews array |
| `/api/v1/buyers/products/:id/related` | GET | Get related products | `limit` | Related products |
| `/api/v1/buyers/products/:id/creator` | GET | Get product's creator with their products | `limit, excludeCurrentProduct=true/false` | Creator details with products array |
| `/api/v1/buyers/products/recently-viewed` | GET | Get recently viewed products | `limit` | Products array |
| `/api/v1/buyers/products/recommended` | GET | Get recommended products | `limit, based_on` | Products array |
| `/api/v1/buyers/products/creator/:id` | GET | Get creator's products | `page, limit, sort, filter` | Products array |
| `/api/v1/buyers/products/followed-creators` | GET | Get products from followed creators | `page, limit` | Products array |


### Bale Browsing Endpoints

> **Note:** Similar to products, the main bales endpoint supports comprehensive filtering options. Use the appropriate query parameters to filter bales as needed.

| Endpoint | Method | Description | Query Parameters | Response |
|----------|--------|-------------|------------------|----------|
| `/api/v1/buyers/bales` | GET | Get bale feed | `page, limit, sort, filter, category, country, condition, priceMin, priceMax, featured=true/false, onSale=true/false, hasPromotion=true/false, hasDiscount=true/false, minDiscount` | Bales array |
| `/api/v1/buyers/bales/filter-params` | GET | Get filter parameters | - | Filter options |
| `/api/v1/buyers/bales/new-arrivals` | GET | Get new arrivals | `page, limit, days` | New bales |
| `/api/v1/buyers/bales/by-categories` | GET | Get bales by categories | `categories, page, limit` | Bales by category |
| `/api/v1/buyers/bales/category/:id` | GET | Get bales in category | `page, limit, sort, filter` | Bales in category |
| `/api/v1/buyers/bales/search` | GET | Search bales | `query, page, limit, sort, filter` | Search results |
| `/api/v1/buyers/bales/:id` | GET | Get bale details | `includeReviews, includeRelated` | Bale details |
| `/api/v1/buyers/bales/:id/variations` | GET | Get bale variations | - | Variations array |
| `/api/v1/buyers/bales/:id/reviews` | GET | Get bale reviews | `page, limit, sort, rating` | Reviews array |
| `/api/v1/buyers/bales/:id/related` | GET | Get related bales | `limit` | Related bales |
| `/api/v1/buyers/bales/:id/creator` | GET | Get bale's creator with their bales | `limit, excludeCurrentBale=true/false` | Creator details with bales array |
| `/api/v1/buyers/bales/recently-viewed` | GET | Get recently viewed bales | `limit` | Bales array |
| `/api/v1/buyers/bales/recommended` | GET | Get recommended bales | `limit, based_on` | Bales array |
| `/api/v1/buyers/bales/creator/:id` | GET | Get creator's bales | `page, limit, sort, filter` | Bales array |

### Review Management Endpoints

> **Note:** Buyers can only review products, bales, and creators they have purchased from. Reviews are verified based on order history and help other buyers make informed decisions.

| Endpoint | Method | Description | Query Parameters/Request Body | Response |
|----------|--------|-------------|--------------------------|----------|
| `/api/v1/buyers/reviews` | GET | Get all my reviews | `page, limit, sort=['newest','oldest','highest','lowest'], type=['product','bale','creator']` | Reviews array |
| `/api/v1/buyers/reviews/products/:id` | POST | Create product review | `{ rating, title, review, images }` | Created review |
| `/api/v1/buyers/reviews/bales/:id` | POST | Create bale review | `{ rating, title, review, images }` | Created review |
| `/api/v1/buyers/reviews/creators/:id` | GET | Get my review for a creator | - | Review details or null |
| `/api/v1/buyers/reviews/:id` | GET | Get review details | - | Review details |
| `/api/v1/buyers/reviews/:id` | PATCH | Update review | `{ title, review, rating, images }` | Updated review |
| `/api/v1/buyers/reviews/:id` | DELETE | Delete review | - | Success message |
| `/api/v1/buyers/reviews/stats` | GET | Get review statistics | - | Stats with counts by rating, avg rating |
| `/api/v1/buyers/reviews/recent` | GET | Get recent reviews | `limit, days=30` | Reviews array |

### Creator Shop

> **Note:** The Creator Shop provides a dedicated storefront for each creator, showcasing their products, bales, promotions, and information. Buyers can browse, filter, and purchase directly from a creator's shop.

| Endpoint | Method | Description | Query Parameters/Request Body | Response |
|----------|--------|-------------|--------------------------|----------|
| `/api/v1/buyers/creators` | GET | Get featured creators | `page, limit, sort=['popular','rating','newest'], categor search` | Creators array |
| `/api/v1/buyers/creators/:id` | GET | Get creator shop details | `includeStats=true/false` | Creator shop details |
| `/api/v1/buyers/creators/:id/products` | GET | Get creator products | `page, limit, sort=['newest','oldest','price_low','price_high','popular'], category, priceMin, priceMax, onSale=true/false, featured=true/false` | Products array |
| `/api/v1/buyers/creators/:id/bales` | GET | Get creator bales | `page, limit, sort=['newest','oldest','price_low','price_high','popular'], category, priceMin, priceMax, onSale=true/false` | Bales array |
| `/api/v1/buyers/creators/:id/reviews` | GET | Get creator reviews | `page, limit, sort=['newest','oldest','highest','lowest'], rating` | Reviews array |
| `/api/v1/buyers/creators/:id/stats` | GET | Get creator statistics | - | Stats with ratings, products count, etc. |
| `/api/v1/buyers/creators/:id/new-arrivals` | GET | Get creator new arrivals | `limit, days=30, type=['products','bales','all']` | New items |
| `/api/v1/buyers/creators/:id/bestsellers` | GET | Get creator bestsellers | `limit, period=['30d','90d','all']` | Bestselling items |

### Creator Following

> **Note:** Buyers can follow creators to receive notifications about new products, promotions, and updates. Following a creator also affects the buyer's personalized product feed.

| Endpoint | Method | Description | Query Parameters/Request Body | Response |
|----------|--------|-------------|--------------------------|----------|
| `/api/v1/buyers/follow` | GET | Get followed creators | `page, limit, sort=['newest','oldest','name_asc','name_desc']` | Creators array with basic info |
| `/api/v1/buyers/follow/detailed` | GET | Get detailed followed creators | `page, limit, includeRecentProducts=true/false, productLimit` | Creators with recent products |
| `/api/v1/buyers/follow/:creatorId` | POST | Follow creator | `{ notificationPreferences }` | Updated follow status |
| `/api/v1/buyers/follow/:creatorId` | DELETE | Unfollow creator | - | Success message |
| `/api/v1/buyers/follow/:creatorId/check` | GET | Check if following creator | - | Follow status with details |
| `/api/v1/buyers/follow/suggestions` | GET | Get creator suggestions | `limit, based_on=['purchases','views','popular']` | Suggested creators |
| `/api/v1/buyers/follow/count` | GET | Get follow counts | - | Counts of followers and following |

### Wishlist Management Endpoints

> **Note:** The wishlist allows buyers to save products and bales they're interested in for later purchase. Items in the wishlist maintain their variation details (color, size, etc.) and can be easily moved to the cart.

| Endpoint | Method | Description | Query Parameters/Request Body | Response |
|----------|--------|-------------|--------------------------|----------|
| `/api/v1/buyers/wishlist` | GET | Get wishlist | `groupByCreator=true/false` | Wishlist with items, grouped by creator if requested |
| `/api/v1/buyers/wishlist` | DELETE | Clear wishlist | - | Empty wishlist |
| `/api/v1/buyers/wishlist/count` | GET | Get wishlist item count | - | Count of items in wishlist |
| `/api/v1/buyers/wishlist/count/by-creator` | GET | Get count by creator | - | Counts grouped by creator |
| `/api/v1/buyers/wishlist/products` | PATCH | Add product variations to wishlist | `{ productId, variations: [{ variationId, quantity }] }` | Updated wishlist |
| `/api/v1/buyers/wishlist/products/:id` | DELETE | Remove product variation from wishlist | `variationId` (query param) | Updated wishlist |
| `/api/v1/buyers/wishlist/products/:id/variations` | DELETE | Remove multiple product variations | `{ variationIds: [String] }` | Updated wishlist |
| `/api/v1/buyers/wishlist/products/:id` | PATCH | Update product variation quantity | `{ variationId, quantity }` | Updated wishlist |
| `/api/v1/buyers/wishlist/bales` | PATCH | Add bale variations to wishlist | `{ baleId, variations: [{ variationId, quantity }] }` | Updated wishlist |
| `/api/v1/buyers/wishlist/bales/:id` | DELETE | Remove bale variation from wishlist | `variationId` (query param) | Updated wishlist |
| `/api/v1/buyers/wishlist/bales/:id/variations` | DELETE | Remove multiple bale variations | `{ variationIds: [String] }` | Updated wishlist |
| `/api/v1/buyers/wishlist/bales/:id` | PATCH | Update bale variation quantity | `{ variationId, quantity }` | Updated wishlist |
| `/api/v1/buyers/wishlist/move-to-cart` | POST | Move single item to cart | `{ type: ['product','bale'], id, variationId }` | Updated cart and wishlist |
| `/api/v1/buyers/wishlist/move-all-to-cart` | POST | Move all items to cart | - | Updated cart and wishlist |
| `/api/v1/buyers/wishlist/check` | GET | Check if item is in wishlist | `type=['product','bale'], id, variationId` | Status with details |

### Cart Management

> **Note:** The cart system allows buyers to add product and bale variations for purchase. Items are grouped by creator for shipping calculations, and the cart automatically calculates subtotals and totals based on current prices (including any active discounts).

| Endpoint | Method | Description | Query Parameters/Request Body | Response |
|----------|--------|-------------|--------------------------|----------|
| `/api/v1/buyers/cart` | GET | Get cart | - | Cart with items, subtotal, total, and counts |
| `/api/v1/buyers/cart` | DELETE | Clear cart | - | Empty cart |
| `/api/v1/buyers/cart/products` | POST | Add product variations to cart | `{ productId, variations: [{ variationId, quantity }] }` | Updated cart |
| `/api/v1/buyers/cart/products/:productId/:variationId` | PATCH | Update product quantity | `{ quantity }` | Updated cart |
| `/api/v1/buyers/cart/products/:productId/:variationId` | DELETE | Remove product from cart | - | Updated cart |
| `/api/v1/buyers/cart/bales` | POST | Add bale variations to cart | `{ baleId, variations: [{ variationId, quantity }] }` | Updated cart |
| `/api/v1/buyers/cart/bales/:baleId/:variationId` | PATCH | Update bale quantity | `{ quantity }` | Updated cart |
| `/api/v1/buyers/cart/bales/:baleId/:variationId` | DELETE | Remove bale from cart | - | Updated cart |
| `/api/v1/buyers/cart/items/:id` | PATCH | Update any cart item | `{ quantity }` | Updated cart |
| `/api/v1/buyers/cart/count` | GET | Get cart item count | - | Count of items in cart |


### Checkout Process

> **Note:** The checkout process allows buyers to convert their cart into an order. The system validates item availability, calculates shipping costs based on location, and processes payment through Paystack. Orders receive a unique order number (format: EF-YYYYMMDD-TIMESTAMP) and go through a defined status flow.



| Endpoint | Method | Description | Query Parameters/Request Body | Response |
|----------|--------|-------------|--------------------------|----------|
| `/api/v1/buyers/checkout` | GET | Get checkout summary | - | Checkout data with cart, shipping options, and totals |
| `/api/v1/buyers/checkout/shipping` | GET | Get shipping options | `addressId` | Available shipping methods and costs |
| `/api/v1/buyers/checkout/validate` | POST | Validate cart items | `{ items: [{ id, type, variationId, quantity }] }` | Validation results with stock status |
| `/api/v1/buyers/checkout/order` | POST | Create order | `{ shippingAddress, shippingMethod, paymentMethod, notes, couponCode }` | Created order with payment instructions |
| `/api/v1/buyers/checkout/payment` | POST | Process payment | `{ orderId, paymentMethod, channel, paymentDetails }` | Payment result with order status |
| `/api/v1/buyers/checkout/verify-payment` | POST | Verify payment (webhook) | `{ reference, status, gatewayResponse, meta }` | Updated order with payment status |
| `/api/v1/buyers/checkout/payment-methods` | GET | Get available payment methods | - | Payment methods with details |
| `/api/v1/buyers/checkout/apply-coupon` | POST | Apply coupon to cart | `{ code }` | Updated cart with applied coupon |
| `/api/v1/buyers/checkout/remove-coupon` | DELETE | Remove coupon from cart | - | Updated cart without coupon |
| `/api/v1/buyers/checkout/estimate-shipping` | POST | Estimate shipping cost | `{ address, items }` | Estimated shipping cost |


### Order Management


| Endpoint | Method | Description | Query Parameters/Request Body | Response |
|----------|--------|-------------|--------------------------|----------|
| `/api/v1/buyers/orders` | GET | Get buyer orders | `page, limit, status=['pending','processing','shipped','delivered','cancelled','refunded'], sort=['newest','oldest'], startDate, endDate` | Orders array with pagination |
| `/api/v1/buyers/orders/:id` | GET | Get order details | `includeStatusHistory=true/false` | Order details with items, shipping, payment info |
| `/api/v1/buyers/orders/:id/track` | GET | Track order shipment | - | Tracking information with carrier details |
| `/api/v1/buyers/orders/:id/receipt` | GET | Get order receipt | `format=['pdf','html']` | Receipt data with payment details |
| `/api/v1/buyers/orders/:id/return-request` | POST | Request item return | `{ items: [{ id, type, variationId, reason }], images, additionalInfo }` | Return request details |
| `/api/v1/buyers/orders/recent` | GET | Get recent orders | `limit, status` | Recent orders array |
| `/api/v1/buyers/orders/stats` | GET | Get order statistics | `period=['7d','30d','90d','all']` | Order counts by status, spending stats |
| `/api/v1/buyers/orders/pending-review` | GET | Get orders pending review | `page, limit` | Orders with delivered items not yet reviewed |


### Payment and Wallet Management

> **Note:** Buyers can manage their payment methods, view transaction history, and access their wallet for refunds and store credits. The wallet can be used for future purchases and receives funds from returns and promotional credits.

| Endpoint | Method | Description | Query Parameters/Request Body | Response |
|----------|--------|-------------|--------------------------|----------|
| `/api/v1/buyers/payments/transactions/:id` | GET | Get transaction details | - | Transaction details |
| `/api/v1/buyers/wallet` | GET | Get wallet balance | - | Wallet data with available balance |
| `/api/v1/buyers/wallet/transactions` | GET | Get wallet transactions | `page, limit, type=['refund','credit','debit','adjustment'], status, startDate, endDate` | Transactions array |
| `/api/v1/buyers/wallet/apply` | POST | Apply wallet balance to order | `{ orderId, amount }` | Updated order with applied wallet balance |


### Notification Management

| Endpoint | Method | Description | Query Parameters/Request Body | Response |
|----------|--------|-------------|--------------------------|----------|
| `/api/v1/buyers/notifications` | GET | Get notifications | `page, limit, read, type, priority, populate` | Notifications array |
| `/api/v1/buyers/notifications/unread-count` | GET | Get unread count | - | Unread count |
| `/api/v1/buyers/notifications/:id/read` | PATCH | Mark as read | - | Updated notification |
| `/api/v1/buyers/notifications/mark-all-read` | PATCH | Mark all as read | - | Count of updated notifications |
| `/api/v1/buyers/notifications/:id` | DELETE | Delete notification | - | Success message |
| `/api/v1/buyers/notifications/preferences` | GET | Get notification preferences | - | Preferences object |
| `/api/v1/buyers/notifications/preferences` | PATCH | Update preferences | `{ channels: { email, push }, types: { orderUpdates, promotions, priceDrops, newArrivals } }` | Updated preferences |

