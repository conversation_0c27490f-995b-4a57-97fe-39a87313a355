const Payout = require('../../models/payout.model');
const Order = require('../../models/order.model');
const { Creator } = require('../../models/user.model');
const AppError = require('../../utils/appError');
const catchAsync = require('../../utils/catchAsync');

/**
 * Get all payouts for the logged-in creator
 * @route GET /api/v1/creators/payouts
 * @access Private (Creator only)
 */
exports.getMyPayouts = catchAsync(async (req, res, next) => {
  const payouts = await Payout.find({ recipient: req.user.id, recipientType: 'creator' })
    .sort('-createdAt');

  res.status(200).json({
    status: 'success',
    results: payouts.length,
    data: {
      payouts
    }
  });
});

/**
 * Get payout by ID for the logged-in creator
 * @route GET /api/v1/creators/payouts/:id
 * @access Private (Creator only)
 */
exports.getMyPayout = catchAsync(async (req, res, next) => {
  const payout = await Payout.findOne({
    _id: req.params.id,
    recipient: req.user.id,
    recipientType: 'creator'
  });

  if (!payout) {
    return next(new AppError('No payout found with that ID', 404));
  }

  res.status(200).json({
    status: 'success',
    data: {
      payout
    }
  });
});

/**
 * Get payout statistics for the logged-in creator
 * @route GET /api/v1/creators/payouts/stats
 * @access Private (Creator only)
 */
exports.getMyPayoutStats = catchAsync(async (req, res, next) => {
  const stats = await Payout.aggregate([
    {
      $match: { recipient: req.user._id, recipientType: 'creator' }
    },
    {
      $group: {
        _id: '$status',
        count: { $sum: 1 },
        totalAmount: { $sum: '$amount' }
      }
    },
    {
      $sort: { _id: 1 }
    }
  ]);

  // Get total earnings
  const totalEarnings = await Order.aggregate([
    {
      $unwind: '$items'
    },
    {
      $match: { 'items.creator': req.user._id, status: 'delivered' }
    },
    {
      $group: {
        _id: null,
        total: { $sum: { $multiply: ['$items.price', '$items.quantity'] } }
      }
    }
  ]);

  // Get total paid out
  const totalPaidOut = await Payout.aggregate([
    {
      $match: { recipient: req.user._id, recipientType: 'creator', status: 'completed' }
    },
    {
      $group: {
        _id: null,
        total: { $sum: '$amount' }
      }
    }
  ]);

  // Get pending amount
  const pendingAmount = await Payout.aggregate([
    {
      $match: { recipient: req.user._id, recipientType: 'creator', status: { $in: ['pending', 'processing'] } }
    },
    {
      $group: {
        _id: null,
        total: { $sum: '$amount' }
      }
    }
  ]);

  res.status(200).json({
    status: 'success',
    data: {
      stats,
      summary: {
        totalEarnings: totalEarnings.length > 0 ? totalEarnings[0].total : 0,
        totalPaidOut: totalPaidOut.length > 0 ? totalPaidOut[0].total : 0,
        pendingAmount: pendingAmount.length > 0 ? pendingAmount[0].total : 0,
        availableBalance: (totalEarnings.length > 0 ? totalEarnings[0].total : 0) -
                          (totalPaidOut.length > 0 ? totalPaidOut[0].total : 0) -
                          (pendingAmount.length > 0 ? pendingAmount[0].total : 0)
      }
    }
  });
});

/**
 * Request a payout
 * @route POST /api/v1/creators/payouts/request
 * @access Private (Creator only)
 * @param {string} [payoutType='on_demand'] - Type of payout ('weekly' or 'on_demand')
 * @param {number} amount - Amount to be paid out
 * @param {string} [description] - Optional description for the payout
 * @param {Date} [periodStart] - Start date of the period for which the payout is requested
 * @param {Date} [periodEnd] - End date of the period for which the payout is requested
 */
exports.requestPayout = catchAsync(async (req, res, next) => {
  // Get creator
  const creator = await Creator.findById(req.user.id);

  if (!creator) {
    return next(new AppError('Creator not found', 404));
  }

  // Check if creator is verified
  if (creator.verificationStatus !== 'verified') {
    return next(new AppError('You must be verified to request a payout', 403));
  }

  // Check if creator has completed onboarding
  if (creator.onboardingStatus !== 'completed') {
    return next(new AppError('You must complete onboarding to request a payout', 403));
  }

  // Check if creator has payment information
  if (!creator.paymentInfo || !creator.paymentInfo.paymentOption) {
    return next(new AppError('You must set up payment information to request a payout', 403));
  }

  // Calculate available balance
  const totalEarnings = await Order.aggregate([
    {
      $unwind: '$items'
    },
    {
      $match: { 'items.creator': req.user._id, status: 'delivered' }
    },
    {
      $group: {
        _id: null,
        total: { $sum: { $multiply: ['$items.price', '$items.quantity'] } }
      }
    }
  ]);

  const totalPaidOut = await Payout.aggregate([
    {
      $match: { recipient: req.user._id, recipientType: 'creator', status: { $in: ['completed', 'pending', 'processing'] } }
    },
    {
      $group: {
        _id: null,
        total: { $sum: '$amount' }
      }
    }
  ]);

  const availableBalance = (totalEarnings.length > 0 ? totalEarnings[0].total : 0) -
                          (totalPaidOut.length > 0 ? totalPaidOut[0].total : 0);

  // Check if amount is provided
  if (!req.body.amount) {
    return next(new AppError('Please provide an amount', 400));
  }

  // Check if amount is valid
  const amount = parseFloat(req.body.amount);
  if (isNaN(amount) || amount <= 0) {
    return next(new AppError('Please provide a valid amount', 400));
  }

  // Check if amount is less than or equal to available balance
  if (amount > availableBalance) {
    return next(new AppError(`Requested amount exceeds available balance of ${availableBalance}`, 400));
  }

  // Check if amount is greater than or equal to minimum threshold
  const minimumThreshold = 100; // Default minimum threshold
  if (amount < minimumThreshold) {
    return next(new AppError(`Requested amount must be at least ${minimumThreshold}`, 400));
  }

  // Create payout request
  const payout = await Payout.create({
    recipient: req.user.id,
    recipientType: 'creator',
    payoutType: req.body.payoutType || 'on_demand', // Default to on-demand if not specified
    amount,
    currency: 'GHS', // Default currency
    status: 'pending',
    method: creator.paymentInfo.paymentOption,
    description: req.body.description || 'Payout request',
    reference: `PO-${Date.now()}-${Math.floor(Math.random() * 1000)}`,
    periodStart: req.body.periodStart ? new Date(req.body.periodStart) : null,
    periodEnd: req.body.periodEnd ? new Date(req.body.periodEnd) : null
  });

  // Update payout details based on payment method
  if (creator.paymentInfo.paymentOption === 'bank') {
    payout.bankDetails = creator.paymentInfo.bankDetails;
  } else if (creator.paymentInfo.paymentOption === 'mobile_money') {
    payout.mobileMoneyDetails = creator.paymentInfo.mobileMoneyDetails;
  }

  await payout.save();

  res.status(201).json({
    status: 'success',
    data: {
      payout
    }
  });
});

/**
 * Cancel a pending payout request
 * @route PATCH /api/v1/creators/payouts/:id/cancel
 * @access Private (Creator only)
 */
exports.cancelPayoutRequest = catchAsync(async (req, res, next) => {
  // Find payout
  const payout = await Payout.findOne({
    _id: req.params.id,
    recipient: req.user.id,
    recipientType: 'creator',
    status: 'pending'
  });

  if (!payout) {
    return next(new AppError('No pending payout found with that ID', 404));
  }

  // Update payout status
  payout.status = 'cancelled';
  await payout.save();

  res.status(200).json({
    status: 'success',
    data: {
      payout
    }
  });
});
