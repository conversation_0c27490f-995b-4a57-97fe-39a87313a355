const express = require('express');
const followController = require('../../controllers/buyers/follow.controller');
const authMiddleware = require('../../middleware/auth.middleware');

const router = express.Router();

// Protect all routes
router.use(authMiddleware.protect);
router.use(authMiddleware.restrictTo('buyer'));

// Follow routes
router.get('/', followController.getFollowedCreators);
router.post('/:creatorId', followController.followCreator);
router.delete('/:creatorId', followController.unfollowCreator);
router.get('/:creatorId/check', followController.checkFollowing);

module.exports = router;
