{"info": {"_postman_id": "creator-product-test-scripts-2024", "name": "Creator Product Management - With Test Scripts", "description": "Complete test collection with automated test scripts and validations", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{creator_token}}", "type": "string"}]}, "event": [{"listen": "prerequest", "script": {"exec": ["// Global pre-request script", "if (!pm.environment.get('creator_token')) {", "    console.log('No creator token found. Please login first.');", "}"], "type": "text/javascript"}}, {"listen": "test", "script": {"exec": ["// Global test script", "pm.test('Response time is less than 2000ms', function () {", "    pm.expect(pm.response.responseTime).to.be.below(2000);", "});", "", "pm.test('Response has correct content type', function () {", "    pm.expect(pm.response.headers.get('Content-Type')).to.include('application/json');", "});"], "type": "text/javascript"}}], "variable": [{"key": "base_url", "value": "http://localhost:3000/api/v1", "type": "string"}], "item": [{"name": "Authentication", "item": [{"name": "C<PERSON> Login", "event": [{"listen": "test", "script": {"exec": ["pm.test('Login successful', function () {", "    pm.response.to.have.status(200);", "    const response = pm.response.json();", "    pm.expect(response.status).to.eql('success');", "    pm.expect(response.token).to.exist;", "    ", "    // Store token for subsequent requests", "    pm.environment.set('creator_token', response.token);", "    pm.environment.set('creator_id', response.data.user._id);", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"{{creator_email}}\",\n  \"password\": \"{{creator_password}}\"\n}"}, "url": {"raw": "{{base_url}}/auth/login", "host": ["{{base_url}}"], "path": ["auth", "login"]}}, "response": []}]}, {"name": "Product Setup", "item": [{"name": "Create Test Product", "event": [{"listen": "test", "script": {"exec": ["pm.test('Product created successfully', function () {", "    pm.response.to.have.status(201);", "    const response = pm.response.json();", "    pm.expect(response.status).to.eql('success');", "    pm.expect(response.data.product._id).to.exist;", "    ", "    // Store product ID for subsequent tests", "    pm.environment.set('product_id', response.data.product._id);", "    ", "    // Store variation ID if variations exist", "    if (response.data.product.variations && response.data.product.variations.length > 0) {", "        pm.environment.set('variation_id', response.data.product.variations[0]._id);", "    }", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Test Product for API Testing\",\n  \"brand\": \"Test Brand\",\n  \"description\": \"This is a test product for API testing\",\n  \"highlights\": [\"Test feature 1\", \"Test feature 2\"],\n  \"gender\": \"Unisex\",\n  \"basePrice\": 99.99,\n  \"category\": \"{{category_id}}\",\n  \"tags\": [\"test\", \"api\"],\n  \"variations\": [\n    {\n      \"color\": \"Red\",\n      \"size\": \"Medium\",\n      \"quantity\": 100,\n      \"price\": 99.99,\n      \"sku\": \"TEST-RED-M-001\"\n    }\n  ]\n}"}, "url": {"raw": "{{base_url}}/creators/products", "host": ["{{base_url}}"], "path": ["creators", "products"]}}, "response": []}]}, {"name": "Basic Info Tests", "item": [{"name": "Update Basic Info - Success", "event": [{"listen": "test", "script": {"exec": ["pm.test('Basic info updated successfully', function () {", "    pm.response.to.have.status(200);", "    const response = pm.response.json();", "    pm.expect(response.status).to.eql('success');", "    pm.expect(response.data.product.name).to.eql('Updated Test Product');", "    pm.expect(response.data.product.basePrice).to.eql(89.99);", "});"], "type": "text/javascript"}}], "request": {"method": "PATCH", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "name", "value": "Updated Test Product", "type": "text"}, {"key": "basePrice", "value": "89.99", "type": "text"}, {"key": "highlights", "value": "[\"Updated feature 1\", \"Updated feature 2\", \"New feature 3\"]", "type": "text"}]}, "url": {"raw": "{{base_url}}/creators/products/{{product_id}}/basic-info", "host": ["{{base_url}}"], "path": ["creators", "products", "{{product_id}}", "basic-info"]}}, "response": []}]}, {"name": "Variation Tests", "item": [{"name": "Get All Variations", "event": [{"listen": "test", "script": {"exec": ["pm.test('Variations retrieved successfully', function () {", "    pm.response.to.have.status(200);", "    const response = pm.response.json();", "    pm.expect(response.status).to.eql('success');", "    pm.expect(response.data.variations).to.be.an('array');", "    pm.expect(response.results).to.be.a('number');", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/creators/products/{{product_id}}/variations", "host": ["{{base_url}}"], "path": ["creators", "products", "{{product_id}}", "variations"]}}, "response": []}, {"name": "Add New Variation", "event": [{"listen": "test", "script": {"exec": ["pm.test('Variation added successfully', function () {", "    pm.response.to.have.status(201);", "    const response = pm.response.json();", "    pm.expect(response.status).to.eql('success');", "    pm.expect(response.data.product.variations).to.be.an('array');", "    ", "    // Find the new variation", "    const newVariation = response.data.product.variations.find(v => v.color === 'Blue' && v.size === 'Large');", "    pm.expect(newVariation).to.exist;", "    pm.environment.set('new_variation_id', newVariation._id);", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "color", "value": "Blue", "type": "text"}, {"key": "size", "value": "Large", "type": "text"}, {"key": "quantity", "value": "75", "type": "text"}, {"key": "price", "value": "94.99", "type": "text"}, {"key": "sku", "value": "TEST-BLUE-L-001", "type": "text"}]}, "url": {"raw": "{{base_url}}/creators/products/{{product_id}}/variations", "host": ["{{base_url}}"], "path": ["creators", "products", "{{product_id}}", "variations"]}}, "response": []}]}]}