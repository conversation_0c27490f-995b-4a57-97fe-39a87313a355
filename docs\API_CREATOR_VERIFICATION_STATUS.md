# Creator Verification Status API Documentation

## Overview
The verification status endpoint allows creators to check their account verification status, including submitted documents, review progress, and next steps.

## Endpoint Details

### Get Verification Status
```
GET /api/v1/creators/onboarding/verification-status
```

**Authentication:** Required (Creator only)  
**Content-Type:** `application/json`

## Response Structure

### Success Response

**Status Code:** `200 OK`

```json
{
  "status": "success",
  "data": {
    "verificationStatus": "pending",
    "verificationDetails": {
      "submittedAt": "2024-01-15T10:30:00.000Z",
      "reviewedAt": null,
      "reviewedBy": null,
      "feedback": null,
      "documents": [
        "https://cloudinary.com/business_license.pdf",
        "https://cloudinary.com/owner_id.jpg"
      ]
    },
    "documentsSubmitted": [
      "https://cloudinary.com/business_license.pdf",
      "https://cloudinary.com/owner_id.jpg"
    ],
    "onboardingStatus": "pending",
    "onboardingProgress": {
      "businessInfo": true,
      "paymentInfo": true,
      "shopInfo": false,
      "shippingInfo": false
    },
    "creatorInfo": {
      "name": "<PERSON>e",
      "email": "<EMAIL>"
    },
    "statusMessage": "Your verification documents are under review.",
    "nextSteps": [
      "Wait for admin review (typically 2-3 business days)",
      "Check your email for updates",
      "Ensure your contact information is up to date"
    ]
  }
}
```

## Verification Status Values

### Status Types

| Status | Description | Typical Duration |
|--------|-------------|------------------|
| `unverified` | No documents submitted yet | - |
| `pending` | Documents under review | 2-3 business days |
| `verified` | Verification successful | - |
| `rejected` | Verification failed | - |

### Status Details

#### 1. Unverified
```json
{
  "verificationStatus": "unverified",
  "statusMessage": "No verification documents have been submitted yet.",
  "nextSteps": [
    "Complete your business information",
    "Upload required verification documents",
    "Submit for review"
  ]
}
```

#### 2. Pending Review
```json
{
  "verificationStatus": "pending",
  "statusMessage": "Your verification documents are under review.",
  "nextSteps": [
    "Wait for admin review (typically 2-3 business days)",
    "Check your email for updates",
    "Ensure your contact information is up to date"
  ]
}
```

#### 3. Verified
```json
{
  "verificationStatus": "verified",
  "statusMessage": "Your account has been verified successfully!",
  "nextSteps": [
    "Complete remaining onboarding steps",
    "Start creating and selling products",
    "Set up your shop information"
  ]
}
```

#### 4. Rejected
```json
{
  "verificationStatus": "rejected",
  "statusMessage": "Your verification was rejected. Please review the feedback and resubmit.",
  "nextSteps": [
    "Review admin feedback",
    "Update or replace rejected documents",
    "Resubmit for verification"
  ],
  "verificationDetails": {
    "feedback": "Business license document is unclear. Please provide a clearer image.",
    "reviewedAt": "2024-01-16T14:30:00.000Z",
    "reviewedBy": "admin_user_id"
  }
}
```



## Error Responses

### Authentication Errors

**Status Code:** `401 Unauthorized`

```json
{
  "status": "fail",
  "message": "You are not logged in! Please log in to get access."
}
```

### Authorization Errors

**Status Code:** `403 Forbidden`

```json
{
  "status": "fail",
  "message": "You do not have permission to perform this action"
}
```

### Not Found Errors

**Status Code:** `404 Not Found`

```json
{
  "status": "fail",
  "message": "Creator not found"
}
```

## Frontend Integration Examples

### JavaScript/Fetch API

```javascript
const getVerificationStatus = async () => {
  try {
    const response = await fetch('/api/v1/creators/onboarding/verification-status', {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });

    const data = await response.json();
    
    if (data.status === 'success') {
      const { verificationStatus, statusMessage, nextSteps } = data.data;
      
      // Update UI based on status
      updateVerificationUI(verificationStatus, statusMessage, nextSteps);
      
      return data.data;
    } else {
      console.error('Error:', data.message);
    }
  } catch (error) {
    console.error('Network error:', error);
  }
};

// Usage
getVerificationStatus().then(verificationData => {
  console.log('Verification Status:', verificationData.verificationStatus);
  console.log('Message:', verificationData.statusMessage);
  console.log('Next Steps:', verificationData.nextSteps);
});
```

### React Hook Example

```jsx
import { useState, useEffect } from 'react';

const useVerificationStatus = () => {
  const [verificationData, setVerificationData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  const fetchVerificationStatus = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/v1/creators/onboarding/verification-status', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json'
        }
      });

      const data = await response.json();
      
      if (data.status === 'success') {
        setVerificationData(data.data);
        setError(null);
      } else {
        setError(data.message);
      }
    } catch (err) {
      setError('Failed to fetch verification status');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchVerificationStatus();
  }, []);

  return { verificationData, loading, error, refetch: fetchVerificationStatus };
};

// Component usage
const VerificationStatusComponent = () => {
  const { verificationData, loading, error } = useVerificationStatus();

  if (loading) return <div>Loading verification status...</div>;
  if (error) return <div>Error: {error}</div>;

  const getStatusColor = (status) => {
    switch (status) {
      case 'approved': return 'green';
      case 'rejected': return 'red';
      case 'pending': return 'orange';
      case 'requires_update': return 'yellow';
      default: return 'gray';
    }
  };

  return (
    <div className="verification-status">
      <h3>Verification Status</h3>
      <div className={`status-badge ${getStatusColor(verificationData.verificationStatus)}`}>
        {verificationData.verificationStatus.replace('_', ' ').toUpperCase()}
      </div>
      
      <p>{verificationData.statusMessage}</p>
      
      <div className="next-steps">
        <h4>Next Steps:</h4>
        <ul>
          {verificationData.nextSteps.map((step, index) => (
            <li key={index}>{step}</li>
          ))}
        </ul>
      </div>
      
      {verificationData.documentsSubmitted.length > 0 && (
        <div className="submitted-documents">
          <h4>Submitted Documents:</h4>
          <ul>
            {verificationData.documentsSubmitted.map((doc, index) => (
              <li key={index}>
                <a href={doc} target="_blank" rel="noopener noreferrer">
                  Document {index + 1}
                </a>
              </li>
            ))}
          </ul>
        </div>
      )}
    </div>
  );
};
```

### Status-Based UI Logic

```javascript
const handleVerificationStatus = (verificationData) => {
  const { verificationStatus, nextSteps, verificationDetails } = verificationData;

  switch (verificationStatus) {
    case 'not_submitted':
      // Show upload documents UI
      showDocumentUploadForm();
      break;
      
    case 'pending':
      // Show waiting message with progress indicator
      showPendingReviewUI();
      break;
      
    case 'approved':
      // Show success message and enable next onboarding steps
      showSuccessMessage();
      enableOnboardingSteps();
      break;
      
    case 'rejected':
      // Show rejection feedback and resubmission form
      showRejectionFeedback(verificationDetails.feedback);
      showResubmissionForm();
      break;
      
    case 'requires_update':
      // Show update requirements and edit form
      showUpdateRequirements(verificationDetails.feedback);
      showEditForm();
      break;
      
    default:
      // Show error state
      showErrorState();
  }
};
```

## Use Cases

### 1. Dashboard Status Widget
Display current verification status on creator dashboard with appropriate actions.

### 2. Onboarding Progress Tracker
Show verification as part of the overall onboarding progress.

### 3. Document Management
Allow creators to see which documents were submitted and their status.

### 4. Notification System
Trigger notifications based on status changes.

### 5. Support Integration
Provide context for customer support interactions.

## Notes

1. **Real-time Updates**: Consider implementing WebSocket or polling for real-time status updates
2. **Document Links**: Submitted document URLs are included for reference
3. **Admin Feedback**: Rejection and update requests include admin feedback
4. **Onboarding Integration**: Status affects overall onboarding progress
5. **Security**: Only the creator can access their own verification status
