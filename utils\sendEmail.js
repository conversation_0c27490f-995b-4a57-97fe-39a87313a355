const nodemailer = require('nodemailer');

// Utility function to send email
const sendEmail = async (options) => {
  // 1) Create a transporter object using the default SMTP transport
  const transporter = nodemailer.createTransport({
    service: 'gmail', // Use your email service (e.g., Gmail, SendGrid, etc.)
    auth: {
      user: process.env.EMAIL_USER,  // Your email (from .env file)
      pass: process.env.EMAIL_PASS,  // Your email password or app-specific password
    },
  });

  // 2) Set email options
  const mailOptions = {
    from: `EveryFash <${process.env.EMAIL_USER}>`, // Sender address
    to: options.email,                           // Recipient address
    subject: options.subject,                    // Subject line
    text: options.message,                       // Plain text body
    html: options.html || options.message,       // HTML body (optional)
  };

  // 3) Send email
  try {
    await transporter.sendMail(mailOptions);
  } catch (err) {
    console.error('Error sending email:', err);
    throw new Error('Email could not be sent');
  }
};

module.exports = sendEmail;
