const mongoose = require('mongoose');
const validator = require('validator');

// Support Ticket Schema
const ticketSchema = new mongoose.Schema(
  {
    ticketNumber: {
      type: String,
      unique: true
    },
    user: {
      type: mongoose.Schema.ObjectId,
      ref: 'User',
      required: [true, 'Ticket must belong to a user']
    },
    subject: {
      type: String,
      required: [true, 'Ticket must have a subject'],
      trim: true,
      maxlength: [100, 'Subject cannot be longer than 100 characters']
    },
    description: {
      type: String,
      required: [true, 'Ticket must have a description'],
      trim: true
    },
    category: {
      type: String,
      enum: [
        'order_issue', 
        'payment_issue', 
        'product_inquiry', 
        'return_refund', 
        'shipping_delivery', 
        'account_issue', 
        'technical_issue',
        'creator_support',
        'other'
      ],
      required: [true, 'Ticket must have a category']
    },
    priority: {
      type: String,
      enum: ['low', 'medium', 'high', 'urgent'],
      default: 'medium'
    },
    status: {
      type: String,
      enum: ['open', 'in_progress', 'waiting_on_customer', 'waiting_on_third_party', 'resolved', 'closed'],
      default: 'open'
    },
    assignedTo: {
      type: mongoose.Schema.ObjectId,
      ref: 'User' // Admin user
    },
    relatedOrder: {
      type: mongoose.Schema.ObjectId,
      ref: 'Order'
    },
    relatedProduct: {
      type: mongoose.Schema.ObjectId,
      ref: 'Product'
    },
    relatedBale: {
      type: mongoose.Schema.ObjectId,
      ref: 'Bale'
    },
    attachments: [String],
    messages: [
      {
        sender: {
          type: mongoose.Schema.ObjectId,
          ref: 'User',
          required: true
        },
        senderType: {
          type: String,
          enum: ['user', 'admin', 'system'],
          required: true
        },
        message: {
          type: String,
          required: true,
          trim: true
        },
        attachments: [String],
        createdAt: {
          type: Date,
          default: Date.now
        },
        isInternal: {
          type: Boolean,
          default: false // For admin-only notes
        },
        readBy: [
          {
            user: {
              type: mongoose.Schema.ObjectId,
              ref: 'User'
            },
            readAt: {
              type: Date,
              default: Date.now
            }
          }
        ]
      }
    ],
    statusHistory: [
      {
        status: {
          type: String,
          enum: ['open', 'in_progress', 'waiting_on_customer', 'waiting_on_third_party', 'resolved', 'closed'],
          required: true
        },
        changedBy: {
          type: mongoose.Schema.ObjectId,
          ref: 'User',
          required: true
        },
        changedAt: {
          type: Date,
          default: Date.now
        },
        note: String
      }
    ],
    resolution: {
      resolvedBy: {
        type: mongoose.Schema.ObjectId,
        ref: 'User'
      },
      resolvedAt: Date,
      resolutionNote: String,
      resolutionType: {
        type: String,
        enum: [
          'issue_fixed', 
          'refund_issued', 
          'replacement_sent', 
          'information_provided', 
          'no_action_needed',
          'escalated',
          'other'
        ]
      }
    },
    customerSatisfaction: {
      rating: {
        type: Number,
        min: 1,
        max: 5
      },
      feedback: String,
      submittedAt: Date
    },
    firstResponseTime: Date, // When first staff response was sent
    resolutionTime: Number, // Time to resolution in hours
    reopenCount: {
      type: Number,
      default: 0
    },
    isEscalated: {
      type: Boolean,
      default: false
    },
    escalationReason: String,
    escalatedAt: Date,
    escalatedBy: {
      type: mongoose.Schema.ObjectId,
      ref: 'User'
    },
    tags: [String]
  },
  {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true }
  }
);

// Live Chat Schema
const chatSchema = new mongoose.Schema(
  {
    chatId: {
      type: String,
      unique: true
    },
    user: {
      type: mongoose.Schema.ObjectId,
      ref: 'User',
      required: [true, 'Chat must belong to a user']
    },
    agent: {
      type: mongoose.Schema.ObjectId,
      ref: 'User' // Admin user
    },
    status: {
      type: String,
      enum: ['active', 'waiting', 'closed'],
      default: 'waiting'
    },
    startedAt: {
      type: Date,
      default: Date.now
    },
    endedAt: Date,
    messages: [
      {
        sender: {
          type: mongoose.Schema.ObjectId,
          ref: 'User',
          required: true
        },
        senderType: {
          type: String,
          enum: ['user', 'agent', 'bot'],
          required: true
        },
        message: {
          type: String,
          required: true,
          trim: true
        },
        attachments: [String],
        createdAt: {
          type: Date,
          default: Date.now
        },
        readAt: Date
      }
    ],
    relatedOrder: {
      type: mongoose.Schema.ObjectId,
      ref: 'Order'
    },
    relatedProduct: {
      type: mongoose.Schema.ObjectId,
      ref: 'Product'
    },
    convertedToTicket: {
      type: Boolean,
      default: false
    },
    relatedTicket: {
      type: mongoose.Schema.ObjectId,
      ref: 'Ticket'
    },
    customerSatisfaction: {
      rating: {
        type: Number,
        min: 1,
        max: 5
      },
      feedback: String,
      submittedAt: Date
    },
    waitTime: Number, // Time in seconds before agent joined
    duration: Number, // Total chat duration in seconds
    pageUrl: String, // Page where chat was initiated
    userDevice: {
      type: {
        type: String,
        enum: ['mobile', 'tablet', 'desktop', 'unknown'],
        default: 'unknown'
      },
      browser: String,
      os: String
    }
  },
  {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true }
  }
);

// Feedback Schema
const feedbackSchema = new mongoose.Schema(
  {
    user: {
      type: mongoose.Schema.ObjectId,
      ref: 'User'
    },
    type: {
      type: String,
      enum: ['suggestion', 'bug_report', 'feature_request', 'general_feedback', 'complaint'],
      required: [true, 'Feedback must have a type']
    },
    subject: {
      type: String,
      required: [true, 'Feedback must have a subject'],
      trim: true,
      maxlength: [100, 'Subject cannot be longer than 100 characters']
    },
    description: {
      type: String,
      required: [true, 'Feedback must have a description'],
      trim: true
    },
    category: {
      type: String,
      enum: [
        'user_interface', 
        'user_experience', 
        'product_listings', 
        'checkout_process', 
        'payment_system',
        'shipping_delivery',
        'customer_service',
        'creator_tools',
        'mobile_app',
        'website',
        'other'
      ],
      required: [true, 'Feedback must have a category']
    },
    status: {
      type: String,
      enum: ['new', 'under_review', 'planned', 'in_progress', 'completed', 'declined'],
      default: 'new'
    },
    priority: {
      type: String,
      enum: ['low', 'medium', 'high', 'critical'],
      default: 'medium'
    },
    assignedTo: {
      type: mongoose.Schema.ObjectId,
      ref: 'User' // Admin user
    },
    attachments: [String],
    response: {
      message: String,
      respondedBy: {
        type: mongoose.Schema.ObjectId,
        ref: 'User'
      },
      respondedAt: Date
    },
    upvotes: {
      type: Number,
      default: 0
    },
    upvotedBy: [
      {
        type: mongoose.Schema.ObjectId,
        ref: 'User'
      }
    ],
    isAnonymous: {
      type: Boolean,
      default: false
    },
    tags: [String],
    relatedFeedback: [
      {
        type: mongoose.Schema.ObjectId,
        ref: 'Feedback'
      }
    ]
  },
  {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true }
  }
);

// FAQ Schema
const faqSchema = new mongoose.Schema(
  {
    question: {
      type: String,
      required: [true, 'FAQ must have a question'],
      trim: true,
      unique: true
    },
    answer: {
      type: String,
      required: [true, 'FAQ must have an answer'],
      trim: true
    },
    category: {
      type: String,
      enum: [
        'account', 
        'orders', 
        'payments', 
        'shipping', 
        'returns', 
        'products',
        'creators',
        'bales',
        'technical',
        'other'
      ],
      required: [true, 'FAQ must have a category']
    },
    tags: [String],
    isPublished: {
      type: Boolean,
      default: true
    },
    viewCount: {
      type: Number,
      default: 0
    },
    helpfulCount: {
      type: Number,
      default: 0
    },
    notHelpfulCount: {
      type: Number,
      default: 0
    },
    createdBy: {
      type: mongoose.Schema.ObjectId,
      ref: 'User'
    },
    lastUpdatedBy: {
      type: mongoose.Schema.ObjectId,
      ref: 'User'
    },
    order: {
      type: Number,
      default: 0
    },
    relatedFaqs: [
      {
        type: mongoose.Schema.ObjectId,
        ref: 'FAQ'
      }
    ]
  },
  {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true }
  }
);

// Pre-save middleware to generate ticket number
ticketSchema.pre('save', async function(next) {
  if (!this.isNew) return next();

  try {
    // Get current date in format YYYYMMDD
    const date = new Date();
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const datePrefix = `${year}${month}${day}`;
    
    // Find the highest ticket number for today
    const highestTicket = await this.constructor.findOne(
      { ticketNumber: new RegExp(`^TKT-${datePrefix}-`) },
      { ticketNumber: 1 },
      { sort: { ticketNumber: -1 } }
    );
    
    let sequenceNumber = 1;
    if (highestTicket && highestTicket.ticketNumber) {
      const currentSequence = parseInt(highestTicket.ticketNumber.split('-')[2], 10);
      sequenceNumber = currentSequence + 1;
    }
    
    // Format: TKT-YYYYMMDD-XXXX (XXXX is a sequential number)
    this.ticketNumber = `TKT-${datePrefix}-${String(sequenceNumber).padStart(4, '0')}`;
    next();
  } catch (error) {
    next(error);
  }
});

// Pre-save middleware to generate chat ID
chatSchema.pre('save', async function(next) {
  if (!this.isNew) return next();

  try {
    // Get current date in format YYYYMMDD
    const date = new Date();
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const datePrefix = `${year}${month}${day}`;
    
    // Find the highest chat ID for today
    const highestChat = await this.constructor.findOne(
      { chatId: new RegExp(`^CHAT-${datePrefix}-`) },
      { chatId: 1 },
      { sort: { chatId: -1 } }
    );
    
    let sequenceNumber = 1;
    if (highestChat && highestChat.chatId) {
      const currentSequence = parseInt(highestChat.chatId.split('-')[2], 10);
      sequenceNumber = currentSequence + 1;
    }
    
    // Format: CHAT-YYYYMMDD-XXXX (XXXX is a sequential number)
    this.chatId = `CHAT-${datePrefix}-${String(sequenceNumber).padStart(4, '0')}`;
    next();
  } catch (error) {
    next(error);
  }
});

// Virtual for time to first response
ticketSchema.virtual('timeToFirstResponse').get(function() {
  if (!this.firstResponseTime || !this.createdAt) return null;
  
  const createdAt = new Date(this.createdAt);
  const firstResponse = new Date(this.firstResponseTime);
  
  // Return time difference in hours
  return Math.round((firstResponse - createdAt) / (1000 * 60 * 60) * 10) / 10;
});

// Virtual for ticket age
ticketSchema.virtual('ticketAge').get(function() {
  if (!this.createdAt) return null;
  
  const createdAt = new Date(this.createdAt);
  const now = new Date();
  
  // Return age in days
  return Math.round((now - createdAt) / (1000 * 60 * 60 * 24) * 10) / 10;
});

// Virtual for unread messages count
ticketSchema.virtual('unreadMessagesCount').get(function() {
  if (!this.messages || !this.messages.length) return 0;
  
  // Count messages not read by the user
  return this.messages.filter(msg => 
    msg.senderType !== 'user' && 
    (!msg.readBy || !msg.readBy.some(read => read.user.toString() === this.user.toString()))
  ).length;
});

// Indexes for Ticket
ticketSchema.index({ user: 1 });
ticketSchema.index({ ticketNumber: 1 });
ticketSchema.index({ status: 1 });
ticketSchema.index({ category: 1 });
ticketSchema.index({ priority: 1 });
ticketSchema.index({ assignedTo: 1 });
ticketSchema.index({ createdAt: -1 });
ticketSchema.index({ 'resolution.resolvedAt': -1 });

// Indexes for Chat
chatSchema.index({ user: 1 });
chatSchema.index({ chatId: 1 });
chatSchema.index({ status: 1 });
chatSchema.index({ agent: 1 });
chatSchema.index({ startedAt: -1 });

// Indexes for Feedback
feedbackSchema.index({ user: 1 });
feedbackSchema.index({ type: 1 });
feedbackSchema.index({ category: 1 });
feedbackSchema.index({ status: 1 });
feedbackSchema.index({ priority: 1 });
feedbackSchema.index({ upvotes: -1 });
feedbackSchema.index({ createdAt: -1 });

// Indexes for FAQ
faqSchema.index({ category: 1 });
faqSchema.index({ isPublished: 1 });
faqSchema.index({ viewCount: -1 });
faqSchema.index({ order: 1 });
faqSchema.index({ tags: 1 });
faqSchema.index({ question: 'text', answer: 'text' });

// Create models
const Ticket = mongoose.model('Ticket', ticketSchema);
const Chat = mongoose.model('Chat', chatSchema);
const Feedback = mongoose.model('Feedback', feedbackSchema);
const FAQ = mongoose.model('FAQ', faqSchema);

module.exports = {
  Ticket,
  Chat,
  Feedback,
  FAQ
};