const Review = require('../../models/review.model');
const Product = require('../../models/product.model');
const Bale = require('../../models/bale.model');
const { Creator } = require('../../models/user.model');
const AppError = require('../../utils/appError');
const catchAsync = require('../../utils/catchAsync');

/**
 * Create a new review
 * @route POST /api/v1/reviews
 * @access Private (Buyer only)
 */
exports.createReview = catchAsync(async (req, res, next) => {
  // Add user to req.body
  req.body.user = req.user.id;

  // Validate required fields
  if (!req.body.title) {
    return next(new AppError('Please provide a review title', 400));
  }

  if (!req.body.review) {
    return next(new AppError('Please provide review details', 400));
  }

  if (!req.body.rating || req.body.rating < 1 || req.body.rating > 5) {
    return next(new AppError('Please provide a rating between 1 and 5', 400));
  }

  // Check if the item exists
  let item;
  if (req.body.product) {
    item = await Product.findById(req.body.product);
    if (!item) {
      return next(new AppError('No product found with that ID', 404));
    }
    req.body.creator = item.creator;
  } else if (req.body.bale) {
    item = await Bale.findById(req.body.bale);
    if (!item) {
      return next(new AppError('No bale found with that ID', 404));
    }
    req.body.creator = item.creator;
  } else if (req.body.creator) {
    // Review for a creator
    const creator = await Creator.findById(req.body.creator);
    if (!creator) {
      return next(new AppError('No creator found with that ID', 404));
    }
  } else {
    return next(new AppError('Review must be for a product, bale, or creator', 400));
  }

  // Check if user has already reviewed this item
  const existingReview = await Review.findOne({
    user: req.user.id,
    $or: [
      { product: req.body.product },
      { bale: req.body.bale },
      { creator: req.body.creator }
    ]
  });

  if (existingReview) {
    return next(new AppError('You have already reviewed this item', 400));
  }

  const newReview = await Review.create(req.body);

  res.status(201).json({
    status: 'success',
    data: {
      review: newReview
    }
  });
});

/**
 * Get all reviews
 * @route GET /api/v1/reviews
 * @access Public
 */
exports.getAllReviews = catchAsync(async (req, res, next) => {
  // Build query
  let query = Review.find();

  // Add user context to query options if user is authenticated
  if (req.user) {
    query.setOptions({ user: req.user });
  }

  // Filter by product, bale, or creator if specified
  if (req.query.product) {
    query = query.find({ product: req.query.product });
  } else if (req.query.bale) {
    query = query.find({ bale: req.query.bale });
  } else if (req.query.creator) {
    query = query.find({ creator: req.query.creator });
  }

  // Sort
  if (req.query.sort) {
    const sortBy = req.query.sort.split(',').join(' ');
    query = query.sort(sortBy);
  } else {
    query = query.sort('-createdAt');
  }

  // Pagination
  const page = req.query.page * 1 || 1;
  const limit = req.query.limit * 1 || 20;
  const skip = (page - 1) * limit;
  query = query.skip(skip).limit(limit);

  // Execute query
  const reviews = await query.populate({
    path: 'user',
    select: 'name photo'
  });

  res.status(200).json({
    status: 'success',
    results: reviews.length,
    data: {
      reviews
    }
  });
});

/**
 * Get review by ID
 * @route GET /api/v1/reviews/:id
 * @access Public
 */
exports.getReview = catchAsync(async (req, res, next) => {
  const review = await Review.findById(req.params.id)
    .populate({
      path: 'user',
      select: 'name photo'
    })
    .populate({
      path: 'creator',
      select: 'name photo storeInfo'
    });

  if (!review) {
    return next(new AppError('No review found with that ID', 404));
  }

  res.status(200).json({
    status: 'success',
    data: {
      review
    }
  });
});

/**
 * Update review
 * @route PATCH /api/v1/reviews/:id
 * @access Private (Owner of review only)
 */
exports.updateReview = catchAsync(async (req, res, next) => {
  // Find review
  let review = await Review.findById(req.params.id);

  if (!review) {
    return next(new AppError('No review found with that ID', 404));
  }

  // Check if the review belongs to the user
  if (review.user.toString() !== req.user.id) {
    return next(
      new AppError('You do not have permission to update this review', 403)
    );
  }

  // Validate request body
  if (!req.body || Object.keys(req.body).length === 0) {
    return next(new AppError('Please provide data to update', 400));
  }

  // Validate rating if provided
  if (req.body.rating !== undefined) {
    if (req.body.rating < 1 || req.body.rating > 5) {
      return next(new AppError('Rating must be between 1 and 5', 400));
    }
  }

  // Validate title if provided
  if (req.body.title !== undefined && (!req.body.title || req.body.title.trim() === '')) {
    return next(new AppError('Review title cannot be empty', 400));
  }

  // Validate review text if provided
  if (req.body.review !== undefined && (!req.body.review || req.body.review.trim() === '')) {
    return next(new AppError('Review details cannot be empty', 400));
  }

  // Create a filtered body to prevent unwanted fields
  const filteredBody = {};
  const allowedFields = ['title', 'review', 'rating', 'photos'];

  Object.keys(req.body).forEach(key => {
    if (allowedFields.includes(key)) {
      filteredBody[key] = req.body[key];
    }
  });

  // Update review
  review = await Review.findByIdAndUpdate(req.params.id, filteredBody, {
    new: true,
    runValidators: true
  });

  res.status(200).json({
    status: 'success',
    data: {
      review
    }
  });
});

/**
 * Delete review
 * @route DELETE /api/v1/reviews/:id
 * @access Private (Owner of review or Admin)
 */
exports.deleteReview = catchAsync(async (req, res, next) => {
  // Find review
  const review = await Review.findById(req.params.id);

  if (!review) {
    return next(new AppError('No review found with that ID', 404));
  }

  // Check if the review belongs to the user or user is admin
  if (review.user.toString() !== req.user.id && req.user.role !== 'admin') {
    return next(
      new AppError('You do not have permission to delete this review', 403)
    );
  }

  // Delete review
  await Review.findByIdAndDelete(req.params.id);

  res.status(204).json({
    status: 'success',
    data: null
  });
});

/**
 * Get reviews for a creator
 * @route GET /api/v1/creators/:id/reviews
 * @access Public
 */
exports.getCreatorReviews = catchAsync(async (req, res, next) => {
  const query = Review.find({ creator: req.params.id });

  // Add user context to query options if user is authenticated
  if (req.user) {
    query.setOptions({ user: req.user });
  }

  const reviews = await query.populate({
    path: 'user',
    select: 'name photo'
  });

  res.status(200).json({
    status: 'success',
    results: reviews.length,
    data: {
      reviews
    }
  });
});

/**
 * Get reviews for a product
 * @route GET /api/v1/products/:id/reviews
 * @access Public
 */
exports.getProductReviews = catchAsync(async (req, res, next) => {
  const query = Review.find({ product: req.params.id });

  // Add user context to query options if user is authenticated
  if (req.user) {
    query.setOptions({ user: req.user });
  }

  const reviews = await query.populate({
    path: 'user',
    select: 'name photo'
  });

  res.status(200).json({
    status: 'success',
    results: reviews.length,
    data: {
      reviews
    }
  });
});

/**
 * Get reviews for a bale
 * @route GET /api/v1/bales/:id/reviews
 * @access Public
 */
exports.getBaleReviews = catchAsync(async (req, res, next) => {
  const query = Review.find({ bale: req.params.id });

  // Add user context to query options if user is authenticated
  if (req.user) {
    query.setOptions({ user: req.user });
  }

  const reviews = await query.populate({
    path: 'user',
    select: 'name photo'
  });

  res.status(200).json({
    status: 'success',
    results: reviews.length,
    data: {
      reviews
    }
  });
});
