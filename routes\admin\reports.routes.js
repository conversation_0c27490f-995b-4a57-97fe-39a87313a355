const express = require('express');
const router = express.Router();
const financialReportController = require('../../controllers/admin/financialReport.controller');
const { protect, restrictTo } = require('../../middleware/auth.middleware');

// Protect all report routes
router.use(protect);
router.use(restrictTo('admin'));

// Revenue reports
router.get('/revenue', financialReportController.getPlatformRevenue);
router.get('/revenue/breakdown', financialReportController.getRevenueBreakdown);

// Creator earnings reports
router.get('/creators/earnings', financialReportController.getCreatorEarnings);

// Shipping reports
router.get('/shipping', financialReportController.getShippingStats);

// Payment method reports
router.get('/payments', financialReportController.getPaymentMethodStats);

// Payout reports
router.get('/payouts', financialReportController.getPayoutSummary);

// Overall financial summary
router.get('/summary', financialReportController.getFinancialSummary);

module.exports = router;
