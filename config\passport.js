 const passport = require('passport');
const { Strategy: JwtStrategy, ExtractJwt } = require('passport-jwt');
const LocalStrategy = require('passport-local').Strategy;
const GoogleStrategy = require('passport-google-oauth20').Strategy;
const FacebookStrategy = require('passport-facebook').Strategy;
const { BaseUser, Buyer, Creator, Admin } = require('../models/user.model');
const authConfig = require('./auth.config');


// This function returns a configured strategy per user type
const configureGoogleStrategy = (userType = 'creator') => {
  return new GoogleStrategy(
    {
      clientID: authConfig.google.clientID,
      clientSecret: authConfig.google.clientSecret,
      callbackURL: `${authConfig.google.callbackBaseURL}/${userType}/callback`,
      scope: ['profile', 'email'],
      passReqToCallback: true, // allows access to req in callback
    },
    async (req, accessToken, refreshToken, profile, done) => {
      try {
        const email = profile.emails[0].value;
        let user = await BaseUser.findOne({ email }).select('+active');

        if (user) {
          // Check if user is active
          if (!user.active) {
            return done(null, false, { message: 'Your account has been deactivated. Please contact support.' });
          }
          return done(null, user);
        }

        const userData = {
          name: profile.displayName,
          email,
          photo: profile.photos[0].value,
          role: userType,
          socialProvider: 'google',
          emailVerified: true // Social auth users have verified emails
        };

        const newUser =
          userType === 'creator'
            ? await Creator.create(userData)
            : await Buyer.create(userData);

        return done(null, newUser);
      } catch (err) {
        return done(err);
      }
    }
  );
};


const configureFacebookStrategy = (userType = 'buyer') => {
  return new FacebookStrategy(
    {
      clientID: authConfig.facebook.clientID,
      clientSecret: authConfig.facebook.clientSecret,
      callbackURL: `${authConfig.facebook.callbackBaseURL}/auth/facebook/${userType}/callback`,
      profileFields: authConfig.facebook.profileFields,
      passReqToCallback: true
    },
    async (req, accessToken, refreshToken, profile, done) => {
      try {
        const email = profile.emails?.[0]?.value || '';
        const photo = profile.photos?.[0]?.value || '';

        let user = await BaseUser.findOne({ email }).select('+active');

        if (user) {
          // Check if user is active
          if (!user.active) {
            return done(null, false, { message: 'Your account has been deactivated. Please contact support.' });
          }
          return done(null, user);
        }

        const userData = {
          name: profile.displayName,
          email,
          photo,
          facebookId: profile.id,
          role: userType,
          socialProvider: 'facebook',
          emailVerified: true // Social auth users have verified emails
        };

        const newUser =
          userType === 'creator'
            ? await Creator.create(userData)
            : await Buyer.create(userData);

        return done(null, newUser);
      } catch (err) {
        return done(err);
      }
    }
  );
};

// JWT Strategy
const jwtOptions = {
  jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
  secretOrKey: authConfig.jwtSecret
};

passport.use(new JwtStrategy(jwtOptions, async (jwtPayload, done) => {
  try {
    // Find user by ID and explicitly select active field
    const user = await BaseUser.findById(jwtPayload.id).select('+active');

    // Check if user exists
    if (!user) {
      return done(null, false);
    }

    // Check if user is active
    if (!user.active) {
      return done(null, false, { message: 'Your account has been deactivated. Please contact support.' });
    }

    return done(null, user);
  } catch (error) {
    return done(error, false);
  }
}));

// Local Strategy (username/password)
passport.use(new LocalStrategy(
  {
    usernameField: 'email',
    passwordField: 'password'
  },
  async (email, password, done) => {
    try {
      // Find user by email and explicitly select active field
      const user = await BaseUser.findOne({ email }).select('+password +active');

      // Check if user exists
      if (!user) {
        return done(null, false, { message: 'No account found with this email address. Please check your email or sign up for a new account.' });
      }

      // Check if user is active
      if (!user.active) {
        return done(null, false, { message: 'Your account has been deactivated. Please contact support for assistance.' });
      }

      // Check if password is correct
      const isMatch = await user.correctPassword(password, user.password);
      if (!isMatch) {
        return done(null, false, { message: 'Incorrect password. Please check your password and try again.' });
      }

      // Return user without password
      user.password = undefined;
      return done(null, user);
    } catch (error) {
      return done(error);
    }
  }
));

passport.use('google-buyer', configureGoogleStrategy('buyer'));
passport.use('google-creator', configureGoogleStrategy('creator'));



passport.use('facebook-buyer', configureFacebookStrategy('buyer'));
passport.use('facebook-creator', configureFacebookStrategy('creator'));


// Facebook Strategy
if (authConfig.facebook.clientID && authConfig.facebook.clientSecret) {
  passport.use(new FacebookStrategy(
    {
      clientID: authConfig.facebook.clientID,
      clientSecret: authConfig.facebook.clientSecret,
      callbackURL: authConfig.facebook.callbackURL,
      profileFields: authConfig.facebook.profileFields,
      enableProof: true
    },
    async (accessToken, refreshToken, profile, done) => {
      try {
        // Check if user already exists
        let user = await BaseUser.findOne({ email: profile.emails[0].value }).select('+active');

        if (user) {
          // Check if user is active
          if (!user.active) {
            return done(null, false, { message: 'Your account has been deactivated. Please contact support.' });
          }
          // User exists and is active, return user
          return done(null, user);
        } else {
          // Create new user as a Buyer by default
          // For social sign-ins, we don't need to set a password
          const newUser = await Buyer.create({
            name: profile.displayName,
            email: profile.emails[0].value,
            photo: profile.photos ? profile.photos[0].value : 'default.jpg',
            role: 'buyer',
            // Set a flag to indicate this is a social auth user
            socialProvider: 'facebook',
            emailVerified: true // Social auth users have verified emails
          });

          return done(null, newUser);
        }
      } catch (error) {
        return done(error);
      }
    }
  ));
}

// Serialize and deserialize user
passport.serializeUser((user, done) => {
  done(null, user.id);
});

passport.deserializeUser(async (id, done) => {
  try {
    // Exclude password when deserializing user
    const user = await BaseUser.findById(id).select('-password');
    done(null, user);
  } catch (error) {
    done(error);
  }
});

module.exports = passport;


