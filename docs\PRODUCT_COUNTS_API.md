# Product Counts API Documentation

## Overview
The Product Counts API provides real-time counts of products by different statuses and stock levels for creator dashboard filtering.

## Endpoint
**GET** `/api/v1/creators/products/counts`

## Authentication
Requires creator authentication via Bear<PERSON> token.

## Response Format

### Success Response (200)
```json
{
  "status": "success",
  "data": {
    "all": 45,
    "approved": 32,
    "pending": 8,
    "rejected": 2,
    "lowStock": 5,
    "outOfStock": 3
  }
}
```

### Error Response (401)
```json
{
  "status": "fail",
  "message": "You are not logged in! Please log in to get access."
}
```

## Count Definitions

| Field | Description | Criteria |
|-------|-------------|----------|
| `all` | Total products | All products by the creator |
| `approved` | Approved products | Products with status 'active' |
| `pending` | Pending approval | Products with status 'pending' |
| `rejected` | Rejected products | Products with status 'rejected' |
| `lowStock` | Low stock products | Total stock > 0 and ≤ 10 units |
| `outOfStock` | Out of stock products | Total stock = 0 units |

## Stock Calculation
- **Total Stock**: Sum of quantities across all variations of a product
- **Low Stock**: Products with 1-10 total units remaining
- **Out of Stock**: Products with 0 total units

## Usage Examples

### JavaScript/Fetch
```javascript
const getProductCounts = async () => {
  try {
    const response = await fetch('/api/v1/creators/products/counts', {
      headers: {
        'Authorization': `Bearer ${creatorToken}`,
        'Content-Type': 'application/json'
      }
    });

    const data = await response.json();
    
    if (data.status === 'success') {
      return data.data;
    } else {
      throw new Error(data.message);
    }
  } catch (error) {
    console.error('Error fetching product counts:', error);
    throw error;
  }
};

// Usage
getProductCounts().then(counts => {
  console.log('Product counts:', counts);
  // Update UI with counts
  updateFilterCounts(counts);
});
```

### React Hook
```javascript
import { useState, useEffect } from 'react';

const useProductCounts = () => {
  const [counts, setCounts] = useState({
    all: 0,
    approved: 0,
    pending: 0,
    rejected: 0,
    lowStock: 0,
    outOfStock: 0
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  const fetchCounts = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await fetch('/api/v1/creators/products/counts', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      const data = await response.json();
      
      if (data.status === 'success') {
        setCounts(data.data);
      } else {
        setError(data.message);
      }
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchCounts();
  }, []);

  return { counts, loading, error, refetch: fetchCounts };
};

export default useProductCounts;
```

### React Component
```jsx
import React from 'react';
import useProductCounts from './hooks/useProductCounts';

const ProductCountsFilter = ({ onFilterChange, activeFilter }) => {
  const { counts, loading, error } = useProductCounts();

  if (loading) return <div>Loading counts...</div>;
  if (error) return <div>Error: {error}</div>;

  const filters = [
    { key: 'all', label: 'All Products', count: counts.all },
    { key: 'approved', label: 'Approved', count: counts.approved },
    { key: 'pending', label: 'Pending', count: counts.pending },
    { key: 'rejected', label: 'Rejected', count: counts.rejected },
    { key: 'lowStock', label: 'Low Stock', count: counts.lowStock },
    { key: 'outOfStock', label: 'Out of Stock', count: counts.outOfStock }
  ];

  return (
    <div className="product-counts-filter">
      {filters.map(filter => (
        <button
          key={filter.key}
          className={`filter-btn ${activeFilter === filter.key ? 'active' : ''}`}
          onClick={() => onFilterChange(filter.key)}
        >
          {filter.label} ({filter.count})
        </button>
      ))}
    </div>
  );
};

export default ProductCountsFilter;
```

## Integration with Product List

### Updated Product List Query Parameters
When using the counts for filtering, map the filter keys to appropriate query parameters:

```javascript
const filterToQuery = {
  all: {},
  approved: { status: 'active' },
  pending: { status: 'pending' },
  rejected: { status: 'rejected' },
  lowStock: { stockLevel: 'low' },
  outOfStock: { stockLevel: 'out' }
};

const handleFilterChange = (filterKey) => {
  const queryParams = filterToQuery[filterKey];
  fetchProducts(queryParams);
};
```

### Product List API Support
The product list endpoint (`GET /api/v1/creators/products`) now supports the `stockLevel` parameter:

- `stockLevel=low` - Products with 1-10 total units
- `stockLevel=out` - Products with 0 total units

Example:
```javascript
// Get low stock products
fetch('/api/v1/creators/products?stockLevel=low')

// Get out of stock products  
fetch('/api/v1/creators/products?stockLevel=out')

// Combine with other filters
fetch('/api/v1/creators/products?status=active&stockLevel=low')
```

## Performance Notes

- The endpoint uses efficient MongoDB aggregation for stock calculations
- Counts are calculated in real-time for accuracy
- Consider caching results for 1-2 minutes if called frequently
- The endpoint typically responds in under 100ms for most product volumes

## Error Handling

### Common Errors
- **401 Unauthorized**: Invalid or missing authentication token
- **403 Forbidden**: Account not verified or insufficient permissions
- **500 Internal Server Error**: Database or server issues

### Best Practices
- Always handle loading and error states in UI
- Provide fallback counts (0) when API fails
- Retry failed requests with exponential backoff
- Show user-friendly error messages

## Testing

### Manual Testing
```bash
# Test with valid creator token
curl -H "Authorization: Bearer YOUR_CREATOR_TOKEN" \
  https://everyfash-api.onrender.com/api/v1/creators/products/counts

# Expected response
{
  "status": "success",
  "data": {
    "all": 45,
    "approved": 32,
    "pending": 8,
    "rejected": 2,
    "lowStock": 5,
    "outOfStock": 3
  }
}
```

### Unit Test Example
```javascript
describe('Product Counts API', () => {
  it('should return product counts for authenticated creator', async () => {
    const response = await request(app)
      .get('/api/v1/creators/products/counts')
      .set('Authorization', `Bearer ${creatorToken}`)
      .expect(200);

    expect(response.body.status).toBe('success');
    expect(response.body.data).toHaveProperty('all');
    expect(response.body.data).toHaveProperty('approved');
    expect(response.body.data).toHaveProperty('pending');
    expect(response.body.data).toHaveProperty('rejected');
    expect(response.body.data).toHaveProperty('lowStock');
    expect(response.body.data).toHaveProperty('outOfStock');
    
    // Verify counts are numbers
    Object.values(response.body.data).forEach(count => {
      expect(typeof count).toBe('number');
      expect(count).toBeGreaterThanOrEqual(0);
    });
  });
});
```

This API provides the foundation for building responsive, data-driven product filtering interfaces in creator dashboards.
