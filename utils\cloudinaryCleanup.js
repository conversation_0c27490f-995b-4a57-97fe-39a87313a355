const { deleteFromCloudinary, extractPublicId } = require('../config/cloudinary.config');
const AppError = require('./appError');

/**
 * Delete a single image from Cloudinary
 * @param {string} imageUrl - Cloudinary image URL
 * @returns {Promise<object>} - Delete result
 */
const deleteSingleImage = async (imageUrl) => {
  try {
    if (!imageUrl || typeof imageUrl !== 'string') {
      return { success: false, error: 'Invalid image URL' };
    }

    const publicId = extractPublicId(imageUrl);
    if (!publicId) {
      return { success: false, error: 'Could not extract public ID from URL' };
    }

    const result = await deleteFromCloudinary(publicId);
    
    if (result.result === 'ok') {
      return { success: true, publicId, result };
    } else {
      return { success: false, error: `Delete failed: ${result.result}`, publicId };
    }
  } catch (error) {
    console.error('Error deleting image from Cloudinary:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Delete multiple images from Cloudinary
 * @param {string[]} imageUrls - Array of Cloudinary image URLs
 * @returns {Promise<object>} - Delete results summary
 */
const deleteMultipleImages = async (imageUrls) => {
  if (!Array.isArray(imageUrls) || imageUrls.length === 0) {
    return { success: true, deleted: 0, failed: 0, results: [] };
  }

  const results = [];
  let deleted = 0;
  let failed = 0;

  // Process deletions in parallel but with a reasonable limit
  const batchSize = 5;
  for (let i = 0; i < imageUrls.length; i += batchSize) {
    const batch = imageUrls.slice(i, i + batchSize);
    const batchPromises = batch.map(url => deleteSingleImage(url));
    const batchResults = await Promise.all(batchPromises);
    
    batchResults.forEach((result, index) => {
      const url = batch[index];
      if (result.success) {
        deleted++;
        results.push({ url, status: 'deleted', publicId: result.publicId });
      } else {
        failed++;
        results.push({ url, status: 'failed', error: result.error });
      }
    });
  }

  return {
    success: failed === 0,
    deleted,
    failed,
    total: imageUrls.length,
    results
  };
};

/**
 * Compare old and new image arrays and delete removed images
 * @param {string[]} oldImages - Previous image URLs
 * @param {string[]} newImages - New image URLs
 * @returns {Promise<object>} - Cleanup result
 */
const cleanupRemovedImages = async (oldImages = [], newImages = []) => {
  // Find images that were removed
  const removedImages = oldImages.filter(oldUrl => !newImages.includes(oldUrl));
  
  if (removedImages.length === 0) {
    return { success: true, deleted: 0, removedImages: [] };
  }

  console.log(`Cleaning up ${removedImages.length} removed images from Cloudinary`);
  
  const deleteResult = await deleteMultipleImages(removedImages);
  
  return {
    success: deleteResult.success,
    deleted: deleteResult.deleted,
    failed: deleteResult.failed,
    removedImages,
    results: deleteResult.results
  };
};

/**
 * Delete all images associated with a document/entity
 * @param {object} entity - The entity containing image URLs
 * @param {string[]} imageFields - Array of field names that contain image URLs
 * @returns {Promise<object>} - Cleanup result
 */
const cleanupEntityImages = async (entity, imageFields = ['images']) => {
  const allImages = [];
  
  // Collect all image URLs from specified fields
  imageFields.forEach(field => {
    const fieldValue = entity[field];
    if (Array.isArray(fieldValue)) {
      allImages.push(...fieldValue);
    } else if (typeof fieldValue === 'string' && fieldValue) {
      allImages.push(fieldValue);
    }
  });

  if (allImages.length === 0) {
    return { success: true, deleted: 0, images: [] };
  }

  console.log(`Cleaning up ${allImages.length} images for entity ${entity._id}`);
  
  const deleteResult = await deleteMultipleImages(allImages);
  
  return {
    success: deleteResult.success,
    deleted: deleteResult.deleted,
    failed: deleteResult.failed,
    images: allImages,
    results: deleteResult.results
  };
};

/**
 * Middleware to automatically cleanup images when updating documents
 * Usage: Add this middleware before save operations
 */
const createImageCleanupMiddleware = (imageFields = ['images']) => {
  return async function(next) {
    // Only run on updates, not on new documents
    if (this.isNew) {
      return next();
    }

    try {
      // Get the original document to compare images
      const originalDoc = await this.constructor.findById(this._id);
      if (!originalDoc) {
        return next();
      }

      // Store cleanup tasks to run after successful save
      this._imageCleanupTasks = [];

      imageFields.forEach(field => {
        const oldImages = originalDoc[field] || [];
        const newImages = this[field] || [];
        
        // Find removed images
        const removedImages = Array.isArray(oldImages) 
          ? oldImages.filter(oldUrl => !newImages.includes(oldUrl))
          : [];

        if (removedImages.length > 0) {
          this._imageCleanupTasks.push({
            field,
            removedImages
          });
        }
      });

      next();
    } catch (error) {
      console.error('Error in image cleanup middleware:', error);
      // Don't fail the save operation due to cleanup issues
      next();
    }
  };
};

/**
 * Post-save middleware to execute image cleanup tasks
 */
const executeImageCleanup = async function() {
  if (!this._imageCleanupTasks || this._imageCleanupTasks.length === 0) {
    return;
  }

  try {
    for (const task of this._imageCleanupTasks) {
      const result = await deleteMultipleImages(task.removedImages);
      console.log(`Cleaned up ${result.deleted} images from field '${task.field}' for ${this.constructor.modelName} ${this._id}`);
      
      if (result.failed > 0) {
        console.warn(`Failed to delete ${result.failed} images from Cloudinary:`, result.results.filter(r => r.status === 'failed'));
      }
    }
  } catch (error) {
    console.error('Error executing image cleanup:', error);
    // Don't throw error as the main operation was successful
  } finally {
    // Clean up the cleanup tasks
    delete this._imageCleanupTasks;
  }
};

/**
 * Utility to manually trigger cleanup for specific image URLs
 * Useful for one-off cleanups or batch operations
 */
const manualCleanup = async (imageUrls, options = {}) => {
  const { dryRun = false, batchSize = 10 } = options;
  
  if (dryRun) {
    console.log('DRY RUN: Would delete the following images:');
    imageUrls.forEach(url => {
      const publicId = extractPublicId(url);
      console.log(`- ${url} (public_id: ${publicId})`);
    });
    return { success: true, deleted: 0, dryRun: true, total: imageUrls.length };
  }

  return await deleteMultipleImages(imageUrls);
};

module.exports = {
  deleteSingleImage,
  deleteMultipleImages,
  cleanupRemovedImages,
  cleanupEntityImages,
  createImageCleanupMiddleware,
  executeImageCleanup,
  manualCleanup
};
