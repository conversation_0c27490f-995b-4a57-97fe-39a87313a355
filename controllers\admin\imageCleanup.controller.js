const catchAsync = require('../../utils/catchAsync');
const AppError = require('../../utils/appError');
const { 
  deleteSingleImage, 
  deleteMultipleImages, 
  manualCleanup 
} = require('../../utils/cloudinaryCleanup');
const { findOrphanedImages } = require('../../scripts/cleanupOrphanedImages');

/**
 * Scan for orphaned images
 * @route GET /api/v1/admin/images/scan
 * @access Private (Admin only)
 */
exports.scanOrphanedImages = catchAsync(async (req, res, next) => {
  const orphanedUrls = await findOrphanedImages();
  
  res.status(200).json({
    status: 'success',
    data: {
      orphanedCount: orphanedUrls.length,
      orphanedImages: orphanedUrls,
      message: orphanedUrls.length > 0 
        ? `Found ${orphanedUrls.length} orphaned images`
        : 'No orphaned images found'
    }
  });
});

/**
 * Clean up orphaned images
 * @route DELETE /api/v1/admin/images/cleanup
 * @access Private (Admin only)
 */
exports.cleanupOrphanedImages = catchAsync(async (req, res, next) => {
  const { dryRun = false } = req.query;
  
  // Find orphaned images
  const orphanedUrls = await findOrphanedImages();
  
  if (orphanedUrls.length === 0) {
    return res.status(200).json({
      status: 'success',
      data: {
        deleted: 0,
        failed: 0,
        message: 'No orphaned images found'
      }
    });
  }
  
  if (dryRun === 'true') {
    return res.status(200).json({
      status: 'success',
      data: {
        dryRun: true,
        wouldDelete: orphanedUrls.length,
        orphanedImages: orphanedUrls,
        message: `Would delete ${orphanedUrls.length} orphaned images`
      }
    });
  }
  
  // Execute cleanup
  const result = await manualCleanup(orphanedUrls);
  
  res.status(200).json({
    status: 'success',
    data: {
      deleted: result.deleted,
      failed: result.failed,
      total: result.total,
      results: result.results,
      message: `Cleanup completed. Deleted ${result.deleted} images, ${result.failed} failed.`
    }
  });
});

/**
 * Delete specific images by URLs
 * @route DELETE /api/v1/admin/images/delete
 * @access Private (Admin only)
 */
exports.deleteSpecificImages = catchAsync(async (req, res, next) => {
  const { imageUrls } = req.body;
  
  if (!imageUrls || !Array.isArray(imageUrls) || imageUrls.length === 0) {
    return next(new AppError('Please provide an array of image URLs to delete', 400));
  }
  
  if (imageUrls.length > 50) {
    return next(new AppError('Cannot delete more than 50 images at once', 400));
  }
  
  const result = await deleteMultipleImages(imageUrls);
  
  res.status(200).json({
    status: 'success',
    data: {
      deleted: result.deleted,
      failed: result.failed,
      total: result.total,
      results: result.results,
      message: `Deleted ${result.deleted} images, ${result.failed} failed.`
    }
  });
});

/**
 * Delete a single image by URL
 * @route DELETE /api/v1/admin/images/delete-single
 * @access Private (Admin only)
 */
exports.deleteSingleImageByUrl = catchAsync(async (req, res, next) => {
  const { imageUrl } = req.body;
  
  if (!imageUrl || typeof imageUrl !== 'string') {
    return next(new AppError('Please provide a valid image URL', 400));
  }
  
  const result = await deleteSingleImage(imageUrl);
  
  if (result.success) {
    res.status(200).json({
      status: 'success',
      data: {
        deleted: true,
        publicId: result.publicId,
        message: 'Image deleted successfully'
      }
    });
  } else {
    res.status(400).json({
      status: 'fail',
      data: {
        deleted: false,
        error: result.error,
        message: 'Failed to delete image'
      }
    });
  }
});

/**
 * Get Cloudinary storage statistics
 * @route GET /api/v1/admin/images/stats
 * @access Private (Admin only)
 */
exports.getStorageStats = catchAsync(async (req, res, next) => {
  try {
    const { cloudinary } = require('../../config/cloudinary.config');
    
    // Get usage statistics
    const usage = await cloudinary.api.usage();
    
    // Get folder statistics
    const folders = [
      'everyfash/products',
      'everyfash/bales',
      'everyfash/profiles', 
      'everyfash/shop',
      'everyfash/verification'
    ];
    
    const folderStats = {};
    for (const folder of folders) {
      try {
        const result = await cloudinary.search
          .expression(`folder:${folder}`)
          .aggregate('resource_count')
          .execute();
        
        folderStats[folder] = {
          count: result.total_count || 0,
          // You could add more detailed stats here if needed
        };
      } catch (error) {
        folderStats[folder] = { count: 0, error: error.message };
      }
    }
    
    res.status(200).json({
      status: 'success',
      data: {
        usage: {
          credits: usage.credits,
          used_percent: usage.used_percent,
          limit: usage.limit
        },
        storage: {
          bytes: usage.storage?.bytes || 0,
          objects: usage.storage?.objects || 0
        },
        bandwidth: {
          bytes: usage.bandwidth?.bytes || 0,
          requests: usage.bandwidth?.requests || 0
        },
        folders: folderStats,
        lastUpdated: new Date().toISOString()
      }
    });
    
  } catch (error) {
    console.error('Error fetching Cloudinary stats:', error);
    return next(new AppError('Failed to fetch storage statistics', 500));
  }
});

/**
 * Get recent uploads
 * @route GET /api/v1/admin/images/recent
 * @access Private (Admin only)
 */
exports.getRecentUploads = catchAsync(async (req, res, next) => {
  try {
    const { cloudinary } = require('../../config/cloudinary.config');
    const { limit = 20 } = req.query;
    
    const result = await cloudinary.search
      .expression('folder:everyfash/*')
      .sort_by([['created_at', 'desc']])
      .max_results(Math.min(parseInt(limit), 100))
      .execute();
    
    const recentImages = result.resources.map(resource => ({
      public_id: resource.public_id,
      url: resource.secure_url,
      folder: resource.folder,
      format: resource.format,
      bytes: resource.bytes,
      width: resource.width,
      height: resource.height,
      created_at: resource.created_at
    }));
    
    res.status(200).json({
      status: 'success',
      data: {
        images: recentImages,
        total: result.total_count,
        message: `Retrieved ${recentImages.length} recent uploads`
      }
    });
    
  } catch (error) {
    console.error('Error fetching recent uploads:', error);
    return next(new AppError('Failed to fetch recent uploads', 500));
  }
});
