const mongoose = require('mongoose');
require('dotenv').config();

// Import models
const Product = require('./models/product.model');
const Creator = require('./models/creator.model');

// Test data
const sampleProductData = {
  name: "Performance Test Product",
  brand: "Test Brand",
  description: "This is a test product for performance testing",
  basePrice: 99.99,
  category: new mongoose.Types.ObjectId(),
  gender: "Unisex",
  images: ["https://example.com/image1.jpg", "https://example.com/image2.jpg"],
  variations: [
    {
      color: "Red",
      size: "M",
      quantity: 10,
      price: 99.99
    },
    {
      color: "Blue", 
      size: "L",
      quantity: 15,
      price: 109.99
    }
  ],
  status: "pending"
};

async function testProductCreationPerformance() {
  try {
    // Connect to database
    await mongoose.connect(process.env.DATABASE_URL);
    console.log('Connected to database');

    // Create a test creator if needed
    let testCreator = await Creator.findOne({ email: '<EMAIL>' });
    if (!testCreator) {
      testCreator = await Creator.create({
        name: 'Test Creator',
        email: '<EMAIL>',
        password: 'testpassword123',
        role: 'creator',
        onboardingStatus: 'completed',
        verificationStatus: 'verified'
      });
    }

    sampleProductData.creator = testCreator._id;

    console.log('\n=== Testing Product Creation Performance ===');

    // Test 1: Original Product.create() method
    console.log('\n--- Test 1: Standard Product.create() ---');
    const start1 = Date.now();
    
    try {
      const product1 = await Product.create({
        ...sampleProductData,
        name: "Standard Create Test Product"
      });
      const end1 = Date.now();
      console.log(`✅ Standard create took: ${end1 - start1}ms`);
      
      // Clean up
      await Product.findByIdAndDelete(product1._id);
    } catch (error) {
      console.log(`❌ Standard create failed: ${error.message}`);
    }

    // Test 2: Optimized Product.createFast() method
    console.log('\n--- Test 2: Optimized Product.createFast() ---');
    const start2 = Date.now();
    
    try {
      const product2 = await Product.createFast({
        ...sampleProductData,
        name: "Fast Create Test Product"
      });
      const end2 = Date.now();
      console.log(`✅ Fast create took: ${end2 - start2}ms`);
      
      // Clean up
      await Product.findByIdAndDelete(product2._id);
    } catch (error) {
      console.log(`❌ Fast create failed: ${error.message}`);
    }

    // Test 3: Multiple products to see average performance
    console.log('\n--- Test 3: Batch Performance Test (5 products each) ---');
    
    // Standard method
    const standardTimes = [];
    for (let i = 0; i < 5; i++) {
      const start = Date.now();
      try {
        const product = await Product.create({
          ...sampleProductData,
          name: `Standard Batch Test Product ${i}`
        });
        const end = Date.now();
        standardTimes.push(end - start);
        await Product.findByIdAndDelete(product._id);
      } catch (error) {
        console.log(`Standard batch ${i} failed: ${error.message}`);
      }
    }

    // Fast method
    const fastTimes = [];
    for (let i = 0; i < 5; i++) {
      const start = Date.now();
      try {
        const product = await Product.createFast({
          ...sampleProductData,
          name: `Fast Batch Test Product ${i}`
        });
        const end = Date.now();
        fastTimes.push(end - start);
        await Product.findByIdAndDelete(product._id);
      } catch (error) {
        console.log(`Fast batch ${i} failed: ${error.message}`);
      }
    }

    // Calculate averages
    const avgStandard = standardTimes.reduce((a, b) => a + b, 0) / standardTimes.length;
    const avgFast = fastTimes.reduce((a, b) => a + b, 0) / fastTimes.length;
    const improvement = ((avgStandard - avgFast) / avgStandard * 100).toFixed(1);

    console.log(`\n📊 Performance Results:`);
    console.log(`Standard method average: ${avgStandard.toFixed(1)}ms`);
    console.log(`Fast method average: ${avgFast.toFixed(1)}ms`);
    console.log(`Performance improvement: ${improvement}% faster`);

    // Clean up test creator
    await Creator.findByIdAndDelete(testCreator._id);

  } catch (error) {
    console.error('Test error:', error);
  } finally {
    await mongoose.disconnect();
    console.log('\nDisconnected from database');
  }
}

// Run the test
testProductCreationPerformance();
