{"info": {"_postman_id": "product-images-update-2024", "name": "Product Images Update", "description": "Test collection for updating product images endpoint", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{creator_token}}", "type": "string"}]}, "variable": [{"key": "base_url", "value": "http://localhost:3000/api/v1", "type": "string"}, {"key": "creator_token", "value": "", "type": "string"}, {"key": "product_id", "value": "", "type": "string"}], "item": [{"name": "Update Product Images", "item": [{"name": "Replace All Images - Upload Only", "request": {"method": "PATCH", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "productImages", "type": "file", "src": [], "description": "Upload new product images (max 10 files)"}]}, "url": {"raw": "{{base_url}}/creators/products/{{product_id}}/images", "host": ["{{base_url}}"], "path": ["creators", "products", "{{product_id}}", "images"]}}, "response": []}, {"name": "Keep Some Existing + Upload New (Form Array)", "request": {"method": "PATCH", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "images[]", "value": "https://res.cloudinary.com/your-cloud/image/upload/v1234567890/existing1.jpg", "type": "text", "description": "First existing image to keep"}, {"key": "images[]", "value": "https://res.cloudinary.com/your-cloud/image/upload/v1234567890/existing2.jpg", "type": "text", "description": "Second existing image to keep"}, {"key": "productImages", "type": "file", "src": [], "description": "Upload additional new images"}]}, "url": {"raw": "{{base_url}}/creators/products/{{product_id}}/images", "host": ["{{base_url}}"], "path": ["creators", "products", "{{product_id}}", "images"]}}, "response": []}, {"name": "Keep Some Existing + Upload New (JSON String)", "request": {"method": "PATCH", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "images", "value": "[\"https://res.cloudinary.com/your-cloud/image/upload/v1234567890/existing1.jpg\", \"https://res.cloudinary.com/your-cloud/image/upload/v1234567890/existing2.jpg\"]", "type": "text", "description": "JSON string array of existing images to keep"}, {"key": "productImages", "type": "file", "src": [], "description": "Upload additional new images"}]}, "url": {"raw": "{{base_url}}/creators/products/{{product_id}}/images", "host": ["{{base_url}}"], "path": ["creators", "products", "{{product_id}}", "images"]}}, "response": []}, {"name": "Keep Only Existing Images (No Upload)", "request": {"method": "PATCH", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"images\": [\n    \"https://res.cloudinary.com/your-cloud/image/upload/v1234567890/keep1.jpg\",\n    \"https://res.cloudinary.com/your-cloud/image/upload/v1234567890/keep2.jpg\"\n  ]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/creators/products/{{product_id}}/images", "host": ["{{base_url}}"], "path": ["creators", "products", "{{product_id}}", "images"]}}, "response": []}, {"name": "Remove All Current + Upload New", "request": {"method": "PATCH", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "productImages", "type": "file", "src": [], "description": "Upload completely new images (removes all current)"}]}, "url": {"raw": "{{base_url}}/creators/products/{{product_id}}/images", "host": ["{{base_url}}"], "path": ["creators", "products", "{{product_id}}", "images"]}}, "response": []}]}, {"name": "Legacy Support (Backward Compatibility)", "item": [{"name": "Update Product Images - Direct URLs (Legacy)", "request": {"method": "PATCH", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"images\": [\n    \"https://res.cloudinary.com/your-cloud/image/upload/v1234567890/product1.jpg\",\n    \"https://res.cloudinary.com/your-cloud/image/upload/v1234567890/product2.jpg\",\n    \"https://res.cloudinary.com/your-cloud/image/upload/v1234567890/product3.jpg\"\n  ]\n}"}, "url": {"raw": "{{base_url}}/creators/products/{{product_id}}/images", "host": ["{{base_url}}"], "path": ["creators", "products", "{{product_id}}", "images"]}}, "response": []}, {"name": "Update Product Images - Mixed (Upload + URLs) (Legacy)", "request": {"method": "PATCH", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "productImages", "type": "file", "src": [], "description": "Upload new images"}, {"key": "images", "value": "[\"https://res.cloudinary.com/your-cloud/image/upload/v1234567890/existing-image.jpg\"]", "type": "text", "description": "Existing image URLs to keep"}]}, "url": {"raw": "{{base_url}}/creators/products/{{product_id}}/images", "host": ["{{base_url}}"], "path": ["creators", "products", "{{product_id}}", "images"]}}, "response": []}]}, {"name": "Error Test Cases", "item": [{"name": "Error: No Images Provided", "request": {"method": "PATCH", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/creators/products/{{product_id}}/images", "host": ["{{base_url}}"], "path": ["creators", "products", "{{product_id}}", "images"]}}, "response": []}, {"name": "Error: Invalid Images Format", "request": {"method": "PATCH", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"images\": \"not-an-array\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/creators/products/{{product_id}}/images", "host": ["{{base_url}}"], "path": ["creators", "products", "{{product_id}}", "images"]}}, "response": []}, {"name": "Error: Invalid Existing Images", "request": {"method": "PATCH", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"images\": [\n    \"https://res.cloudinary.com/your-cloud/image/upload/v1234567890/not-current-image.jpg\"\n  ]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/creators/products/{{product_id}}/images", "host": ["{{base_url}}"], "path": ["creators", "products", "{{product_id}}", "images"]}}, "response": []}, {"name": "Error: Too Many Images", "request": {"method": "PATCH", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"images\": [\n    \"https://res.cloudinary.com/your-cloud/image/upload/v1/image1.jpg\",\n    \"https://res.cloudinary.com/your-cloud/image/upload/v1/image2.jpg\",\n    \"https://res.cloudinary.com/your-cloud/image/upload/v1/image3.jpg\",\n    \"https://res.cloudinary.com/your-cloud/image/upload/v1/image4.jpg\",\n    \"https://res.cloudinary.com/your-cloud/image/upload/v1/image5.jpg\",\n    \"https://res.cloudinary.com/your-cloud/image/upload/v1/image6.jpg\",\n    \"https://res.cloudinary.com/your-cloud/image/upload/v1/image7.jpg\",\n    \"https://res.cloudinary.com/your-cloud/image/upload/v1/image8.jpg\",\n    \"https://res.cloudinary.com/your-cloud/image/upload/v1/image9.jpg\",\n    \"https://res.cloudinary.com/your-cloud/image/upload/v1/image10.jpg\",\n    \"https://res.cloudinary.com/your-cloud/image/upload/v1/image11.jpg\"\n  ]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/creators/products/{{product_id}}/images", "host": ["{{base_url}}"], "path": ["creators", "products", "{{product_id}}", "images"]}}, "response": []}]}]}