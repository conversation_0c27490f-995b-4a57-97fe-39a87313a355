const DeviceToken = require('../models/deviceToken.model');
const AppError = require('../utils/appError');
const catchAsync = require('../utils/catchAsync');

/**
 * Register a device token
 * @route POST /api/v1/devices/register
 * @access Private
 */
exports.registerDevice = catchAsync(async (req, res, next) => {
  // Validate request body
  if (!req.body.token) {
    return next(new AppError('Device token is required', 400));
  }

  if (!req.body.deviceType) {
    return next(new AppError('Device type is required', 400));
  }

  // Register device token
  const deviceToken = await DeviceToken.registerToken(
    req.user.id,
    req.body.token,
    req.body.deviceType,
    req.body.deviceInfo || {}
  );

  res.status(200).json({
    status: 'success',
    data: {
      deviceToken
    }
  });
});

/**
 * Deactivate a device token
 * @route DELETE /api/v1/devices/deactivate
 * @access Private
 */
exports.deactivateDevice = catchAsync(async (req, res, next) => {
  // Validate request body
  if (!req.body.token) {
    return next(new AppError('Device token is required', 400));
  }

  // Deactivate device token
  const success = await DeviceToken.deactivateToken(req.body.token);

  res.status(200).json({
    status: 'success',
    data: {
      success
    }
  });
});

/**
 * Get all device tokens for the authenticated user
 * @route GET /api/v1/devices
 * @access Private
 */
exports.getDevices = catchAsync(async (req, res, next) => {
  // Get active device tokens
  const deviceTokens = await DeviceToken.getActiveTokens(req.user.id);

  res.status(200).json({
    status: 'success',
    results: deviceTokens.length,
    data: {
      deviceTokens
    }
  });
});
