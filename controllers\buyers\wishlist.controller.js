const Wishlist = require('../../models/wishlist.model');
const Product = require('../../models/product.model');
const AppError = require('../../utils/appError');
const catchAsync = require('../../utils/catchAsync');
const Cart = require('../../models/cart.model');

/**
 * Get user's wishlist
 * @route GET /api/v1/wishlist
 * @access Private
 */
exports.getWishlist = catchAsync(async (req, res, next) => {
  let wishlist = await Wishlist.findOne({ user: req.user.id });

  if (!wishlist) {
    // Create a new wishlist if one doesn't exist
    wishlist = await Wishlist.create({ user: req.user.id });
  }

  // Use the virtual to get items with variation details
  const itemsWithDetails = wishlist.itemsWithVariationDetails;
  
  // Enhance items with ratings information
  const enhancedItems = await Promise.all(itemsWithDetails.map(async (item) => {
    if (item.type === 'product') {
      const product = await Product.findById(item.product._id)
        .select('ratingsAverage ratingsQuantity');
      
      return {
        ...item,
        product: {
          ...item.product,
          ratingsAverage: product?.ratingsAverage || 0,
          ratingsQuantity: product?.ratingsQuantity || 0
        }
      };
    }
    // Note: Bale items are now handled as products with type: 'bale'
    return item;
  }));

  // Group items by creator for better organization
  const itemsByCreator = {};
  enhancedItems.forEach(item => {
    if (!item.creator) return;
    
    const creatorId = item.creator._id.toString();
    if (!itemsByCreator[creatorId]) {
      itemsByCreator[creatorId] = {
        creator: {
          _id: item.creator._id,
          name: item.creator.shopInfo?.name || item.creator.businessInfo?.businessName || item.creator.name,
          logo: item.creator.shopInfo?.logo || item.creator.photo,
          verificationStatus: item.creator.verificationStatus,
          qualityScore: item.creator.metrics?.qualityScore
        },
        items: []
      };
    }
    
    itemsByCreator[creatorId].items.push(item);
  });

  // Separate products and bales for backward compatibility
  const products = enhancedItems.filter(item => item.product.type === 'product');
  const bales = enhancedItems.filter(item => item.product.type === 'bale');

  res.status(200).json({
    status: 'success',
    data: {
      wishlist: {
        _id: wishlist._id,
        user: wishlist.user,
        products,
        bales,
        items: enhancedItems, // New unified array
        itemsByCreator: Object.values(itemsByCreator), // New grouped structure
        totalItems: wishlist.products.length,
        createdAt: wishlist.createdAt,
        updatedAt: wishlist.updatedAt
      }
    }
  });
});

/**
 * Add product to wishlist
 * @route POST /api/v1/wishlist/products
 * @access Private
 */
exports.addProductToWishlist = catchAsync(async (req, res, next) => {
  // Validate request body
  if (!req.body.productId) {
    return next(new AppError('Please provide a product ID', 400));
  }

  // Check if variations array is provided
  if (!req.body.variations || !Array.isArray(req.body.variations) || req.body.variations.length === 0) {
    return next(new AppError('Please provide at least one variation', 400));
  }

  // Validate product exists
  const product = await Product.findById(req.body.productId);
  if (!product) {
    return next(new AppError('No product found with that ID', 404));
  }

  // Get user's wishlist or create a new one
  let wishlist = await Wishlist.findOne({ user: req.user.id });
  if (!wishlist) {
    wishlist = await Wishlist.create({ user: req.user.id });
  }

  // Process each variation
  const errors = [];
  const addedVariations = [];

  for (const variationData of req.body.variations) {
    // Validate variation data
    if (!variationData.variationId) {
      errors.push('Variation ID is required');
      continue;
    }

    // Find the specific variation
    const productVar = product.variations.id(variationData.variationId);
    if (!productVar) {
      errors.push(`Variation with ID ${variationData.variationId} not found`);
      continue;
    }

    // Check if variation has enough stock
    if (productVar.quantity < 1) {
      errors.push(`Variation ${variationData.variationId} is out of stock`);
      continue;
    }

    // Get quantity
    const quantity = variationData.quantity || 1;

    // Validate quantity
    if (quantity < 1) {
      errors.push(`Quantity must be at least 1 for variation ${variationData.variationId}`);
      continue;
    }

    // Find or create the product entry in the wishlist
    const existingProductIndex = wishlist.products.findIndex(
      item => {
        return (
          item.product.toString() === req.body.productId.toString() &&
          item.variationId?.toString() === variationData.variationId.toString()
        )
      }
    );

    if (existingProductIndex === -1) {
      // Add new product with variation and quantity
      wishlist.products.push({
        product: req.body.productId,
        variationId: variationData.variationId,
        quantity
      });
    } else {
      // Update quantity if product with same variation already exists
      wishlist.products[existingProductIndex].quantity = quantity;
    }

    // Calculate discount percentage if on sale
    let productDiscountPercentage = 0;
    if (productVar.salePrice && productVar.price) {
      productDiscountPercentage = Math.round(((productVar.price - productVar.salePrice) / productVar.price) * 100);
    }

    // Calculate time left if on sale
    let productTimeLeft = null;
    if (productVar.salePrice && productVar.saleEndDate && new Date() <= productVar.saleEndDate) {
      const now = new Date();
      const timeLeftMs = productVar.saleEndDate - now;

      const days = Math.floor(timeLeftMs / (1000 * 60 * 60 * 24));
      const hours = Math.floor((timeLeftMs % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
      const minutes = Math.floor((timeLeftMs % (1000 * 60 * 60)) / (1000 * 60));
      const seconds = Math.floor((timeLeftMs % (1000 * 60)) / 1000);

      productTimeLeft = {
        days,
        hours,
        minutes,
        seconds,
        formatted: `${days}d:${hours.toString().padStart(2, '0')}h:${minutes.toString().padStart(2, '0')}m:${seconds.toString().padStart(2, '0')}s`
      };
    }

    // Add to successful variations
    addedVariations.push({
      variationId: variationData.variationId,
      quantity,
      variation: {
        _id: productVar._id,
        color: productVar.color,
        size: productVar.size,
        price: productVar.price,
        salePrice: productVar.salePrice,
        quantity: productVar.quantity,
        discountPercentage: productDiscountPercentage,
        timeLeft: productTimeLeft
      }
    });
  }

  // Save wishlist if any variations were added
  if (addedVariations.length > 0) {
    await wishlist.save();
  }

  res.status(200).json({
    status: 'success',
    message: addedVariations.length > 0 
      ? 'Product variations added to wishlist' 
      : 'No variations were added to wishlist',
    data: {
      wishlist,
      addedVariations,
      errors: errors.length > 0 ? errors : undefined,
      totalItems: wishlist.products.length + wishlist.bales.length
    }
  });
});

/**
 * Add bale to wishlist (now handled as product with type: 'bale')
 * @route POST /api/v1/wishlist/bales
 * @access Private
 */
exports.addBaleToWishlist = catchAsync(async (req, res, next) => {
  // Redirect to addProductToWishlist with bale ID as product ID
  req.body.productId = req.body.baleId;
  delete req.body.baleId;

  // Validate bale exists (now stored as product with type: 'bale')
  const bale = await Product.findOne({ _id: req.body.productId, type: 'bale' });
  if (!bale) {
    return next(new AppError('No bale found with that ID', 404));
  }

  // Use the existing addProductToWishlist logic
  return exports.addProductToWishlist(req, res, next);
});

/**
 * Remove product from wishlist
 * @route DELETE /api/v1/wishlist/products/:id
 * @access Private
 */
exports.removeProductFromWishlist = catchAsync(async (req, res, next) => {
  // Get user's wishlist
  const wishlist = await Wishlist.findOne({ user: req.user.id });
  if (!wishlist) {
    return next(new AppError('Wishlist not found', 404));
  }

  // Get variationId from query params
  const variationId = req.query.variationId;
  if (!variationId) {
    return next(new AppError('Please provide a variation ID', 400));
  }

  // Check if product is in wishlist
  if (!wishlist.hasProduct(req.params.id, variationId)) {
    return next(new AppError('Product not found in wishlist', 404));
  }

  // Remove product from wishlist
  await wishlist.removeProduct(req.params.id, variationId);

  res.status(200).json({
    status: 'success',
    message: 'Product removed from wishlist',
    data: {
      wishlist
    }
  });
});

/**
 * Remove bale from wishlist
 * @route DELETE /api/v1/wishlist/bales/:id
 * @access Private
 */
exports.removeBaleFromWishlist = catchAsync(async (req, res, next) => {
  // Get user's wishlist
  const wishlist = await Wishlist.findOne({ user: req.user.id });
  if (!wishlist) {
    return next(new AppError('Wishlist not found', 404));
  }

  // Get variationId from query params
  const variationId = req.query.variationId;
  if (!variationId) {
    return next(new AppError('Please provide a variation ID', 400));
  }

  // Check if bale is in wishlist
  if (!wishlist.hasBale(req.params.id, variationId)) {
    return next(new AppError('Bale not found in wishlist', 404));
  }

  // Remove bale from wishlist
  await wishlist.removeBale(req.params.id, variationId);

  res.status(200).json({
    status: 'success',
    message: 'Bale removed from wishlist',
    data: {
      wishlist
    }
  });
});

/**
 * Update product quantity in wishlist
 * @route PATCH /api/v1/wishlist/products/:id
 * @access Private
 */
exports.updateProductQuantity = catchAsync(async (req, res, next) => {
  // Validate request body
  if (!req.body.quantity) {
    return next(new AppError('Please provide a quantity', 400));
  }

  const quantity = parseInt(req.body.quantity);
  if (quantity < 1) {
    return next(new AppError('Quantity must be at least 1', 400));
  }

  // Get variationId from request body
  const variationId = req.body.variationId;
  if (!variationId) {
    return next(new AppError('Please provide a variation ID', 400));
  }

  // Get user's wishlist
  const wishlist = await Wishlist.findOne({ user: req.user.id });
  if (!wishlist) {
    return next(new AppError('Wishlist not found', 404));
  }

  // Check if product is in wishlist
  if (!wishlist.hasProduct(req.params.id, variationId)) {
    return next(new AppError('Product not found in wishlist', 404));
  }

  // Update product quantity
  await wishlist.updateProductQuantity(req.params.id, variationId, quantity);

  res.status(200).json({
    status: 'success',
    message: 'Product quantity updated',
    data: {
      wishlist
    }
  });
});

/**
 * Update bale quantity in wishlist
 * @route PATCH /api/v1/wishlist/bales/:id
 * @access Private
 */
exports.updateBaleQuantity = catchAsync(async (req, res, next) => {
  // Validate request body
  if (!req.body.quantity) {
    return next(new AppError('Please provide a quantity', 400));
  }

  const quantity = parseInt(req.body.quantity);
  if (quantity < 1) {
    return next(new AppError('Quantity must be at least 1', 400));
  }

  // Get variationId from request body
  const variationId = req.body.variationId;
  if (!variationId) {
    return next(new AppError('Please provide a variation ID', 400));
  }

  // Get user's wishlist
  const wishlist = await Wishlist.findOne({ user: req.user.id });
  if (!wishlist) {
    return next(new AppError('Wishlist not found', 404));
  }

  // Check if bale is in wishlist
  if (!wishlist.hasBale(req.params.id, variationId)) {
    return next(new AppError('Bale not found in wishlist', 404));
  }

  // Update bale quantity
  await wishlist.updateBaleQuantity(req.params.id, variationId, quantity);

  res.status(200).json({
    status: 'success',
    message: 'Bale quantity updated',
    data: {
      wishlist
    }
  });
});

/**
 * Clear wishlist
 * @route DELETE /api/v1/wishlist
 * @access Private
 */
exports.clearWishlist = catchAsync(async (req, res, next) => {
  // Get user's wishlist
  const wishlist = await Wishlist.findOne({ user: req.user.id });
  if (!wishlist) {
    return next(new AppError('Wishlist not found', 404));
  }

  // Clear wishlist
  wishlist.products = [];
  wishlist.bales = [];
  await wishlist.save();

  res.status(200).json({
    status: 'success',
    message: 'Wishlist cleared',
    data: {
      wishlist
    }
  });
});

/**
 * Move item to cart
 * @route POST /api/v1/wishlist/move-to-cart
 * @access Private
 */
exports.moveToCart = catchAsync(async (req, res, next) => {
  // Validate request body
  if (!req.body.id || !req.body.type || !req.body.variationId) {
    return next(new AppError('Please provide item ID, type, and variation ID', 400));
  }

  // Validate type
  if (!['product', 'bale'].includes(req.body.type)) {
    return next(new AppError('Type must be either product or bale', 400));
  }

  // Get user's wishlist
  const wishlist = await Wishlist.findOne({ user: req.user.id });
  if (!wishlist) {
    return next(new AppError('Wishlist not found', 404));
  }

  // Check if item exists in wishlist
  let itemExists = false;
  let itemData = null;

  if (req.body.type === 'product') {
    itemExists = wishlist.hasProduct(req.body.id, req.body.variationId);

    if (itemExists) {
      // Get product item from wishlist
      const productItem = wishlist.products.find(
        item =>
          item.product._id.toString() === req.body.id.toString() &&
          item.variationId?.toString() === req.body.variationId.toString()
      );

      if (productItem) {
        itemData = {
          type: 'product',
          id: productItem.product._id,
          variationId: productItem.variationId,
          quantity: productItem.quantity
        };

        // Remove from wishlist
        await wishlist.removeProduct(req.body.id, req.body.variationId);
      }
    }
  } else if (req.body.type === 'bale') {
    itemExists = wishlist.hasBale(req.body.id, req.body.variationId);

    if (itemExists) {
      // Get bale item from wishlist
      const baleItem = wishlist.bales.find(
        item =>
          item.bale._id.toString() === req.body.id.toString() &&
          item.variationId?.toString() === req.body.variationId.toString()
      );

      if (baleItem) {
        itemData = {
          type: 'bale',
          id: baleItem.bale._id,
          variationId: baleItem.variationId,
          quantity: baleItem.quantity
        };

        // Remove from wishlist
        await wishlist.removeBale(req.body.id, req.body.variationId);
      }
    }
  }

  if (!itemExists || !itemData) {
    return next(new AppError('Item not found in wishlist', 404));
  }

  // Add item to cart
  let cart = await Cart.findOne({ user: req.user.id });
  if (!cart) {
    cart = await Cart.create({ user: req.user.id });
  }

  // Process based on item type
  if (itemData.type === 'product') {
    const product = await Product.findById(itemData.id);
    if (!product) {
      return next(new AppError('Product not found', 404));
    }

    // Find the specific variation
    const variation = product.variations.id(itemData.variationId);
    if (!variation) {
      return next(new AppError('Product variation not found', 404));
    }

    // Calculate price
    const price = variation.onSale ? variation.salePrice : variation.price;

    // Check if product is already in cart
    const existingItemIndex = cart.items.findIndex(
      item =>
        item.product &&
        item.product.toString() === itemData.id.toString() &&
        item.variationId === itemData.variationId
    );

    if (existingItemIndex > -1) {
      // Update quantity if product is already in cart
      cart.items[existingItemIndex].quantity = itemData.quantity;
    } else {
      // Add new item to cart
      cart.items.push({
        product: itemData.id,
        type: 'product',
        variationId: itemData.variationId,
        quantity: itemData.quantity,
        price
      });
    }
  } else if (itemData.type === 'bale') {
    const bale = await Bale.findById(itemData.id);
    if (!bale) {
      return next(new AppError('Bale not found', 404));
    }

    // Find the specific variation
    const variation = bale.variations.id(itemData.variationId);
    if (!variation) {
      return next(new AppError('Bale variation not found', 404));
    }

    // Calculate price
    const price = variation.onSale ? variation.salePrice : variation.price;

    // Check if bale is already in cart
    const existingItemIndex = cart.items.findIndex(
      item =>
        item.bale &&
        item.bale.toString() === itemData.id.toString() &&
        item.variationId === itemData.variationId
    );

    if (existingItemIndex > -1) {
      // Update quantity if bale is already in cart
      cart.items[existingItemIndex].quantity = itemData.quantity;
    } else {
      // Add new item to cart
      cart.items.push({
        bale: itemData.id,
        type: 'bale',
        variationId: itemData.variationId,
        quantity: itemData.quantity,
        price
      });
    }
  }

  // Calculate total
  cart.calculateTotal();

  // Save cart
  await cart.save();

  res.status(200).json({
    status: 'success',
    message: 'Item moved to cart',
    data: {
      wishlist,
      cart
    }
  });
});

/**
 * Get wishlist item count
 * @route GET /api/v1/wishlist/count
 * @access Private
 */
exports.getWishlistCount = catchAsync(async (req, res, next) => {
  let wishlist = await Wishlist.findOne({ user: req.user.id });

  if (!wishlist) {
    // Create a new wishlist if one doesn't exist
    wishlist = await Wishlist.create({ user: req.user.id });
  }

  const count = wishlist.products.length + wishlist.bales.length;

  res.status(200).json({
    status: 'success',
    data: {
      count
    }
  });
});

/**
 * Move all wishlist items to cart
 * @route POST /api/v1/wishlist/move-all-to-cart
 * @access Private
 */
exports.moveAllToCart = catchAsync(async (req, res, next) => {
  // Get user's wishlist
  const wishlist = await Wishlist.findOne({ user: req.user.id });
  if (!wishlist) {
    return next(new AppError('Wishlist not found', 404));
  }

  // Check if wishlist is empty
  if (wishlist.products.length === 0 && wishlist.bales.length === 0) {
    return next(new AppError('Wishlist is empty', 400));
  }

  // Get or create user's cart

  let cart = await Cart.findOne({ user: req.user.id });
  if (!cart) {
    cart = await Cart.create({ user: req.user.id });
  }

  // Process products
  for (const item of wishlist.products) {
    const product = await Product.findById(item.product);
    if (!product) continue;

    // Find the specific variation
    const variation = product.variations.find(
      v => v.color === item.color && v.size === item.size
    );

    if (!variation) continue;

    // Check if variation has enough stock
    if (variation.quantity < item.quantity) continue;

    // Calculate price
    const price = variation.onSale ? variation.salePrice : variation.price;

    // Check if product is already in cart
    const existingItemIndex = cart.items.findIndex(
      cartItem =>
        cartItem.product &&
        cartItem.product.toString() === item.product.toString() &&
        cartItem.color === item.color &&
        cartItem.size === item.size
    );

    if (existingItemIndex > -1) {
      // Update quantity if product is already in cart
      cart.items[existingItemIndex].quantity = item.quantity;
    } else {
      // Add new item to cart
      cart.items.push({
        product: item.product,
        type: 'product',
        color: item.color,
        size: item.size,
        quantity: item.quantity,
        price
      });
    }
  }

  // Process bales
  for (const item of wishlist.bales) {
    const bale = await Bale.findById(item.bale);
    if (!bale) continue;

    // Find the specific variation
    const variation = bale.variations.find(v => v.size === item.size);
    if (!variation) continue;

    // Check if variation has enough stock
    if (variation.quantity < item.quantity) continue;

    // Calculate price
    const price = variation.onSale ? variation.salePrice : variation.price;

    // Check if bale is already in cart
    const existingItemIndex = cart.items.findIndex(
      cartItem =>
        cartItem.bale &&
        cartItem.bale.toString() === item.bale.toString() &&
        cartItem.size === item.size
    );

    if (existingItemIndex > -1) {
      // Update quantity if bale is already in cart
      cart.items[existingItemIndex].quantity = item.quantity;
    } else {
      // Add new item to cart
      cart.items.push({
        bale: item.bale,
        type: 'bale',
        size: item.size,
        quantity: item.quantity,
        price
      });
    }
  }

  // Calculate total
  cart.calculateTotal();

  // Save cart
  await cart.save();

  // Clear wishlist
  wishlist.products = [];
  wishlist.bales = [];
  await wishlist.save();

  res.status(200).json({
    status: 'success',
    message: 'All items moved to cart',
    data: {
      wishlist,
      cart
    }
  });
});



















