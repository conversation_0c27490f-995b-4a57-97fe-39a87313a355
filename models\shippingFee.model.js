const mongoose = require('mongoose');

const shippingFeeSchema = new mongoose.Schema(
  {
    country: {
      type: String,
      required: true,
      trim: true
    },
    region: {
      type: String,
      trim: true
    },
    city: {
      type: String,
      trim: true
    },
    // Standard delivery fee
    standardFee: {
      type: Number,
      required: true,
      min: 0
    },
    // Express delivery fee (can be calculated if not provided)
    expressFee: {
      type: Number,
      min: 0
    },
    // Additional fee based on weight ranges
    weightRanges: [
      {
        minWeight: {
          type: Number,
          required: true,
          min: 0
        },
        maxWeight: {
          type: Number,
          required: true,
          min: 0
        },
        additionalFee: {
          type: Number,
          required: true,
          min: 0
        }
      }
    ],
    // Active status
    isActive: {
      type: Boolean,
      default: true
    },
    // Audit fields
    createdBy: {
      type: mongoose.Schema.ObjectId,
      ref: 'User'
    },
    updatedBy: {
      type: mongoose.Schema.ObjectId,
      ref: 'User'
    }
  },
  {
    timestamps: true
  }
);

// Compound index for efficient lookups
shippingFeeSchema.index({ country: 1, region: 1, city: 1 });

// Pre-save middleware to set express fee if not provided
shippingFeeSchema.pre('save', function(next) {
  if (!this.expressFee) {
    this.expressFee = this.standardFee * 2;
  }
  next();
});

// Static method to find shipping fee for a location
shippingFeeSchema.statics.findFeeForLocation = async function(country, region = null, city = null) {
  // Try to find the most specific match first
  if (city && region) {
    const cityMatch = await this.findOne({
      country,
      region,
      city,
      isActive: true
    });
    
    if (cityMatch) return cityMatch;
  }
  
  // Try to find a region match
  if (region) {
    const regionMatch = await this.findOne({
      country,
      region,
      city: { $exists: false },
      isActive: true
    });
    
    if (regionMatch) return regionMatch;
  }
  
  // Fall back to country-level fee
  const countryMatch = await this.findOne({
    country,
    region: { $exists: false },
    city: { $exists: false },
    isActive: true
  });
  
  if (countryMatch) return countryMatch;
  
  // If no match found, return default fee for Ghana
  return await this.findOne({
    country: 'Ghana',
    region: { $exists: false },
    city: { $exists: false },
    isActive: true
  }) || { standardFee: 30, expressFee: 60, weightRanges: [] };
};

// Method to calculate shipping fee based on weight and delivery type
shippingFeeSchema.methods.calculateFee = function(weight = 0, isExpress = false) {
  // Start with base fee
  let fee = isExpress ? this.expressFee : this.standardFee;
  
  // Add weight-based fee if applicable
  if (weight > 0 && this.weightRanges && this.weightRanges.length > 0) {
    // Find applicable weight range
    const weightRange = this.weightRanges.find(
      range => weight >= range.minWeight && weight <= range.maxWeight
    );
    
    if (weightRange) {
      fee += weightRange.additionalFee;
    } else {
      // If weight exceeds all ranges, use the highest range
      const highestRange = [...this.weightRanges].sort((a, b) => b.maxWeight - a.maxWeight)[0];
      if (highestRange && weight > highestRange.maxWeight) {
        fee += highestRange.additionalFee;
      }
    }
  }
  
  return parseFloat(fee.toFixed(2));
};

const ShippingFee = mongoose.model('ShippingFee', shippingFeeSchema);

module.exports = ShippingFee;
