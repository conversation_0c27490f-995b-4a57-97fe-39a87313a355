const ShippingFee = require('../../models/shippingFee.model');
const catchAsync = require('../../utils/catchAsync');
const AppError = require('../../utils/appError');

/**
 * Get all shipping fees
 * @route GET /api/v1/admin/fees/shipping
 * @access Private (Admin only)
 */
exports.getAllShippingFees = catchAsync(async (req, res, next) => {
  // Build query
  const query = {};
  
  // Filter by country if provided
  if (req.query.country) {
    query.country = req.query.country;
  }
  
  // Filter by region if provided
  if (req.query.region) {
    query.region = req.query.region;
  }
  
  // Filter by city if provided
  if (req.query.city) {
    query.city = req.query.city;
  }
  
  // Filter by active status if provided
  if (req.query.isActive !== undefined) {
    query.isActive = req.query.isActive === 'true';
  }
  
  const fees = await ShippingFee.find(query).sort({ country: 1, region: 1, city: 1 });
  
  res.status(200).json({
    status: 'success',
    results: fees.length,
    data: {
      shippingFees: fees
    }
  });
});

/**
 * Create a new shipping fee
 * @route POST /api/v1/admin/fees/shipping
 * @access Private (Admin only)
 */
exports.createShippingFee = catchAsync(async (req, res, next) => {
  // Set current user as creator
  req.body.createdBy = req.user.id;
  
  // Check if a fee already exists for this location
  const existingFee = await ShippingFee.findOne({
    country: req.body.country,
    region: req.body.region || { $exists: false },
    city: req.body.city || { $exists: false }
  });
  
  if (existingFee) {
    return next(new AppError('A shipping fee already exists for this location', 400));
  }
  
  const newFee = await ShippingFee.create(req.body);
  
  res.status(201).json({
    status: 'success',
    data: {
      shippingFee: newFee
    }
  });
});

/**
 * Get a specific shipping fee
 * @route GET /api/v1/admin/fees/shipping/:id
 * @access Private (Admin only)
 */
exports.getShippingFee = catchAsync(async (req, res, next) => {
  const fee = await ShippingFee.findById(req.params.id);
  
  if (!fee) {
    return next(new AppError('No shipping fee found with that ID', 404));
  }
  
  res.status(200).json({
    status: 'success',
    data: {
      shippingFee: fee
    }
  });
});

/**
 * Update a shipping fee
 * @route PATCH /api/v1/admin/fees/shipping/:id
 * @access Private (Admin only)
 */
exports.updateShippingFee = catchAsync(async (req, res, next) => {
  // Set current user as updater
  req.body.updatedBy = req.user.id;
  
  const fee = await ShippingFee.findByIdAndUpdate(req.params.id, req.body, {
    new: true,
    runValidators: true
  });
  
  if (!fee) {
    return next(new AppError('No shipping fee found with that ID', 404));
  }
  
  res.status(200).json({
    status: 'success',
    data: {
      shippingFee: fee
    }
  });
});

/**
 * Delete a shipping fee
 * @route DELETE /api/v1/admin/fees/shipping/:id
 * @access Private (Admin only)
 */
exports.deleteShippingFee = catchAsync(async (req, res, next) => {
  const fee = await ShippingFee.findByIdAndDelete(req.params.id);
  
  if (!fee) {
    return next(new AppError('No shipping fee found with that ID', 404));
  }
  
  res.status(204).json({
    status: 'success',
    data: null
  });
});

/**
 * Calculate shipping fee for a location
 * @route POST /api/v1/admin/fees/shipping/calculate
 * @access Private (Admin only)
 */
exports.calculateShippingFee = catchAsync(async (req, res, next) => {
  const { country, region, city, weight, isExpress } = req.body;
  
  if (!country) {
    return next(new AppError('Country is required', 400));
  }
  
  const feeConfig = await ShippingFee.findFeeForLocation(country, region, city);
  
  if (!feeConfig) {
    return next(new AppError('No shipping fee configuration found for this location', 404));
  }
  
  const calculatedFee = feeConfig.calculateFee(weight, isExpress);
  
  res.status(200).json({
    status: 'success',
    data: {
      location: {
        country,
        region: region || 'All regions',
        city: city || 'All cities'
      },
      weight: weight || 0,
      deliveryType: isExpress ? 'Express' : 'Standard',
      baseFee: isExpress ? feeConfig.expressFee : feeConfig.standardFee,
      weightBasedFee: calculatedFee - (isExpress ? feeConfig.expressFee : feeConfig.standardFee),
      totalFee: calculatedFee
    }
  });
});
