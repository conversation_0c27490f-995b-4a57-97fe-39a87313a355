# Creator Product Management - Postman Test Collections

This directory contains comprehensive Postman collections for testing all the creator product management endpoints.

## 📁 Files Overview

### Collections
1. **`creator-product-management.json`** - Main collection with all endpoints
2. **`creator-product-error-tests.json`** - Error scenarios and edge cases
3. **`creator-product-test-scripts.json`** - Automated tests with validation scripts
4. **`public-categories.json`** - Public category endpoints (no auth required)
5. **`public-categories-error-tests.json`** - Category error scenarios

### Environment
6. **`creator-product-environment.json`** - Environment variables for testing

## 🚀 Quick Setup

### 1. Import Collections
1. Open Postman
2. Click "Import" button
3. Select all JSON files from this directory
4. Import the environment file as well

### 2. Setup Environment
1. Select "Creator Product Test Environment" from the environment dropdown
2. Set the following variables:
   - `base_url`: Your API base URL (default: `http://localhost:3000/api/v1`)
   - `creator_email`: Test creator email
   - `creator_password`: Test creator password
   - `category_id`: Valid category ID for testing

### 3. Authentication
1. Run the "Creator Login" request from any collection
2. The token will be automatically stored in environment variables

## 📋 Test Collections Details

### Main Collection (`creator-product-management.json`)

#### Product Information Updates
- **Update Basic Info** - Test updating name, brand, description, etc. (Uses form-data for image uploads)
- **Update Specifications** - Test updating material, fit type, etc. (Uses JSON)
- **Update SEO** - Test updating meta title, description, keywords (Uses JSON)

#### Product Variations
- **Get All Variations** - Retrieve all product variations
- **Add New Variation** - Add color/size combinations (Uses form-data for image uploads)
- **Update Variation** - Modify existing variations (Uses form-data for image uploads)
- **Delete Variation** - Remove variations

#### Product Analytics
- **Get Product Reviews** - Fetch reviews with pagination and filtering
- **Get Product Sales History** - Sales data with period filtering
- **Get Product Statistics** - Enhanced stats with period support

#### Product Promotions
- **Get Product Promotions** - View active promotions
- **Join Admin Promotion** - Join existing admin promotions
- **Update Promotion Participation** - Modify participation details
- **Leave Promotion** - Remove product from promotion

### Public Categories Collection (`public-categories.json`)

#### Category Endpoints (No Authentication Required)
- **Get All Categories** - Retrieve all categories with hierarchical structure
- **Get All Categories - With Products** - Filter categories that have products
- **Get All Categories - Featured Only** - Get only featured categories
- **Get Featured Categories** - Dedicated featured categories endpoint with counts
- **Get Category Tree** - Flat structure with breadcrumb paths
- **Get Category by ID** - Retrieve specific category with subcategories and counts
- **Get Category by Slug** - Retrieve category using slug identifier
- **Search Categories** - Search categories by name or description

### Error Tests Collection (`creator-product-error-tests.json`)

#### Validation Errors
- Invalid category IDs
- Negative prices and quantities
- Invalid enum values
- Duplicate variations

#### Authorization Errors
- Accessing other creator's products
- Requests without authentication
- Invalid tokens

#### Business Logic Errors
- Joining expired promotions
- Deleting last variation
- Invalid sale price scenarios
- Updating active product restrictions

### Automated Tests Collection (`creator-product-test-scripts.json`)

Contains the same endpoints but with:
- **Pre-request scripts** for setup
- **Test scripts** for validation
- **Automatic variable extraction**
- **Response validation**

## 🧪 Test Scenarios

### Basic Workflow
1. **Login** → Get authentication token
2. **Create Product** → Setup test product
3. **Update Basic Info** → Test field updates
4. **Add Variations** → Test variation management
5. **Join Promotion** → Test promotion participation
6. **Get Analytics** → Test reporting endpoints

### Error Testing
1. **Invalid Data** → Test validation
2. **Unauthorized Access** → Test security
3. **Business Rules** → Test constraints

## 📊 Expected Responses

### Success Responses
```json
{
  "status": "success",
  "data": {
    "product": { ... }
  }
}
```

### Error Responses
```json
{
  "status": "error",
  "message": "Error description",
  "statusCode": 400
}
```

## 🔧 Environment Variables

### Required Variables
- `base_url` - API base URL
- `creator_token` - Authentication token (auto-set)
- `creator_email` - Test creator email
- `creator_password` - Test creator password

### Auto-Generated Variables
- `product_id` - Created product ID
- `variation_id` - Variation ID for testing
- `promotion_id` - Promotion ID for testing
- `category_id` - Valid category ID

### Test Data Variables
- `invalid_product_id` - Non-existent product ID
- `active_product_id` - Product with active status
- `draft_product_id` - Product with draft status

## 📝 Test Data Requirements

Before running tests, ensure you have:
1. **Valid creator account** with email/password
2. **At least one category** in the system
3. **Admin promotion** (for promotion tests)
4. **Products in different statuses** (draft, active, etc.)

## 🎯 Test Coverage

### Endpoints Covered
- ✅ Basic info updates (PATCH `/basic-info`)
- ✅ Specifications updates (PATCH `/specifications`)
- ✅ SEO updates (PATCH `/seo`)
- ✅ Variation CRUD (GET/POST/PATCH/DELETE `/variations`)
- ✅ Product reviews (GET `/reviews`)
- ✅ Sales history (GET `/sales`)
- ✅ Product statistics (GET `/stats`)
- ✅ Promotion management (GET/POST/PATCH/DELETE `/promotions`)
- ✅ Public categories (GET `/categories` - all endpoints)
- ✅ Category search (GET `/categories/search`)
- ✅ Featured categories (GET `/categories/featured`)
- ✅ Category tree (GET `/categories/tree`)

### Validation Covered
- ✅ Authentication & Authorization
- ✅ Input validation
- ✅ Business rule enforcement
- ✅ Error handling
- ✅ Response format validation

## 🚨 Common Issues

### Authentication
- Ensure token is valid and not expired
- Check creator account permissions

### Product Status
- Some operations restricted for active products
- Check product status before testing

### Promotion Testing
- Promotions must exist before joining
- Check promotion dates and criteria

### Form-Data vs JSON
- **Basic Info Updates** use `form-data` (supports image uploads)
- **Variation Add/Update** use `form-data` (supports image uploads)
- **Specifications/SEO** use `application/json`
- **Analytics/Promotions** use `application/json`

## 📞 Support

For issues with the API endpoints or test collections, refer to:
1. API documentation
2. Controller implementation
3. Error logs in the application

Happy Testing! 🎉
