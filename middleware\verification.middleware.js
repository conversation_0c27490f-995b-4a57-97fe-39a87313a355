const { Creator } = require('../models/user.model');
const AppError = require('../utils/appError');
const catchAsync = require('../utils/catchAsync');

/**
 * Combined middleware to check both verification and onboarding status
 * This middleware should be used after the auth.protect middleware to ensure req.user is available
 */
exports.requireVerifiedAndOnboarded = catchAsync(async (req, res, next) => {
  // Skip verification check for certain onboarding endpoints
  const exemptPaths = [
    '/onboarding',
    '/onboarding/',
    '/onboarding/status',
    '/onboarding/verification-status',
    '/onboarding/business-info',
    '/onboarding/payment-info',
    '/onboarding/shop-info',
    '/onboarding/shipping-info'
  ];

  // Check if the current path should be exempt from verification
  const currentPath = req.originalUrl.replace('/api/v1/creators', '');
  const isExempt = exemptPaths.some(path => currentPath.startsWith(path));

  if (isExempt) {
    return next();
  }

  // Get the creator from the database to ensure we have the latest status
  const creator = await C<PERSON>.findById(req.user.id);

  if (!creator) {
    return next(new AppError('Creator not found', 404));
  }

  // Check if the creator has completed onboarding
  if (creator.onboardingStatus !== 'completed') {
    return next(new AppError('You must complete the onboarding process before you can perform this action. Please complete all required steps.', 403));
  }

  // Check if the creator is verified
  if (creator.verificationStatus !== 'verified') {
    return next(new AppError('Your account must be verified before you can perform this action. Please complete the verification process.', 403));
  }

  // If the creator is verified and has completed onboarding, proceed to the next middleware
  next();
});
