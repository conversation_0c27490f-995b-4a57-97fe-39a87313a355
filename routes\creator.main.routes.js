const express = require('express');
const authMiddleware = require('../middleware/auth.middleware');
const verificationMiddleware = require('../middleware/verification.middleware');

// Import creator sub-routes
const profileRoutes = require('./creators/profile.routes');
const onboardingRoutes = require('./creators/onboarding.routes');
const productRoutes = require('./creators/product.routes');
const orderRoutes = require('./creators/order.routes');
const payoutRoutes = require('./creators/payout.routes');
const walletRoutes = require('./creators/wallet.routes');

const router = express.Router();

// Protect all routes
router.use(authMiddleware.protect);
router.use(authMiddleware.restrictTo('creator'));

// Routes that don't require verification (onboarding and profile)
router.use('/onboarding', onboardingRoutes); //✅
router.use('/profile', profileRoutes); //Using Onboarding Endpoints Instead

// Apply verification middleware for routes that require verified and onboarded creators
router.use(verificationMiddleware.requireVerifiedAndOnboarded);

// Routes that require verification and onboarding completion
router.use('/products', productRoutes); //✅ (now handles both products and bales)
router.use('/orders', orderRoutes);
router.use('/payouts', payoutRoutes);
router.use('/wallet', walletRoutes);

module.exports = router;
