{"info": {"_postman_id": "b1c2d3e4-f5g6-7890-abcd-ef1234567890", "name": "Flashy Admin - Buyer Routes", "description": "A collection for testing the admin buyer routes of the Flashy API", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Get All Buyers", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{ADMIN_AUTH_TOKEN}}", "type": "text"}], "url": {"raw": "{{BASE_URL}}/api/v1/admin/buyers?page=1&limit=10&sort=-createdAt", "host": ["{{BASE_URL}}"], "path": ["api", "v1", "admin", "buyers"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}, {"key": "sort", "value": "-createdAt"}, {"key": "search", "value": "john", "disabled": true}, {"key": "active", "value": "true", "disabled": true}]}, "description": "Get all buyers with pagination, sorting, and filtering options"}, "response": []}, {"name": "Get Buyer by ID", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{ADMIN_AUTH_TOKEN}}", "type": "text"}], "url": {"raw": "{{BASE_URL}}/api/v1/admin/buyers/{{buyerId}}?ordersPage=1&ordersLimit=5&wishlistPage=1&wishlistLimit=5&cartPage=1&cartLimit=5", "host": ["{{BASE_URL}}"], "path": ["api", "v1", "admin", "buyers", "{{buyerId}}"], "query": [{"key": "ordersPage", "value": "1"}, {"key": "ordersLimit", "value": "5"}, {"key": "wishlistPage", "value": "1"}, {"key": "wishlistLimit", "value": "5"}, {"key": "cartPage", "value": "1"}, {"key": "cartLimit", "value": "5"}]}, "description": "Get detailed information about a specific buyer including orders, wishlist, cart, and statistics"}, "response": []}, {"name": "Update Buyer Details", "request": {"method": "PATCH", "header": [{"key": "Authorization", "value": "Bearer {{ADMIN_AUTH_TOKEN}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"Updated Buyer Name\",\n    \"email\": \"<EMAIL>\",\n    \"phone\": \"+233123456789\",\n    \"gender\": \"female\",\n    \"dateOfBirth\": \"1990-01-01\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BASE_URL}}/api/v1/admin/buyers/{{buyerId}}/details", "host": ["{{BASE_URL}}"], "path": ["api", "v1", "admin", "buyers", "{{buyerId}}", "details"]}, "description": "Update a buyer's basic details"}, "response": []}, {"name": "Update Buyer Status", "request": {"method": "PATCH", "header": [{"key": "Authorization", "value": "Bearer {{ADMIN_AUTH_TOKEN}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"active\": true\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BASE_URL}}/api/v1/admin/buyers/{{buyerId}}/status", "host": ["{{BASE_URL}}"], "path": ["api", "v1", "admin", "buyers", "{{buyerId}}", "status"]}, "description": "Update a buyer's active status (activate or deactivate account)"}, "response": []}, {"name": "Reset Buyer Password", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{ADMIN_AUTH_TOKEN}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BASE_URL}}/api/v1/admin/buyers/{{buyerId}}/reset-password", "host": ["{{BASE_URL}}"], "path": ["api", "v1", "admin", "buyers", "{{buyerId}}", "reset-password"]}, "description": "Reset a buyer's password and send a temporary password via email"}, "response": []}, {"name": "Delete Buyer", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{ADMIN_AUTH_TOKEN}}", "type": "text"}], "url": {"raw": "{{BASE_URL}}/api/v1/admin/buyers/{{buyerId}}", "host": ["{{BASE_URL}}"], "path": ["api", "v1", "admin", "buyers", "{{buyerId}}"]}, "description": "Delete a buyer (only if they have no orders, reviews, or wishlist items)"}, "response": []}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test(\"Status code is successful\", function () {", "    pm.expect(pm.response.code).to.be.oneOf([200, 201, 204]);", "});", "", "if (pm.response.code !== 204) {", "    pm.test(\"Response has success status\", function () {", "        var jsonData = pm.response.json();", "        pm.expect(jsonData.status).to.eql(\"success\");", "    });", "}", "", "pm.test(\"Response time is less than 1000ms\", function () {", "    pm.expect(pm.response.responseTime).to.be.below(1000);", "});"]}}], "variable": [{"key": "BASE_URL", "value": "http://localhost:5000", "type": "string"}, {"key": "ADMIN_AUTH_TOKEN", "value": "your-admin-auth-token-here", "type": "string"}, {"key": "buyerId", "value": "buyer-id-here", "type": "string"}]}