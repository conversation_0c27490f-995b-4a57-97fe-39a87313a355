const express = require('express');
const promotionController = require('../../controllers/admin/promotion.controller');
const authMiddleware = require('../../middleware/auth.middleware');

const router = express.Router();

// Protect all routes
router.use(authMiddleware.protect);
router.use(authMiddleware.restrictTo('admin'));

// Promotion routes
router.get('/stats', promotionController.getPromotionStats);

router.route('/')
  .get(promotionController.getAllPromotions)
  .post(promotionController.createPromotion);

router.route('/:id')
  .get(promotionController.getPromotion)
  .patch(promotionController.updatePromotion)
  .delete(promotionController.deletePromotion);

router.patch('/:id/participants/:participantId', promotionController.updateParticipantStatus);

module.exports = router;
