const Buyer = require('../../models/buyer.model');
const Creator = require('../../models/creator.model');
const NotificationService = require('../../services/notification.service');
const AppError = require('../../utils/appError');
const catchAsync = require('../../utils/catchAsync');

/**
 * Follow a creator
 * @route POST /api/v1/buyers/follow/:creatorId
 * @access Private (Buyer only)
 */
exports.followCreator = catchAsync(async (req, res, next) => {
  // Check if creator exists
  const creator = await Creator.findById(req.params.creatorId);
  if (!creator) {
    return next(new AppError('No creator found with that ID', 404));
  }

  // Get buyer
  const buyer = await Buyer.findById(req.user.id);
  if (!buyer) {
    return next(new AppError('Buyer not found', 404));
  }

  // Check if already following
  const isFollowing = buyer.followedCreators.some(
    id => id.toString() === req.params.creatorId
  );

  if (isFollowing) {
    return next(new AppError('You are already following this creator', 400));
  }

  // Follow creator
  await buyer.followCreator(req.params.creatorId);

  // Send notification to creator
  try {
    await NotificationService.createNotification({
      recipient: req.params.creatorId,
      sender: req.user.id,
      type: 'new_follower',
      title: 'New Follower',
      message: `${req.user.name} is now following your shop`,
      priority: 'medium',
      data: {
        buyer: req.user.id,
        url: `/dashboard/followers`
      }
    });
  } catch (error) {
    console.error('Error sending new follower notification:', error);
  }

  res.status(200).json({
    status: 'success',
    message: 'You are now following this creator'
  });
});

/**
 * Unfollow a creator
 * @route DELETE /api/v1/buyers/follow/:creatorId
 * @access Private (Buyer only)
 */
exports.unfollowCreator = catchAsync(async (req, res, next) => {
  // Check if creator exists
  const creator = await Creator.findById(req.params.creatorId);
  if (!creator) {
    return next(new AppError('No creator found with that ID', 404));
  }

  // Get buyer
  const buyer = await Buyer.findById(req.user.id);
  if (!buyer) {
    return next(new AppError('Buyer not found', 404));
  }

  // Check if following
  const isFollowing = buyer.followedCreators.some(
    id => id.toString() === req.params.creatorId
  );

  if (!isFollowing) {
    return next(new AppError('You are not following this creator', 400));
  }

  // Unfollow creator
  await buyer.unfollowCreator(req.params.creatorId);

  res.status(200).json({
    status: 'success',
    message: 'You have unfollowed this creator'
  });
});

/**
 * Get followed creators
 * @route GET /api/v1/buyers/follow
 * @access Private (Buyer only)
 */
exports.getFollowedCreators = catchAsync(async (req, res, next) => {
  // Get buyer with populated followedCreators
  const buyer = await Buyer.findById(req.user.id)
    .populate({
      path: 'followedCreators',
      select: 'name photo shopInfo businessInfo.businessName verificationStatus'
    });

  if (!buyer) {
    return next(new AppError('Buyer not found', 404));
  }

  res.status(200).json({
    status: 'success',
    results: buyer.followedCreators.length,
    data: {
      followedCreators: buyer.followedCreators
    }
  });
});

/**
 * Check if following a creator
 * @route GET /api/v1/buyers/follow/:creatorId/check
 * @access Private (Buyer only)
 */
exports.checkFollowing = catchAsync(async (req, res, next) => {
  // Get buyer
  const buyer = await Buyer.findById(req.user.id);
  if (!buyer) {
    return next(new AppError('Buyer not found', 404));
  }

  // Check if following
  const isFollowing = buyer.followedCreators.some(
    id => id.toString() === req.params.creatorId
  );

  res.status(200).json({
    status: 'success',
    data: {
      isFollowing
    }
  });
});
