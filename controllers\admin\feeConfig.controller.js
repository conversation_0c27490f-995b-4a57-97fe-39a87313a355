const FeeConfig = require('../../models/feeConfig.model');
const catchAsync = require('../../utils/catchAsync');
const AppError = require('../../utils/appError');

/**
 * Get current active fee configuration
 * @route GET /api/v1/admin/fees/config/current
 * @access Private (Admin only)
 */
exports.getCurrentFeeConfig = catchAsync(async (req, res, next) => {
  const config = await FeeConfig.getCurrentConfig();
  
  res.status(200).json({
    status: 'success',
    data: {
      feeConfig: config
    }
  });
});

/**
 * Get all fee configurations
 * @route GET /api/v1/admin/fees/config
 * @access Private (Admin only)
 */
exports.getAllFeeConfigs = catchAsync(async (req, res, next) => {
  const configs = await FeeConfig.find().sort({ effectiveFrom: -1 });
  
  res.status(200).json({
    status: 'success',
    results: configs.length,
    data: {
      feeConfigs: configs
    }
  });
});

/**
 * Create a new fee configuration
 * @route POST /api/v1/admin/fees/config
 * @access Private (Admin only)
 */
exports.createFeeConfig = catchAsync(async (req, res, next) => {
  // Set current user as creator
  req.body.createdBy = req.user.id;
  
  // If setting this config as active, deactivate all other configs
  if (req.body.isActive) {
    await FeeConfig.updateMany({}, { isActive: false });
  }
  
  const newConfig = await FeeConfig.create(req.body);
  
  res.status(201).json({
    status: 'success',
    data: {
      feeConfig: newConfig
    }
  });
});

/**
 * Get a specific fee configuration
 * @route GET /api/v1/admin/fees/config/:id
 * @access Private (Admin only)
 */
exports.getFeeConfig = catchAsync(async (req, res, next) => {
  const config = await FeeConfig.findById(req.params.id);
  
  if (!config) {
    return next(new AppError('No fee configuration found with that ID', 404));
  }
  
  res.status(200).json({
    status: 'success',
    data: {
      feeConfig: config
    }
  });
});

/**
 * Update a fee configuration
 * @route PATCH /api/v1/admin/fees/config/:id
 * @access Private (Admin only)
 */
exports.updateFeeConfig = catchAsync(async (req, res, next) => {
  // Set current user as updater
  req.body.updatedBy = req.user.id;
  
  // If setting this config as active, deactivate all other configs
  if (req.body.isActive) {
    await FeeConfig.updateMany({ _id: { $ne: req.params.id } }, { isActive: false });
  }
  
  const config = await FeeConfig.findByIdAndUpdate(req.params.id, req.body, {
    new: true,
    runValidators: true
  });
  
  if (!config) {
    return next(new AppError('No fee configuration found with that ID', 404));
  }
  
  res.status(200).json({
    status: 'success',
    data: {
      feeConfig: config
    }
  });
});

/**
 * Delete a fee configuration
 * @route DELETE /api/v1/admin/fees/config/:id
 * @access Private (Admin only)
 */
exports.deleteFeeConfig = catchAsync(async (req, res, next) => {
  const config = await FeeConfig.findById(req.params.id);
  
  if (!config) {
    return next(new AppError('No fee configuration found with that ID', 404));
  }
  
  // Don't allow deleting the active configuration
  if (config.isActive) {
    return next(new AppError('Cannot delete the active fee configuration. Make another configuration active first.', 400));
  }
  
  await FeeConfig.findByIdAndDelete(req.params.id);
  
  res.status(204).json({
    status: 'success',
    data: null
  });
});
