const express = require('express');
const exchangeController = require('../../controllers/admin/exchange.controller');
const authMiddleware = require('../../middleware/auth.middleware');

const router = express.Router();

// Protect all routes
router.use(authMiddleware.protect);
router.use(authMiddleware.restrictTo('admin'));

// Exchange routes
router.get('/products', exchangeController.getExchangeProducts);
router.get('/products/:productId/variations', exchangeController.getProductVariations);
router.get('/bales', exchangeController.getExchangeBales);
router.get('/bales/:baleId/variations', exchangeController.getBaleVariations);

module.exports = router;
