const express = require('express');
const payoutController = require('../../controllers/creators/payout.controller');
const authMiddleware = require('../../middleware/auth.middleware');

const router = express.Router();

// Protect all routes
router.use(authMiddleware.protect);
router.use(authMiddleware.restrictTo('creator'));

// Payout routes
router.get('/', payoutController.getMyPayouts);
router.get('/stats', payoutController.getMyPayoutStats);
router.post('/request', payoutController.requestPayout);
router.patch('/:id/cancel', payoutController.cancelPayoutRequest);
router.get('/:id', payoutController.getMyPayout);

module.exports = router;
