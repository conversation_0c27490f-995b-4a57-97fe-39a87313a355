const NotificationPreference = require('../models/notificationPreference.model');
const AppError = require('../utils/appError');
const catchAsync = require('../utils/catchAsync');

/**
 * Get notification preferences for the authenticated user
 * @route GET /api/v1/notifications/preferences
 * @access Private
 */
exports.getPreferences = catchAsync(async (req, res, next) => {
  const preferences = await NotificationPreference.getForUser(req.user.id);

  res.status(200).json({
    status: 'success',
    data: {
      preferences
    }
  });
});

/**
 * Update notification preferences for the authenticated user
 * @route PATCH /api/v1/notifications/preferences
 * @access Private
 */
exports.updatePreferences = catchAsync(async (req, res, next) => {
  // Validate request body
  if (!req.body.channels && !req.body.types && !req.body.quietHours) {
    return next(new AppError('No update data provided', 400));
  }

  const preferences = await NotificationPreference.updateForUser(req.user.id, req.body);

  res.status(200).json({
    status: 'success',
    data: {
      preferences
    }
  });
});

/**
 * Enable all notifications for the authenticated user
 * @route PATCH /api/v1/notifications/preferences/enable-all
 * @access Private
 */
exports.enableAllNotifications = catchAsync(async (req, res, next) => {
  const preferences = await NotificationPreference.getForUser(req.user.id);

  // Enable all channels
  Object.keys(preferences.channels).forEach(channel => {
    preferences.channels[channel] = true;
  });

  // Enable all types
  Object.keys(preferences.types).forEach(type => {
    preferences.types[type] = true;
  });

  // Disable quiet hours
  preferences.quietHours.enabled = false;

  await preferences.save();

  res.status(200).json({
    status: 'success',
    data: {
      preferences
    }
  });
});

/**
 * Disable all notifications for the authenticated user
 * @route PATCH /api/v1/notifications/preferences/disable-all
 * @access Private
 */
exports.disableAllNotifications = catchAsync(async (req, res, next) => {
  const preferences = await NotificationPreference.getForUser(req.user.id);

  // Disable all channels except inApp
  Object.keys(preferences.channels).forEach(channel => {
    if (channel !== 'inApp') {
      preferences.channels[channel] = false;
    }
  });

  // Disable all types
  Object.keys(preferences.types).forEach(type => {
    preferences.types[type] = false;
  });

  await preferences.save();

  res.status(200).json({
    status: 'success',
    data: {
      preferences
    }
  });
});

/**
 * Reset notification preferences to default for the authenticated user
 * @route PATCH /api/v1/notifications/preferences/reset
 * @access Private
 */
exports.resetPreferences = catchAsync(async (req, res, next) => {
  // Delete existing preferences
  await NotificationPreference.deleteOne({ user: req.user.id });

  // Create new preferences with default values
  const preferences = await NotificationPreference.create({ user: req.user.id });

  res.status(200).json({
    status: 'success',
    data: {
      preferences
    }
  });
});
