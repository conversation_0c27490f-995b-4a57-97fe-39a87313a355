# Creators Backend Architecture

This document outlines the creator-specific functionality, models, and endpoints for the Flashy API.

## Models

## Endpoints

### Onboarding

| Endpoint | Method | Description | Request Body | Response |
|----------|--------|-------------|--------------|----------|
| `/api/v1/creators/onboarding/status` | GET | Get onboarding status | - | Onboarding status |
| `/api/v1/creators/onboarding/business-info` | POST | Submit business info | Business info object | Updated creator |
| `/api/v1/creators/onboarding/shop-info` | POST | Submit shop info | Shop info object | Updated creator |
| `/api/v1/creators/onboarding/payment-info` | POST | Submit payment info | Payment info object | Updated creator |
| `/api/v1/creators/onboarding/shipping-info` | POST | Submit shipping info | Shipping info object | Updated creator |
| `/api/v1/creators/onboarding/verification-docs` | POST | Upload verification docs | Form data with files | Updated creator |
| `/api/v1/creators/onboarding/complete` | POST | Complete onboarding | - | Updated creator |

### Profile Management

| Endpoint | Method | Description | Request Body | Response |
|----------|--------|-------------|--------------|----------|
| `/api/v1/creators/profile` | GET | Get creator profile | - | Creator data |
| `/api/v1/creators/profile` | PATCH | Update creator profile | Profile fields | Updated creator |
| `/api/v1/creators/profile/photo` | PATCH | Update profile photo | Form data with photo | Updated creator |
| `/api/v1/creators/profile/business-info` | GET | Get business info | - | Business info data |
| `/api/v1/creators/profile/business-info` | PATCH | Update business info | Business info fields | Updated creator |
| `/api/v1/creators/profile/shop-info` | GET | Get shop info | - | Shop info data |
| `/api/v1/creators/profile/shop-info` | PATCH | Update shop info | Shop info fields | Updated creator |
| `/api/v1/creators/profile/payment-info` | GET | Get payment info | - | Payment info data |
| `/api/v1/creators/profile/payment-info` | PATCH | Update payment info | Payment info fields | Updated creator |
| `/api/v1/creators/profile/shipping-info` | GET | Get shipping info | - | Shipping info data |
| `/api/v1/creators/profile/shipping-info` | PATCH | Update shipping info | Shipping info fields | Updated creator |
| `/api/v1/creators/profile/notification-preferences` | GET | Get notification preferences | - | Preferences data |
| `/api/v1/creators/profile/notification-preferences` | PATCH | Update notification preferences | Preference fields | Updated creator |
| `/api/v1/creators/profile/social-media` | GET | Get social media links | - | Social media data |
| `/api/v1/creators/profile/social-media` | PATCH | Update social media links | Social media fields | Updated creator |
| `/api/v1/creators/profile/seller-profile` | GET | Get seller profile | - | Seller profile data |
| `/api/v1/creators/profile/seller-profile` | PATCH | Update seller profile | Seller profile fields | Updated creator |
| `/api/v1/creators/profile/payout-preferences` | GET | Get payout preferences | - | Payout preferences data |
| `/api/v1/creators/profile/payout-preferences` | PATCH | Update payout preferences | Payout preference fields | Updated creator |
| `/api/v1/creators/profile/dashboard` | GET | Get dashboard statistics | - | Dashboard data |

### Product Management

> **Note:** The main products endpoint supports comprehensive filtering options that eliminate the need for separate endpoints for different product statuses, featured products, or products with sales/promotions. Use the appropriate query parameters to filter products as needed.

| Endpoint | Method | Description | Request Body/Query Parameters | Response |
|----------|--------|-------------|--------------------------|----------|
| `/api/v1/creators/products` | GET | Get creator products | `page, limit, status=['draft','pending','active','inactive','rejected'], sort, search, category, featured=true/false, hasDiscount=true/false, onSale=true/false, hasPromotion=true/false` | Products array |
| `/api/v1/creators/products` | POST | Create product | Product data + images | Created product |
| `/api/v1/creators/products/:id` | GET | Get product details | - | Product details |
| `/api/v1/creators/products/:id` | PATCH | Update product | Product fields | Updated product |
| `/api/v1/creators/products/:id/basic-info` | PATCH | Update basic product info | `{ name, images,brand, description, highlights, gender, basePrice, category, relatedCategories, tags }` | Updated product |
| `/api/v1/creators/products/:id/specifications` | PATCH | Update product specifications | `{ mainMaterial, dressStyle, pantType, skirtType, mensPantSize, fitType, pattern, closure, neckline, sleeveLength, waistline, hemline }` | Updated product |
| `/api/v1/creators/products/:id/seo` | PATCH | Update product SEO | `{ metaTitle, metaDescription, keywords }` | Updated product |
| `/api/v1/creators/products/:id` | DELETE | Delete product | - | Success message |
| `/api/v1/creators/products/:id/variations` | GET | Get variations | - | Variations array |
| `/api/v1/creators/products/:id/variations` | POST | Add variation | `{ color, size, price, quantity, sku, images }` | Updated product |
| `/api/v1/creators/products/:id/variations/:variationId` | PATCH | Update variation | Variation fields | Updated product |
| `/api/v1/creators/products/:id/variations/:variationId` | DELETE | Delete variation | - | Updated product |
| `/api/v1/creators/products/:id/reviews` | GET | Get product reviews | `page, limit, rating` | Reviews array |
| `/api/v1/creators/products/:id/promotions/:promotionId` | PATCH | Update promotion | `{ discountValue, discountType, promoStock, startDate, endDate, isActive }` | Updated product |
| `/api/v1/creators/products/:id/promotions/:promotionId` | DELETE | Remove promotion | - | Updated product |
| `/api/v1/creators/products/:id/sales` | GET | Get sales history | `period` | Sales data |
| `/api/v1/creators/products/stats` | GET | Get product statistics | `period` | Product stats |
| `/api/v1/creators/products/low-stock` | GET | Get low stock products | `threshold` | Products array |
| `/api/v1/creators/products/out-of-stock` | GET | Get out of stock products | - | Products array |



### Bale Management

> **Note:** The main bales endpoint supports comprehensive filtering options that eliminate the need for separate endpoints for different bale statuses, featured bales, or bales with sales/promotions. Use the appropriate query parameters to filter bales as needed.

| Endpoint | Method | Description | Request Body/Query Parameters | Response |
|----------|--------|-------------|--------------------------|----------|
| `/api/v1/creators/bales` | GET | Get creator bales | `page, limit, status=['draft','pending','active','inactive','rejected'], sort, search, category, featured=true/false, hasDiscount=true/false, onSale=true/false, hasPromotion=true/false, country, condition` | Bales array |
| `/api/v1/creators/bales` | POST | Create bale | Bale data + images | Created bale |
| `/api/v1/creators/bales/:id` | GET | Get bale details | - | Bale details |
| `/api/v1/creators/bales/:id` | PATCH | Update bale | Bale fields | Updated bale |
| `/api/v1/creators/bales/:id/basic-info` | PATCH | Update basic bale info | `{ name, description, highlights, basePrice, category, relatedCategories, tags }` | Updated bale |
| `/api/v1/creators/bales/:id/specifications` | PATCH | Update bale specifications | `{ country, totalItems, weight, dimensions: { length, width, height }, condition }` | Updated bale |
| `/api/v1/creators/bales/:id/seo` | PATCH | Update bale SEO | `{ metaTitle, metaDescription, keywords }` | Updated bale |
| `/api/v1/creators/bales/:id` | DELETE | Delete bale | - | Success message |
| `/api/v1/creators/bales/:id/variations` | GET | Get variations | - | Variations array |
| `/api/v1/creators/bales/:id/variations` | POST | Add variation | `{ identifier, price, quantity, lowStockThreshold }` | Updated bale |
| `/api/v1/creators/bales/:id/variations/:variationId` | PATCH | Update variation | `{ identifier, price, quantity, lowStockThreshold }` | Updated bale |
| `/api/v1/creators/bales/:id/variations/:variationId` | DELETE | Delete variation | - | Updated bale |
| `/api/v1/creators/bales/:id/products` | GET | Get associated products | `page, limit` | Products array |
| `/api/v1/creators/bales/:id/products` | POST | Add products to bale | `{ products: [{ product, quantity }] }` | Updated bale |
| `/api/v1/creators/bales/:id/products/:productId` | DELETE | Remove product from bale | - | Updated bale |
| `/api/v1/creators/bales/:id/reviews` | GET | Get bale reviews | `page, limit, rating` | Reviews array |
| `/api/v1/creators/bales/:id/promotions` | GET | Get bale promotions | - | Promotions array |
| `/api/v1/creators/bales/:id/promotions/:promotionId` | PATCH | Update promotion | `{ discountValue, discountType, promoStock, startDate, endDate, isActive }` | Updated bale |
| `/api/v1/creators/bales/:id/promotions/:promotionId` | DELETE | Remove promotion | - | Updated bale |
| `/api/v1/creators/bales/:id/sales` | GET | Get sales history | `period` | Sales data |
| `/api/v1/creators/bales/stats` | GET | Get bale statistics | `period` | Bale stats |
| `/api/v1/creators/bales/low-stock` | GET | Get low stock bales | `threshold` | Bales array |
| `/api/v1/creators/bales/out-of-stock` | GET | Get out of stock bales | - | Bales array |


### Review Management Endpoints

| Endpoint | Method | Description | Query Parameters/Request Body | Response |
|----------|--------|-------------|--------------------------|----------|
| `/api/v1/creators/reviews` | GET | Get all creator reviews | `page, limit, sort, product, bale, rating, verified, hidden` | Reviews array |
| `/api/v1/creators/reviews/product/:id` | GET | Get product reviews | `page, limit, sort, rating, verified` | Reviews array |
| `/api/v1/creators/reviews/bale/:id` | GET | Get bale reviews | `page, limit, sort, rating, verified` | Reviews array |
| `/api/v1/creators/reviews/:id` | GET | Get review details | - | Review details |



### Promotion Management Endpoints

| Endpoint | Method | Description | Request Body/Query Parameters | Response |
|----------|--------|-------------|--------------------------|----------|
| `/api/v1/creators/promotions` | GET | Get all available promotions | `page, limit, sort, search, active, category` | Promotions array |
| `/api/v1/creators/promotions/my` | GET | Get creator's joined promotions | `page, limit, sort, status=['pending','approved','rejected'], active` | Promotions array |
| `/api/v1/creators/promotions/:id` | GET | Get promotion details | - | Promotion details |
| `/api/v1/creators/promotions/:id/join` | POST | Join a promotion | `{ products: [{ productId, discountValue, promoStock }] }` | Updated promotion |
| `/api/v1/creators/promotions/:id/products` | PATCH | Update promotion products | `{ products: [{ productId, discountValue, promoStock }] }` | Updated promotion |
| `/api/v1/creators/promotions/:id/leave` | DELETE | Leave a promotion | - | Success message |
| `/api/v1/creators/promotions/active` | GET | Get active promotions | `page, limit, sort` | Promotions array |
| `/api/v1/creators/promotions/upcoming` | GET | Get upcoming promotions | `page, limit, sort` | Promotions array |
| `/api/v1/creators/promotions/expired` | GET | Get expired promotions | `page, limit, sort` | Promotions array |
| `/api/v1/creators/promotions/stats` | GET | Get promotion statistics | - | Promotion stats |


### Order Model

### Order Management Endpoints

> **Note:** Creators can only view and manage orders that contain their products or bales. Each order may contain items from multiple creators, but creators can only see and update their own items.

| Endpoint | Method | Description | Query Parameters/Request Body | Response |
|----------|--------|-------------|--------------------------|----------|
| `/api/v1/creators/orders` | GET | Get creator orders | `page, limit, status=['pending','processing','shipped','delivered','cancelled','refunded'], startDate, endDate, sort` | Orders array with creator's items |
| `/api/v1/creators/orders/:id` | GET | Get order details | - | Order details with creator's items |
| `/api/v1/creators/orders/:id/items/:itemId` | PATCH | Update order item status | `{ status: ['processing', 'shipped', 'delivered', 'cancelled'], notes }` | Updated item |
| `/api/v1/creators/orders/:id/items/:itemId/ship` | PATCH | Ship order item | `{ trackingNumber, carrier, notes }` | Updated item with shipping info |
| `/api/v1/creators/orders/stats` | GET | Get order statistics | `period: ['7', '30', '90', 'all']` | Order stats with counts, totals, and time-based data |
| `/api/v1/creators/orders/recent` | GET | Get recent orders | `limit` | Orders array |



### Notification Management

| Endpoint | Method | Description | Query Parameters/Request Body | Response |
|----------|--------|-------------|--------------------------|----------|
| `/api/v1/creators/notifications` | GET | Get notifications | `page, limit, read, type, priority, populate` | Notifications array |
| `/api/v1/creators/notifications/unread-count` | GET | Get unread count | - | Unread count |
| `/api/v1/creators/notifications/:id/read` | PATCH | Mark as read | - | Updated notification |
| `/api/v1/creators/notifications/mark-all-read` | PATCH | Mark all as read | - | Count of updated notifications |
| `/api/v1/creators/notifications/:id` | DELETE | Delete notification | - | Success message |
| `/api/v1/creators/notifications/preferences` | GET | Get notification preferences | - | Preferences object |
| `/api/v1/creators/notifications/preferences` | PATCH | Update preferences | `{ channels: { email, push, sms }, types: { orders, payments, products, ... }, quietHours: { enabled, start, end, timezone } }` | Updated preferences |
| `/api/v1/creators/notifications/preferences/enable-all` | PATCH | Enable all notifications | - | Updated preferences |
| `/api/v1/creators/notifications/preferences/disable-all` | PATCH | Disable all notifications | - | Updated preferences |
| `/api/v1/creators/notifications/preferences/reset` | PATCH | Reset to defaults | - | Updated preferences |

### Payout Management

| Endpoint | Method | Description | Query Parameters/Request Body | Response |
|----------|--------|-------------|--------------------------|----------|
| `/api/v1/creators/payouts` | GET | Get creator payouts | `page, limit, status=['pending','processing','paid','failed','cancelled']` | Payouts array |
| `/api/v1/creators/payouts/request` | POST | Request payout | `{ amount, payoutType=['weekly','on_demand'], description, periodStart, periodEnd }` | Created payout |
| `/api/v1/creators/payouts/:id` | GET | Get payout details | - | Payout details with fees, status history, and payment info |
| `/api/v1/creators/payouts/:id/cancel` | PATCH | Cancel pending payout | - | Updated payout with cancelled status |
| `/api/v1/creators/payouts/stats` | GET | Get payout statistics | - | Stats with counts by status, total earnings, total paid out, pending amount, and available balance |


### Wallet Management

| Endpoint | Method | Description | Query Parameters/Request Body | Response |
|----------|--------|-------------|--------------------------|----------|
| `/api/v1/creators/wallet/balance` | GET | Get wallet balance | - | Wallet data with balance, pendingBalance, onHoldBalance, availableBalance |
| `/api/v1/creators/wallet/transactions` | GET | Get wallet transactions | `page, limit, type=['order_earning','payout','refund','adjustment'], status=['pending','completed','failed','reversed'], startDate, endDate` | Transactions array |
| `/api/v1/creators/wallet/summary` | GET | Get transaction summary | - | Summary with total earnings, payouts, refunds, and net balance |
| `/api/v1/creators/wallet/earnings` | GET | Get available earnings | - | Available earnings data |

### Dashboard Management

| Endpoint | Method | Description | Query Parameters/Request Body | Response |
|----------|--------|-------------|--------------------------|----------|
| `/api/v1/creators/dashboard` | GET | Get dashboard overview | `period=['7d','30d','90d','all']` | Complete dashboard with all key metrics |
| `/api/v1/creators/dashboard/summary` | GET | Get summary statistics | `period=['7d','30d','90d','all']` | Summary of products, orders, revenue, etc. |
| `/api/v1/creators/dashboard/inventory` | GET | Get inventory status | - | Low stock, out of stock items |


### Revenue Management

| Endpoint | Method | Description | Query Parameters/Request Body | Response |
|----------|--------|-------------|--------------------------|----------|
| `/api/v1/creators/revenue/summary` | GET | Get revenue summary | `period=['7d','30d','90d','1y','all']` | Summary with total sales, earnings, fees, and net revenue |
| `/api/v1/creators/revenue/chart` | GET | Get revenue chart data | `period=['7d','30d','90d','1y'], interval=['day','week','month']` | Time-series data for charting |
| `/api/v1/creators/revenue/products` | GET | Get top-selling products | `period=['7d','30d','90d','1y','all'], limit` | Products ranked by revenue |
| `/api/v1/creators/revenue/categories` | GET | Get revenue by category | `period=['7d','30d','90d','1y','all']` | Categories with sales and revenue data |
| `/api/v1/creators/revenue/forecast` | GET | Get revenue forecast | `months=3` | Projected revenue for upcoming months |
