const { Creator } = require('../../models/user.model');
const AppError = require('../../utils/appError');
const catchAsync = require('../../utils/catchAsync');

/**
 * Get creator onboarding status
 * @route GET /api/v1/creators/onboarding
 * @access Private (Creator only)
 */
exports.getOnboardingStatus = catchAsync(async (req, res, next) => {
  const creator = await Creator.findById(req.user.id)
    .select('onboardingStatus onboardingProgress');

  if (!creator) {
    return next(new AppError('Creator not found', 404));
  }

  res.status(200).json({
    status: 'success',
    data: {
      onboardingStatus: creator.onboardingStatus,
      onboardingProgress: creator.onboardingProgress,
      isComplete: creator.isOnboardingComplete()
    }
  });
});

/**
 * Get business information
 * @route GET /api/v1/creators/onboarding/business-info
 * @access Private (Creator only)
 */
exports.getBusinessInfo = catchAsync(async (req, res, next) => {
  const creator = await Creator.findById(req.user.id)
    .select('businessInfo verificationStatus verificationDetails');

  if (!creator) {
    return next(new AppError('Creator not found', 404));
  }

  res.status(200).json({
    status: 'success',
    data: {
      businessInfo: creator.businessInfo,
      verificationStatus: creator.verificationStatus,
      verificationDetails: creator.verificationDetails
    }
  });
});

/**
 * Get payment information
 * @route GET /api/v1/creators/onboarding/payment-info
 * @access Private (Creator only)
 */
exports.getPaymentInfo = catchAsync(async (req, res, next) => {
  const creator = await Creator.findById(req.user.id)
    .select('paymentInfo payoutPreferences');

  if (!creator) {
    return next(new AppError('Creator not found', 404));
  }

  res.status(200).json({
    status: 'success',
    data: {
      paymentInfo: creator.paymentInfo,
      payoutPreferences: creator.payoutPreferences
    }
  });
});

/**
 * Get shop information
 * @route GET /api/v1/creators/onboarding/shop-info
 * @access Private (Creator only)
 */
exports.getShopInfo = catchAsync(async (req, res, next) => {
  const creator = await Creator.findById(req.user.id)
    .select('shopInfo socialMedia');

  if (!creator) {
    return next(new AppError('Creator not found', 404));
  }

  res.status(200).json({
    status: 'success',
    data: {
      shopInfo: creator.shopInfo,
      socialMedia: creator.socialMedia
    }
  });
});

/**
 * Get shipping information
 * @route GET /api/v1/creators/onboarding/shipping-info
 * @access Private (Creator only)
 */
exports.getShippingInfo = catchAsync(async (req, res, next) => {
  const creator = await Creator.findById(req.user.id)
    .select('shippingInfo');

  if (!creator) {
    return next(new AppError('Creator not found', 404));
  }

  res.status(200).json({
    status: 'success',
    data: {
      shippingInfo: creator.shippingInfo
    }
  });
});

/**
 * Update business information (Step 1)
 * @route PATCH /api/v1/creators/onboarding/business-info
 * @access Private (Creator only)
 */
exports.updateBusinessInfo = catchAsync(async (req, res, next) => {
  // Validate required fields
  const requiredFields = ['businessName'];
  for (const field of requiredFields) {
    if (!req.body[field]) {
      return next(new AppError(`Please provide ${field}`, 400));
    }
  }

  // Get creator
  const creator = await Creator.findById(req.user.id);

  if (!creator) {
    return next(new AppError('Creator not found', 404));
  }

  // Initialize businessInfo if it doesn't exist
  if (!creator.businessInfo) {
    creator.businessInfo = {};
  }

  // Update business information
  creator.businessInfo.businessName = req.body.businessName;

  // Update optional fields if provided
  if (req.body.businessType) creator.businessInfo.businessType = req.body.businessType;
  if (req.body.ownerName) creator.businessInfo.ownerName = req.body.ownerName;
  if (req.body.ownerID) creator.businessInfo.ownerID = req.body.ownerID;
  if (req.body.taxId) creator.businessInfo.taxId = req.body.taxId;
  if (req.body.phoneNumber) creator.businessInfo.phoneNumber = req.body.phoneNumber;

  // Initialize businessAddress if it doesn't exist
  if (!creator.businessInfo.businessAddress) {
    creator.businessInfo.businessAddress = {
      country: 'Ghana' // Default country
    };
  }

  // Update business address if provided
  if (req.body.businessAddress) {
    // Update specific fields if provided
    if (req.body.businessAddress.addressLine1) {
      creator.businessInfo.businessAddress.addressLine1 = req.body.businessAddress.addressLine1;
    }
    if (req.body.businessAddress.addressLine2) {
      creator.businessInfo.businessAddress.addressLine2 = req.body.businessAddress.addressLine2;
    }
    if (req.body.businessAddress.city) {
      creator.businessInfo.businessAddress.city = req.body.businessAddress.city;
    }
    if (req.body.businessAddress.state) {
      creator.businessInfo.businessAddress.state = req.body.businessAddress.state;
    }
    if (req.body.businessAddress.country) {
      creator.businessInfo.businessAddress.country = req.body.businessAddress.country;
    }
    if (req.body.businessAddress.digitalGps) {
      creator.businessInfo.businessAddress.digitalGps = req.body.businessAddress.digitalGps;
    }
  }

  // Update verification documents if provided
  if (req.body.verificationDocuments && Array.isArray(req.body.verificationDocuments)) {
    creator.businessInfo.verificationDocuments = req.body.verificationDocuments;
    creator.verificationDetails = {
      ...creator.verificationDetails,
      submittedAt: Date.now(),
      documents: req.body.verificationDocuments
    };
    creator.verificationStatus = 'pending';
  }

  // Update onboarding progress
  creator.onboardingProgress.businessInfo = true;

  // Save the updated creator
  await creator.save();

  res.status(200).json({
    status: 'success',
    data: {
      businessInfo: creator.businessInfo,
      onboardingStatus: creator.onboardingStatus,
      onboardingProgress: creator.onboardingProgress
    }
  });
});

/**
 * Update payment information (Step 2)
 * @route PATCH /api/v1/creators/onboarding/payment-info
 * @access Private (Creator only)
 */
exports.updatePaymentInfo = catchAsync(async (req, res, next) => {
  // Check if request body exists
  if (!req.body || Object.keys(req.body).length === 0) {
    return next(new AppError('Please provide payment information to update', 400));
  }

  // Validate payment option
  if (!req.body.paymentOption || !['bank', 'mobile_money'].includes(req.body.paymentOption)) {
    return next(new AppError('Please provide a valid payment option (bank or mobile_money)', 400));
  }

  // Get creator
  const creator = await Creator.findById(req.user.id);

  if (!creator) {
    return next(new AppError('Creator not found', 404));
  }


  // Initialize paymentInfo if it doesn't exist
  if (!creator.paymentInfo) {
    creator.paymentInfo = {};
  }

  // Update payment option
  creator.paymentInfo.paymentOption = req.body.paymentOption;

  // Update bank details if payment option is bank
  if (req.body.paymentOption === 'bank' && req.body.bankDetails) {
    // Validate required bank fields
    if (!req.body.bankDetails.accountNumber || !req.body.bankDetails.bankName) {
      return next(new AppError('Please provide account number and bank name', 400));
    }

    // Initialize bankDetails if it doesn't exist
    if (!creator.paymentInfo.bankDetails) {
      creator.paymentInfo.bankDetails = {};
    }

    // Update specific bank details fields if provided
    if (req.body.bankDetails.accountNumber) {
      creator.paymentInfo.bankDetails.accountNumber = req.body.bankDetails.accountNumber;
    }
    if (req.body.bankDetails.accountName) {
      creator.paymentInfo.bankDetails.accountName = req.body.bankDetails.accountName;
    }
    if (req.body.bankDetails.bankName) {
      creator.paymentInfo.bankDetails.bankName = req.body.bankDetails.bankName;
    }
    if (req.body.bankDetails.branchName) {
      creator.paymentInfo.bankDetails.branchName = req.body.bankDetails.branchName;
    }
    if (req.body.bankDetails.swiftCode) {
      creator.paymentInfo.bankDetails.swiftCode = req.body.bankDetails.swiftCode;
    }
  }

  // Update mobile money details if payment option is mobile_money
  if (req.body.paymentOption === 'mobile_money' && req.body.mobileMoneyDetails) {
    // Validate required mobile money fields
    if (!req.body.mobileMoneyDetails.serviceProvider || !req.body.mobileMoneyDetails.registeredNumber) {
      return next(new AppError('Please provide service provider and registered number', 400));
    }

    // Initialize mobileMoneyDetails if it doesn't exist
    if (!creator.paymentInfo.mobileMoneyDetails) {
      creator.paymentInfo.mobileMoneyDetails = {};
    }

    // Update specific mobile money details fields if provided
    if (req.body.mobileMoneyDetails.serviceProvider) {
      creator.paymentInfo.mobileMoneyDetails.serviceProvider = req.body.mobileMoneyDetails.serviceProvider;
    }
    if (req.body.mobileMoneyDetails.registeredNumber) {
      creator.paymentInfo.mobileMoneyDetails.registeredNumber = req.body.mobileMoneyDetails.registeredNumber;
    }
    if (req.body.mobileMoneyDetails.registeredName) {
      creator.paymentInfo.mobileMoneyDetails.registeredName = req.body.mobileMoneyDetails.registeredName;
    }
  }

  // Update payout preferences if provided
  if (req.body.payoutPreferences) {
    // Initialize payoutPreferences if it doesn't exist
    if (!creator.payoutPreferences) {
      creator.payoutPreferences = {};
    }

    // Update specific payout preferences fields if provided
    if (req.body.payoutPreferences.frequency) {
      creator.payoutPreferences.frequency = req.body.payoutPreferences.frequency;
    }
    if (req.body.payoutPreferences.minimumAmount) {
      creator.payoutPreferences.minimumAmount = req.body.payoutPreferences.minimumAmount;
    }
    if (req.body.payoutPreferences.automaticPayouts !== undefined) {
      creator.payoutPreferences.automaticPayouts = req.body.payoutPreferences.automaticPayouts;
    }
  }

  // Update onboarding progress
  creator.onboardingProgress.paymentInfo = true;

  // Save the updated creator
  await creator.save();

  res.status(200).json({
    status: 'success',
    data: {
      paymentInfo: creator.paymentInfo,
      payoutPreferences: creator.payoutPreferences,
      onboardingStatus: creator.onboardingStatus,
      onboardingProgress: creator.onboardingProgress
    }
  });
});

/**
 * Update shop information (Step 3)
 * @route PATCH /api/v1/creators/onboarding/shop-info
 * @access Private (Creator only)
 */
exports.updateShopInfo = catchAsync(async (req, res, next) => {
  // Check if request body exists
  if (!req.body || Object.keys(req.body).length === 0) {
    return next(new AppError('Please provide shop information to update', 400));
  }



  // Process form data for nested objects
  // Handle contact fields
  if (req.body['contact[name]'] || req.body['contact[email]'] || req.body['contact[phone]']) {
    req.body.contact = {
      name: req.body['contact[name]'],
      email: req.body['contact[email]'],
      phone: req.body['contact[phone]']
    };
    // Delete the original form fields
    delete req.body['contact[name]'];
    delete req.body['contact[email]'];
    delete req.body['contact[phone]'];
  }



  // Handle social media fields
  if (req.body['socialMedia[instagram]'] || req.body['socialMedia[facebook]'] || req.body['socialMedia[twitter]'] ||
      req.body['socialMedia[tiktok]'] || req.body['socialMedia[website]']) {
    req.body.socialMedia = {
      instagram: req.body['socialMedia[instagram]'],
      facebook: req.body['socialMedia[facebook]'],
      twitter: req.body['socialMedia[twitter]'],
      tiktok: req.body['socialMedia[tiktok]'],
      website: req.body['socialMedia[website]']
    };
    // Delete the original form fields
    delete req.body['socialMedia[instagram]'];
    delete req.body['socialMedia[facebook]'];
    delete req.body['socialMedia[twitter]'];
    delete req.body['socialMedia[tiktok]'];
    delete req.body['socialMedia[website]'];
  }



  // Handle customer care fields
  if (req.body['customerCare[name]'] || req.body['customerCare[email]'] || req.body['customerCare[phone]'] ||
      req.body['customerCare[addressLine1]'] || req.body['customerCare[addressLine2]'] || req.body['customerCare[city]'] ||
      req.body['customerCare[region]'] || req.body['customerCare[country]'] || req.body['customerCare[hours]'] ||
      req.body['customerCare[supportWebsite]']) {
    req.body.customerCare = {
      name: req.body['customerCare[name]'],
      email: req.body['customerCare[email]'],
      phone: req.body['customerCare[phone]'],
      addressLine1: req.body['customerCare[addressLine1]'],
      addressLine2: req.body['customerCare[addressLine2]'],
      city: req.body['customerCare[city]'],
      region: req.body['customerCare[region]'],
      country: req.body['customerCare[country]'],
      hours: req.body['customerCare[hours]'],
      supportWebsite: req.body['customerCare[supportWebsite]']
    };
    // Delete the original form fields
    delete req.body['customerCare[name]'];
    delete req.body['customerCare[email]'];
    delete req.body['customerCare[phone]'];
    delete req.body['customerCare[addressLine1]'];
    delete req.body['customerCare[addressLine2]'];
    delete req.body['customerCare[city]'];
    delete req.body['customerCare[region]'];
    delete req.body['customerCare[country]'];
    delete req.body['customerCare[hours]'];
    delete req.body['customerCare[supportWebsite]'];
  }




  // Validate required fields
  const requiredFields = ['name'];
  for (const field of requiredFields) {
    if (req.body[field] === undefined) {
      return next(new AppError(`Please provide shop ${field}`, 400));
    }
  }

  // Validate contact fields
  if (!req.body.contact || !req.body.contact.name || !req.body.contact.email || !req.body.contact.phone) {
    return next(new AppError('Please provide contact name, email, and phone', 400));
  }

  // Get creator
  const creator = await Creator.findById(req.user.id);

  if (!creator) {
    return next(new AppError('Creator not found', 404));
  }


  // Initialize shopInfo if it doesn't exist
  if (!creator.shopInfo) {
    creator.shopInfo = {};
  }

  // Update shop information
  creator.shopInfo.name = req.body.name;
  creator.shopInfo.contact = req.body.contact;

  // Update optional shop fields if provided
  const optionalFields = ['logo', 'banner', 'description', 'customerCare'];
  optionalFields.forEach(field => {
    if (req.body[field]) {
      creator.shopInfo[field] = req.body[field];
    }
  });

  // Update social media if provided
  if (req.body.socialMedia) {
    // Initialize socialMedia if it doesn't exist
    if (!creator.socialMedia) {
      creator.socialMedia = {};
    }
    creator.socialMedia = {
      ...creator.socialMedia,
      ...req.body.socialMedia
    };
  }




  // Update onboarding progress
  creator.onboardingProgress.shopInfo = true;

  // Save the updated creator
  await creator.save();


  res.status(200).json({
    status: 'success',
    data: {
      shopInfo: creator.shopInfo,
      socialMedia: creator.socialMedia,
      onboardingStatus: creator.onboardingStatus,
      onboardingProgress: creator.onboardingProgress
    }
  });
});

/**
 * Update shipping information (Step 4)
 * @route PATCH /api/v1/creators/onboarding/shipping-info
 * @access Private (Creator only)
 */
exports.updateShippingInfo = catchAsync(async (req, res, next) => {
  // Check if request body exists
  if (!req.body || Object.keys(req.body).length === 0) {
    return next(new AppError('Please provide shipping information to update', 400));
  }


  // Process form data for nested objects
  // Handle shipping address fields
  if (req.body['shippingAddress[addressLine1]'] || req.body['shippingAddress[city]'] ||
      req.body['shippingAddress[state]'] || req.body['shippingAddress[zone]']) {
    req.body.shippingAddress = {
      addressLine1: req.body['shippingAddress[addressLine1]'],
      addressLine2: req.body['shippingAddress[addressLine2]'],
      city: req.body['shippingAddress[city]'],
      state: req.body['shippingAddress[state]'],
      zone: req.body['shippingAddress[zone]'],
      country: req.body['shippingAddress[country]'] || 'Ghana',
      postalCode: req.body['shippingAddress[postalCode]'],
      digitalGps: req.body['shippingAddress[digitalGps]'],
      phone: req.body['shippingAddress[phone]']
    };

    // Delete the original form fields
    delete req.body['shippingAddress[addressLine1]'];
    delete req.body['shippingAddress[addressLine2]'];
    delete req.body['shippingAddress[city]'];
    delete req.body['shippingAddress[state]'];
    delete req.body['shippingAddress[zone]'];
    delete req.body['shippingAddress[country]'];
    delete req.body['shippingAddress[postalCode]'];
    delete req.body['shippingAddress[digitalGps]'];
    delete req.body['shippingAddress[phone]'];
  }

  // Handle return address fields
  if (req.body['returnAddress[useSameAddress]'] === 'true') {
    req.body.returnAddress = {
      useSameAddress: true
    };
    delete req.body['returnAddress[useSameAddress]'];
  } else if (req.body['returnAddress[addressLine1]'] || req.body['returnAddress[city]'] ||
             req.body['returnAddress[state]'] || req.body['returnAddress[zone]']) {
    req.body.returnAddress = {
      useSameAddress: false,
      addressLine1: req.body['returnAddress[addressLine1]'],
      addressLine2: req.body['returnAddress[addressLine2]'],
      city: req.body['returnAddress[city]'],
      state: req.body['returnAddress[state]'],
      zone: req.body['returnAddress[zone]'],
      country: req.body['returnAddress[country]'] || 'Ghana',
      postalCode: req.body['returnAddress[postalCode]'],
      digitalGps: req.body['returnAddress[digitalGps]'],
      phone: req.body['returnAddress[phone]']
    };

    // Delete the original form fields
    delete req.body['returnAddress[useSameAddress]'];
    delete req.body['returnAddress[addressLine1]'];
    delete req.body['returnAddress[addressLine2]'];
    delete req.body['returnAddress[city]'];
    delete req.body['returnAddress[state]'];
    delete req.body['returnAddress[zone]'];
    delete req.body['returnAddress[country]'];
    delete req.body['returnAddress[postalCode]'];
    delete req.body['returnAddress[digitalGps]'];
    delete req.body['returnAddress[phone]'];
  }

  // Handle shipping methods as array
  if (req.body.methods && typeof req.body.methods === 'string') {
    try {
      req.body.methods = JSON.parse(req.body.methods);
    } catch (err) {
      console.log('Error parsing shipping methods:', err);
    }
  }


  // Validate required fields
  if (!req.body.shippingAddress) {
    return next(new AppError('Please provide shipping address', 400));
  }

  // Validate shipping address fields
  const requiredAddressFields = ['addressLine1', 'city', 'state', 'zone'];
  for (const field of requiredAddressFields) {
    if (!req.body.shippingAddress[field]) {
      return next(new AppError(`Please provide shipping address ${field}`, 400));
    }
  }

  // Get creator
  const creator = await Creator.findById(req.user.id);

  if (!creator) {
    return next(new AppError('Creator not found', 404));
  }

  // Check if shop info is complete
  if (!creator.onboardingProgress.shopInfo) {
    return next(new AppError('Please complete shop information first', 400));
  }

  // Initialize shippingInfo if it doesn't exist
  if (!creator.shippingInfo) {
    creator.shippingInfo = {};
  }

  // Initialize shippingAddress if it doesn't exist
  if (!creator.shippingInfo.shippingAddress) {
    creator.shippingInfo.shippingAddress = {};
  }

  // Update shipping address fields
  if (req.body.shippingAddress.addressLine1) {
    creator.shippingInfo.shippingAddress.addressLine1 = req.body.shippingAddress.addressLine1;
  }
  if (req.body.shippingAddress.addressLine2) {
    creator.shippingInfo.shippingAddress.addressLine2 = req.body.shippingAddress.addressLine2;
  }
  if (req.body.shippingAddress.city) {
    creator.shippingInfo.shippingAddress.city = req.body.shippingAddress.city;
  }
  if (req.body.shippingAddress.state) {
    creator.shippingInfo.shippingAddress.state = req.body.shippingAddress.state;
  }
  if (req.body.shippingAddress.zone) {
    creator.shippingInfo.shippingAddress.zone = req.body.shippingAddress.zone;
  }
  if (req.body.shippingAddress.country) {
    creator.shippingInfo.shippingAddress.country = req.body.shippingAddress.country;
  } else {
    // Default country
    creator.shippingInfo.shippingAddress.country = 'Ghana';
  }
  if (req.body.shippingAddress.postalCode) {
    creator.shippingInfo.shippingAddress.postalCode = req.body.shippingAddress.postalCode;
  }
  if (req.body.shippingAddress.digitalGps) {
    creator.shippingInfo.shippingAddress.digitalGps = req.body.shippingAddress.digitalGps;
    // Also update gpsAddress for backward compatibility
    creator.shippingInfo.shippingAddress.gpsAddress = req.body.shippingAddress.digitalGps;
  } else if (req.body.shippingAddress.gpsAddress) {
    creator.shippingInfo.shippingAddress.gpsAddress = req.body.shippingAddress.gpsAddress;
    // Also update digitalGps for consistency
    creator.shippingInfo.shippingAddress.digitalGps = req.body.shippingAddress.gpsAddress;
  }
  if (req.body.shippingAddress.phone) {
    creator.shippingInfo.shippingAddress.phone = req.body.shippingAddress.phone;
  }

  // Update return address if provided
  if (req.body.returnAddress) {
    // Initialize returnAddress if it doesn't exist
    if (!creator.shippingInfo.returnAddress) {
      creator.shippingInfo.returnAddress = {};
    }

    if (req.body.returnAddress.useSameAddress) {
      // Use shipping address for return address
      creator.shippingInfo.returnAddress = {
        ...creator.shippingInfo.shippingAddress,
        useSameAddress: true
      };
    } else {
      // Update specific return address fields
      if (req.body.returnAddress.addressLine1) {
        creator.shippingInfo.returnAddress.addressLine1 = req.body.returnAddress.addressLine1;
      }
      if (req.body.returnAddress.addressLine2) {
        creator.shippingInfo.returnAddress.addressLine2 = req.body.returnAddress.addressLine2;
      }
      if (req.body.returnAddress.city) {
        creator.shippingInfo.returnAddress.city = req.body.returnAddress.city;
      }
      if (req.body.returnAddress.state) {
        creator.shippingInfo.returnAddress.state = req.body.returnAddress.state;
      }
      if (req.body.returnAddress.zone) {
        creator.shippingInfo.returnAddress.zone = req.body.returnAddress.zone;
      }
      if (req.body.returnAddress.country) {
        creator.shippingInfo.returnAddress.country = req.body.returnAddress.country;
      } else {
        // Default country
        creator.shippingInfo.returnAddress.country = 'Ghana';
      }
      if (req.body.returnAddress.postalCode) {
        creator.shippingInfo.returnAddress.postalCode = req.body.returnAddress.postalCode;
      }
      if (req.body.returnAddress.digitalGps) {
        creator.shippingInfo.returnAddress.digitalGps = req.body.returnAddress.digitalGps;
        // Also update gpsAddress for backward compatibility
        creator.shippingInfo.returnAddress.gpsAddress = req.body.returnAddress.digitalGps;
      } else if (req.body.returnAddress.gpsAddress) {
        creator.shippingInfo.returnAddress.gpsAddress = req.body.returnAddress.gpsAddress;
        // Also update digitalGps for consistency
        creator.shippingInfo.returnAddress.digitalGps = req.body.returnAddress.gpsAddress;
      }
      if (req.body.returnAddress.phone) {
        creator.shippingInfo.returnAddress.phone = req.body.returnAddress.phone;
      }
      creator.shippingInfo.returnAddress.useSameAddress = false;
    }
  }

  // Update onboarding progress
  creator.onboardingProgress.shippingInfo = true;

  // Save the updated creator - this will automatically set onboardingStatus to 'completed'
  // if all steps are complete, thanks to the updateOnboardingProgress method
  await creator.save();

  res.status(200).json({
    status: 'success',
    data: {
      shippingInfo: creator.shippingInfo,
      onboardingStatus: creator.onboardingStatus,
      onboardingProgress: creator.onboardingProgress,
      isComplete: creator.isOnboardingComplete()
    }
  });
});

/**
 * Get verification status of a creator
 * @route GET /api/v1/creators/onboarding/verification-status
 * @access Private (Creator only)
 */
exports.getVerificationStatus = catchAsync(async (req, res, next) => {
  // Get creator with verification details
  const creator = await Creator.findById(req.user.id).select(
    'verificationStatus verificationDetails businessInfo.verificationDocuments onboardingStatus onboardingProgress name email'
  );

  if (!creator) {
    return next(new AppError('Creator not found', 404));
  }

  // Prepare verification status response
  const verificationData = {
    verificationStatus: creator.verificationStatus || 'unverified',
    verificationDetails: creator.verificationDetails || null,
    documentsSubmitted: creator.businessInfo?.verificationDocuments || [],
    onboardingStatus: creator.onboardingStatus,
    onboardingProgress: creator.onboardingProgress,
    creatorInfo: {
      name: creator.name,
      email: creator.email
    }
  };

  // Add additional status information
  let statusMessage = '';
  let nextSteps = [];

  switch (creator.verificationStatus) {
    case 'unverified':
      statusMessage = 'No verification documents have been submitted yet.';
      nextSteps = [
        'Complete your business information',
        'Upload required verification documents',
        'Submit for review'
      ];
      break;
    case 'pending':
      statusMessage = 'Your verification documents are under review.';
      nextSteps = [
        'Wait for admin review (typically 2-3 business days)',
        'Check your email for updates',
        'Ensure your contact information is up to date'
      ];
      break;
    case 'verified':
      statusMessage = 'Your account has been verified successfully!';
      nextSteps = [
        'Complete remaining onboarding steps',
        'Start creating and selling products',
        'Set up your shop information'
      ];
      break;
    case 'rejected':
      statusMessage = 'Your verification was rejected. Please review the feedback and resubmit.';
      nextSteps = [
        'Review admin feedback',
        'Update or replace rejected documents',
        'Resubmit for verification'
      ];
      break;
    default:
      statusMessage = 'Verification status unknown.';
      nextSteps = ['Contact support for assistance'];
  }

  verificationData.statusMessage = statusMessage;
  verificationData.nextSteps = nextSteps;

  res.status(200).json({
    status: 'success',
    data: verificationData
  });
});
