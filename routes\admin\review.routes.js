const express = require('express');
const reviewController = require('../../controllers/admin/review.controller');
const authMiddleware = require('../../middleware/auth.middleware');

const router = express.Router();

// Protect all routes
router.use(authMiddleware.protect);
router.use(authMiddleware.restrictTo('admin'));

// Review routes
router.get('/', reviewController.getAllReviews);
router.get('/stats', reviewController.getReviewStats);
router.patch('/:id/toggle-hidden', reviewController.toggleReviewHidden);

module.exports = router;
