const mongoose = require('mongoose');
const { cloudinary, extractPublicId } = require('../config/cloudinary.config');
const { manualCleanup } = require('../utils/cloudinaryCleanup');
const Product = require('../models/product.model');
const { Creator } = require('../models/user.model');

/**
 * <PERSON><PERSON>t to find and clean up orphaned images in Cloudinary
 * Run with: node scripts/cleanupOrphanedImages.js [--dry-run]
 */

const isDryRun = process.argv.includes('--dry-run');

/**
 * Get all images from Cloudinary in a specific folder
 */
const getCloudinaryImages = async (folder) => {
  try {
    const result = await cloudinary.search
      .expression(`folder:${folder}`)
      .sort_by([['created_at', 'desc']])
      .max_results(500)
      .execute();
    
    return result.resources.map(resource => ({
      public_id: resource.public_id,
      url: resource.secure_url,
      created_at: resource.created_at,
      bytes: resource.bytes
    }));
  } catch (error) {
    console.error(`Error fetching images from ${folder}:`, error);
    return [];
  }
};

/**
 * Get all image URLs from database
 */
const getDatabaseImages = async () => {
  const allImages = new Set();
  
  try {
    // Get product images
    console.log('Fetching product images...');
    const products = await Product.find({}, 'images variations.images').lean();
    products.forEach(product => {
      if (product.images) {
        product.images.forEach(img => allImages.add(img));
      }
      if (product.variations) {
        product.variations.forEach(variation => {
          if (variation.images) {
            variation.images.forEach(img => allImages.add(img));
          }
        });
      }
    });
    
    // Get bale images (now stored as products with type: 'bale')
    console.log('Fetching bale images...');
    const bales = await Product.find({ type: 'bale' }, 'images').lean();
    bales.forEach(bale => {
      if (bale.images) {
        bale.images.forEach(img => allImages.add(img));
      }
    });
    
    // Get creator images
    console.log('Fetching creator images...');
    const creators = await Creator.find({}, 'photo shopInfo.logo shopInfo.banner businessInfo.verificationDocuments').lean();
    creators.forEach(creator => {
      if (creator.photo) allImages.add(creator.photo);
      if (creator.shopInfo?.logo) allImages.add(creator.shopInfo.logo);
      if (creator.shopInfo?.banner) allImages.add(creator.shopInfo.banner);
      if (creator.businessInfo?.verificationDocuments) {
        creator.businessInfo.verificationDocuments.forEach(doc => allImages.add(doc));
      }
    });
    
    console.log(`Found ${allImages.size} images in database`);
    return Array.from(allImages);
    
  } catch (error) {
    console.error('Error fetching database images:', error);
    return [];
  }
};

/**
 * Find orphaned images by comparing Cloudinary and database
 */
const findOrphanedImages = async () => {
  console.log('Starting orphaned image detection...\n');
  
  // Get all images from different folders
  const folders = [
    'everyfash/products',
    'everyfash/bales', 
    'everyfash/profiles',
    'everyfash/shop',
    'everyfash/verification'
  ];
  
  const cloudinaryImages = [];
  for (const folder of folders) {
    console.log(`Fetching images from ${folder}...`);
    const images = await getCloudinaryImages(folder);
    cloudinaryImages.push(...images);
    console.log(`Found ${images.length} images in ${folder}`);
  }
  
  console.log(`\nTotal Cloudinary images: ${cloudinaryImages.length}`);
  
  // Get all images referenced in database
  const databaseImages = await getDatabaseImages();
  const databaseUrls = new Set(databaseImages);
  
  // Find orphaned images
  const orphanedImages = cloudinaryImages.filter(img => !databaseUrls.has(img.url));
  
  console.log(`\nOrphaned images found: ${orphanedImages.length}`);
  
  if (orphanedImages.length > 0) {
    console.log('\nOrphaned images:');
    orphanedImages.forEach((img, index) => {
      const sizeKB = Math.round(img.bytes / 1024);
      console.log(`${index + 1}. ${img.public_id} (${sizeKB}KB) - ${img.created_at}`);
    });
    
    // Calculate total size
    const totalBytes = orphanedImages.reduce((sum, img) => sum + img.bytes, 0);
    const totalMB = Math.round(totalBytes / (1024 * 1024) * 100) / 100;
    console.log(`\nTotal orphaned storage: ${totalMB}MB`);
  }
  
  return orphanedImages.map(img => img.url);
};

/**
 * Clean up orphaned images
 */
const cleanupOrphanedImages = async () => {
  try {
    // Connect to database
    await mongoose.connect(process.env.DATABASE_URI);
    console.log('Connected to database');
    
    // Find orphaned images
    const orphanedUrls = await findOrphanedImages();
    
    if (orphanedUrls.length === 0) {
      console.log('\n✅ No orphaned images found!');
      return;
    }
    
    if (isDryRun) {
      console.log('\n🔍 DRY RUN MODE - No images will be deleted');
      console.log(`Would delete ${orphanedUrls.length} orphaned images`);
      return;
    }
    
    // Confirm deletion
    console.log(`\n⚠️  About to delete ${orphanedUrls.length} orphaned images`);
    console.log('This action cannot be undone!');
    
    // In a real script, you might want to add a confirmation prompt here
    // For now, we'll proceed with deletion
    
    console.log('\n🗑️  Starting cleanup...');
    const result = await manualCleanup(orphanedUrls);
    
    console.log(`\n✅ Cleanup completed!`);
    console.log(`- Deleted: ${result.deleted} images`);
    console.log(`- Failed: ${result.failed} images`);
    
    if (result.failed > 0) {
      console.log('\n❌ Failed deletions:');
      result.results
        .filter(r => r.status === 'failed')
        .forEach(r => console.log(`- ${r.url}: ${r.error}`));
    }
    
  } catch (error) {
    console.error('Error during cleanup:', error);
  } finally {
    await mongoose.disconnect();
    console.log('Disconnected from database');
  }
};

/**
 * Find images that might be duplicates
 */
const findDuplicateImages = async () => {
  try {
    await mongoose.connect(process.env.DATABASE_URI);
    console.log('Connected to database');
    
    const databaseImages = await getDatabaseImages();
    const imageCount = {};
    
    // Count occurrences of each image URL
    databaseImages.forEach(url => {
      imageCount[url] = (imageCount[url] || 0) + 1;
    });
    
    // Find duplicates
    const duplicates = Object.entries(imageCount)
      .filter(([url, count]) => count > 1)
      .map(([url, count]) => ({ url, count }));
    
    if (duplicates.length > 0) {
      console.log(`\n📋 Found ${duplicates.length} duplicate image references:`);
      duplicates.forEach(dup => {
        console.log(`- ${dup.url} (referenced ${dup.count} times)`);
      });
    } else {
      console.log('\n✅ No duplicate image references found');
    }
    
  } catch (error) {
    console.error('Error finding duplicates:', error);
  } finally {
    await mongoose.disconnect();
  }
};

// Main execution
const main = async () => {
  const command = process.argv[2];
  
  switch (command) {
    case 'cleanup':
      await cleanupOrphanedImages();
      break;
    case 'duplicates':
      await findDuplicateImages();
      break;
    case 'scan':
      await mongoose.connect(process.env.DATABASE_URI);
      await findOrphanedImages();
      await mongoose.disconnect();
      break;
    default:
      console.log('Usage:');
      console.log('  node scripts/cleanupOrphanedImages.js scan [--dry-run]     - Scan for orphaned images');
      console.log('  node scripts/cleanupOrphanedImages.js cleanup [--dry-run]  - Clean up orphaned images');
      console.log('  node scripts/cleanupOrphanedImages.js duplicates          - Find duplicate references');
      break;
  }
};

// Run the script
if (require.main === module) {
  main().catch(console.error);
}

module.exports = {
  findOrphanedImages,
  cleanupOrphanedImages,
  findDuplicateImages
};
