const mongoose = require('mongoose');
const Product = require('../../models/product.model');
const Review = require('../../models/review.model');
const Category = require('../../models/category.model');
const Order = require('../../models/order.model');
const { Creator } = require('../../models/user.model');
const AppError = require('../../utils/appError');
const catchAsync = require('../../utils/catchAsync');


/**
 * Get all products (including pending and inactive ones)
 * @route GET /api/v1/admin/products
 * @access Private (Admin only)
 */
exports.getAllProducts = catchAsync(async (req, res) => {
  // Build query
  const queryObj = { ...req.query };
  const excludedFields = ['page', 'sort', 'limit', 'fields', 'search'];
  excludedFields.forEach(el => delete queryObj[el]);

  // Advanced filtering
  let queryStr = JSON.stringify(queryObj);
  queryStr = queryStr.replace(/\b(gte|gt|lte|lt)\b/g, match => `$${match}`);

  let query = Product.find(JSON.parse(queryStr));

  // Search functionality
  if (req.query.search) {
    const searchRegex = new RegExp(req.query.search, 'i');
    query = query.find({
      $or: [
        { name: searchRegex },
        { description: searchRegex },
        { 'specifications.mainMaterial': searchRegex }
      ]
    });
  }

  // Filter by status if specified
  if (req.query.status) {
    query = query.find({ status: req.query.status });
  }

  // Filter by creator if specified
  if (req.query.creator) {
    query = query.find({ creator: req.query.creator });
  }

  // Count total before applying pagination
  const total = await Product.countDocuments(query);

  // Sorting
  if (req.query.sort) {
    const sortBy = req.query.sort.split(',').join(' ');
    query = query.sort(sortBy);
  } else {
    query = query.sort('-createdAt');
  }

  // Field limiting
  if (req.query.fields) {
    const fields = req.query.fields.split(',').join(' ');
    query = query.select(fields);
  } else {
    query = query.select('-__v');
  }

  // Pagination
  const page = req.query.page * 1 || 1;
  const limit = req.query.limit * 1 || 20;
  const skip = (page - 1) * limit;

  query = query.skip(skip).limit(limit);

  // Execute query
  const products = await query;

  res.status(200).json({
    status: 'success',
    results: products.length,
    total,
    page,
    limit,
    data: {
      products
    }
  });
});

/**
 * Get product by ID
 * @route GET /api/v1/admin/products/:id
 * @access Private (Admin only)
 */
exports.getProduct = catchAsync(async (req, res, next) => {
  const product = await Product.findById(req.params.id)
    .populate({
      path: 'creator',
      select: 'name email businessInfo.businessName businessInfo.businessAddress verificationStatus'
    })
    .populate({
      path: 'category',
      select: 'name description'
    })
    .populate({
      path: 'relatedCategories',
      select: 'name description'
    });

  if (!product) {
    return next(new AppError('No product found with that ID', 404));
  }

  // Transform category data to include path array
  if (product.category) {
    const pathArray = product.category.name.split(' > ');
    product.category._doc.pathArray = pathArray;
  }

  // Transform related categories to include path arrays
  if (product.relatedCategories && product.relatedCategories.length > 0) {
    product.relatedCategories.forEach(category => {
      if (category) {
        const pathArray = category.name.split(' > ');
        category._doc.pathArray = pathArray;
      }
    });
  }

  // Get reviews for this product
  const reviews = await Review.find({ product: product._id })
    .select('+hidden')
    .sort('-createdAt');

  res.status(200).json({
    status: 'success',
    data: {
      product,
      reviews
    }
  });
});

/**
 * Approve or reject a product
 * @route PATCH /api/v1/admin/products/:id/status
 * @access Private (Admin only)
 */
exports.updateProductStatus = catchAsync(async (req, res, next) => {
  // Validate request body
  if (!req.body.status || !['pending', 'active', 'inactive', 'rejected'].includes(req.body.status)) {
    return next(new AppError('Please provide a valid status (pending, active, inactive, rejected)', 400));
  }

  // Find product
  const product = await Product.findById(req.params.id);

  if (!product) {
    return next(new AppError('No product found with that ID', 404));
  }

  // Update product status
  product.status = req.body.status;

  // Add rejection reason if status is rejected
  if (req.body.status === 'rejected' && req.body.rejectionReason) {
    product.rejectionReason = req.body.rejectionReason;
  }

  // Add approval notes if provided
  if (req.body.adminNotes) {
    product.adminNotes = req.body.adminNotes;
  }

  // Save the updated product
  await product.save();

  // If product is approved, update creator's product count
  if (req.body.status === 'active') {
    await Creator.findByIdAndUpdate(product.creator, {
      $inc: { 'metrics.totalProducts': 1 }
    });

    // Send product approved notification
    try {
      const NotificationService = require('../../services/notification.service');
      await NotificationService.createProductNotification({
        product,
        type: 'product_approved',
        recipient: product.creator
      });

      // Send new product notification to followers
      const Buyer = require('../../models/buyer.model');
      const followers = await Buyer.find({ followedCreators: product.creator });

      for (const follower of followers) {
        // Check if follower has newArrivals preference enabled
        if (follower.preferences?.notificationPreferences?.newArrivals) {
          await NotificationService.createProductNotification({
            product,
            type: 'new_arrivals',
            recipient: follower._id
          });
        }
      }
    } catch (error) {
      console.error('Error sending product notifications:', error);
    }
  }

  // If product is rejected, send rejection notification
  if (req.body.status === 'rejected') {
    try {
      const NotificationService = require('../../services/notification.service');
      await NotificationService.createProductNotification({
        product,
        type: 'product_rejected',
        recipient: product.creator
      });
    } catch (error) {
      console.error('Error sending product rejection notification:', error);
    }
  }

  res.status(200).json({
    status: 'success',
    data: {
      product
    }
  });
});

/**
 * Get all bales (including pending and inactive ones)
 * @route GET /api/v1/admin/bales
 * @access Private (Admin only)
 */
exports.getAllBales = catchAsync(async (req, res) => {
  // Build query
  const queryObj = { ...req.query };
  const excludedFields = ['page', 'sort', 'limit', 'fields', 'search'];
  excludedFields.forEach(el => delete queryObj[el]);

  // Advanced filtering
  let queryStr = JSON.stringify(queryObj);
  queryStr = queryStr.replace(/\b(gte|gt|lte|lt)\b/g, match => `$${match}`);

  let query = Product.find({ ...JSON.parse(queryStr), type: 'bale' });

  // Search functionality
  if (req.query.search) {
    const searchRegex = new RegExp(req.query.search, 'i');
    query = query.find({
      $or: [
        { name: searchRegex },
        { description: searchRegex },
        { country: searchRegex }
      ]
    });
  }

  // Filter by status if specified
  if (req.query.status) {
    query = query.find({ status: req.query.status });
  }

  // Filter by creator if specified
  if (req.query.creator) {
    query = query.find({ creator: req.query.creator });
  }

  // Count total before applying pagination
  const total = await Product.countDocuments({ ...query.getQuery(), type: 'bale' });

  // Sorting
  if (req.query.sort) {
    const sortBy = req.query.sort.split(',').join(' ');
    query = query.sort(sortBy);
  } else {
    query = query.sort('-createdAt');
  }

  // Field limiting
  if (req.query.fields) {
    const fields = req.query.fields.split(',').join(' ');
    query = query.select(fields);
  } else {
    query = query.select('-__v');
  }

  // Pagination
  const page = req.query.page * 1 || 1;
  const limit = req.query.limit * 1 || 20;
  const skip = (page - 1) * limit;

  query = query.skip(skip).limit(limit);

  // Execute query
  const bales = await query;

  res.status(200).json({
    status: 'success',
    results: bales.length,
    total,
    page,
    limit,
    data: {
      bales
    }
  });
});

/**
 * Get bale by ID
 * @route GET /api/v1/admin/bales/:id
 * @access Private (Admin only)
 */
exports.getBale = catchAsync(async (req, res, next) => {
  const bale = await Product.findOne({ _id: req.params.id, type: 'bale' })
    .populate({
      path: 'creator',
      select: 'name email businessInfo.businessName businessInfo.businessAddress verificationStatus'
    })
    .populate({
      path: 'category',
      select: 'name description'
    })
    .populate({
      path: 'relatedCategories',
      select: 'name description'
    })
    .populate({
      path: 'products.product',
      select: 'name images basePrice status'
    });

  if (!bale) {
    return next(new AppError('No bale found with that ID', 404));
  }

  // Transform category data to include path array
  if (bale.category) {
    const pathArray = bale.category.name.split(' > ');
    bale.category._doc.pathArray = pathArray;
  }

  // Transform related categories to include path arrays
  if (bale.relatedCategories && bale.relatedCategories.length > 0) {
    bale.relatedCategories.forEach(category => {
      if (category) {
        const pathArray = category.name.split(' > ');
        category._doc.pathArray = pathArray;
      }
    });
  }

  // Get reviews for this bale
  const reviews = await Review.find({ product: bale._id })
    .select('+hidden')
    .sort('-createdAt');

  res.status(200).json({
    status: 'success',
    data: {
      bale,
      reviews
    }
  });
});

/**
 * Approve or reject a bale
 * @route PATCH /api/v1/admin/bales/:id/status
 * @access Private (Admin only)
 */
exports.updateBaleStatus = catchAsync(async (req, res, next) => {
  // Validate request body
  if (!req.body.status || !['pending', 'active', 'inactive', 'rejected'].includes(req.body.status)) {
    return next(new AppError('Please provide a valid status (pending, active, inactive, rejected)', 400));
  }

  // Find bale
  const bale = await Product.findOne({ _id: req.params.id, type: 'bale' });

  if (!bale) {
    return next(new AppError('No bale found with that ID', 404));
  }

  // Update bale status
  bale.status = req.body.status;

  // Add rejection reason if status is rejected
  if (req.body.status === 'rejected' && req.body.rejectionReason) {
    bale.rejectionReason = req.body.rejectionReason;
  }

  // Add approval notes if provided
  if (req.body.adminNotes) {
    bale.adminNotes = req.body.adminNotes;
  }

  // Save the updated bale
  await bale.save();

  // If bale is approved, update creator's bale count
  if (req.body.status === 'active') {
    await Creator.findByIdAndUpdate(bale.creator, {
      $inc: { 'metrics.totalBales': 1 }
    });

    // Send bale approved notification
    try {
      const NotificationService = require('../../services/notification.service');
      await NotificationService.createProductNotification({
        product: bale, // Using the product notification for bales too
        type: 'bale_approved',
        recipient: bale.creator
      });
    } catch (error) {
      console.error('Error sending bale approval notification:', error);
    }
  }

  // If bale is rejected, send rejection notification
  if (req.body.status === 'rejected') {
    try {
      const NotificationService = require('../../services/notification.service');
      await NotificationService.createProductNotification({
        product: bale, // Using the product notification for bales too
        type: 'bale_rejected',
        recipient: bale.creator
      });
    } catch (error) {
      console.error('Error sending bale rejection notification:', error);
    }
  }

  res.status(200).json({
    status: 'success',
    data: {
      bale
    }
  });
});

/**
 * Get product approval statistics
 * @route GET /api/v1/admin/products/stats
 * @access Private (Admin only)
 */
exports.getProductStats = catchAsync(async (_, res) => {
  const stats = await Product.aggregate([
    {
      $group: {
        _id: '$status',
        count: { $sum: 1 },
        avgPrice: { $avg: '$basePrice' }
      }
    },
    {
      $sort: { count: -1 }
    }
  ]);

  const totalProducts = await Product.countDocuments();
  const pendingProducts = await Product.countDocuments({ status: 'pending' });
  const activeProducts = await Product.countDocuments({ status: 'active' });
  const inactiveProducts = await Product.countDocuments({ status: 'inactive' });
  const rejectedProducts = await Product.countDocuments({ status: 'rejected' });

  res.status(200).json({
    status: 'success',
    data: {
      stats,
      summary: {
        total: totalProducts,
        pending: pendingProducts,
        active: activeProducts,
        inactive: inactiveProducts,
        rejected: rejectedProducts
      }
    }
  });
});

/**
 * Get bale approval statistics
 * @route GET /api/v1/admin/bales/stats
 * @access Private (Admin only)
 */
exports.getBaleStats = catchAsync(async (_, res) => {
  const stats = await Product.aggregate([
    { $match: { type: 'bale' } },
    {
      $group: {
        _id: '$status',
        count: { $sum: 1 },
        avgPrice: { $avg: '$basePrice' }
      }
    },
    {
      $sort: { count: -1 }
    }
  ]);

  const totalBales = await Product.countDocuments({ type: 'bale' });
  const pendingBales = await Product.countDocuments({ type: 'bale', status: 'pending' });
  const activeBales = await Product.countDocuments({ type: 'bale', status: 'active' });
  const inactiveBales = await Product.countDocuments({ type: 'bale', status: 'inactive' });
  const rejectedBales = await Product.countDocuments({ type: 'bale', status: 'rejected' });

  res.status(200).json({
    status: 'success',
    data: {
      stats,
      summary: {
        total: totalBales,
        pending: pendingBales,
        active: activeBales,
        inactive: inactiveBales,
        rejected: rejectedBales
      }
    }
  });
});

/**
 * Update a product
 * @route PATCH /api/v1/admin/products/:id
 * @access Private (Admin only)
 */
exports.updateProduct = catchAsync(async (req, res, next) => {
  const { id } = req.params;

  const product = await Product.findById(id);
  if (!product) return next(new AppError('No product found with that ID', 404));

  // Admin can update product in any status
  if (req.body.basePrice !== undefined && req.body.basePrice < 0) {
    return next(new AppError('Base price cannot be negative', 400));
  }

  if (req.body.gender) {
    const validGenders = ['Men', 'Women', 'Unisex', 'Boys', 'Girls'];
    if (!validGenders.includes(req.body.gender)) {
      return next(new AppError(`Gender must be one of: ${validGenders.join(', ')}`, 400));
    }
  }

  if (req.body.category && !mongoose.Types.ObjectId.isValid(req.body.category)) {
    return next(new AppError('Please provide a valid category ID', 400));
  }

  if (req.body.relatedCategories) {
    if (!Array.isArray(req.body.relatedCategories)) {
      return next(new AppError('Related categories must be an array', 400));
    }

    for (const categoryId of req.body.relatedCategories) {
      if (!mongoose.Types.ObjectId.isValid(categoryId)) {
        return next(new AppError('Please provide valid related category IDs', 400));
      }
    }
  }

  const updatedVariations = [];
  if (req.body.variations) {
    if (!Array.isArray(req.body.variations) || req.body.variations.length === 0) {
      return next(new AppError('Please provide at least one product variation', 400));
    }

    const existingVariationIds = product.variations.map(v => v._id.toString());

    for (const variation of req.body.variations) {
      if (variation._id) {
        if (!existingVariationIds.includes(variation._id.toString())) {
          return next(new AppError(`Variation with ID ${variation._id} not found in this product`, 404));
        }

        const existingVariation = product.variations.id(variation._id);

        Object.keys(variation).forEach(key => {
          if (key !== '_id') {
            if (key === 'quantity' && variation[key] < 0) {
              return next(new AppError('Quantity cannot be negative', 400));
            }
            if (key === 'price' && variation[key] < 0) {
              return next(new AppError('Price cannot be negative', 400));
            }
            existingVariation[key] = variation[key];
          }
        });

        updatedVariations.push(existingVariation);
      } else {
        const requiredFields = ['color', 'size', 'quantity', 'price'];
        for (const field of requiredFields) {
          if (!variation[field] && variation[field] !== 0) {
            return next(new AppError(`Please provide ${field} for each new variation`, 400));
          }
        }

        if (variation.quantity < 0) {
          return next(new AppError('Quantity cannot be negative', 400));
        }

        if (variation.price < 0) {
          return next(new AppError('Price cannot be negative', 400));
        }

        if (variation.salePrice && variation.salePrice >= variation.price) {
          return next(new AppError('Sale price must be lower than regular price', 400));
        }

        if (variation.salePrice) {
          if (!variation.saleStartDate || !variation.saleEndDate) {
            return next(new AppError('Sale start and end dates are required when sale price is provided', 400));
          }

          const start = new Date(variation.saleStartDate);
          const end = new Date(variation.saleEndDate);

          if (isNaN(start) || isNaN(end)) {
            return next(new AppError('Invalid sale dates', 400));
          }

          if (start >= end) {
            return next(new AppError('Sale end date must be after sale start date', 400));
          }
        }

        if (variation.images && !Array.isArray(variation.images)) {
          return next(new AppError('Variation images must be an array', 400));
        }

        updatedVariations.push(variation);
      }
    }

    const variationsToKeep = product.variations.filter(
      v => !req.body.variations.some(updateV => updateV._id && updateV._id.toString() === v._id.toString())
    );

    req.body.variations = [...variationsToKeep, ...updatedVariations];
  }

  if (req.body.specifications) {
    const specs = req.body.specifications;

    if (specs.fitType) {
      const validFits = ['Slim', 'Regular', 'Loose', 'Oversized', 'Tailored', 'Skinny', 'Straight', 'Relaxed'];
      if (!validFits.includes(specs.fitType)) {
        return next(new AppError(`Fit type must be one of: ${validFits.join(', ')}`, 400));
      }
    }

    const arrayFields = ['occasion', 'season'];
    arrayFields.forEach(field => {
      if (specs[field] && !Array.isArray(specs[field])) {
        return next(new AppError(`${field} must be an array`, 400));
      }
    });
  }

  if (req.body.highlights && !Array.isArray(req.body.highlights)) {
    return next(new AppError('Highlights must be an array', 400));
  }

  if (req.body.tags && !Array.isArray(req.body.tags)) {
    return next(new AppError('Tags must be an array', 400));
  }

  const filteredBody = {};
  // Add status to allowed fields for admin
  const allowedFields = ['name', 'brand', 'description', 'basePrice', 'category', 'relatedCategories', 'tags', 'gender', 'highlights', 'specifications', 'variations', 'images', 'seo', 'status'];

  for (const key in req.body) {
    if (allowedFields.includes(key)) {
      filteredBody[key] = req.body[key];
    }
  }

  // Admin can change status directly without restrictions
  if (req.body.status && product.status !== req.body.status) {
    // If product is being activated
    if (req.body.status === 'active') {
      await Creator.findByIdAndUpdate(product.creator, {
        $inc: { 'metrics.activeProducts': 1 }
      });
    }
    // If product is being deactivated from active status
    else if (product.status === 'active') {
      await Creator.findByIdAndUpdate(product.creator, {
        $inc: { 'metrics.activeProducts': -1 }
      });
    }
  }

  if (filteredBody.category && typeof filteredBody.category === 'string') {
    filteredBody.category = mongoose.Types.ObjectId.createFromHexString(filteredBody.category);
  }

  if (Array.isArray(filteredBody.relatedCategories)) {
    filteredBody.relatedCategories = filteredBody.relatedCategories.map(id =>
      typeof id === 'string' ? mongoose.Types.ObjectId.createFromHexString(id) : id
    );
  }

  const updatedProduct = await Product.findByIdAndUpdate(id, filteredBody, {
    new: true,
    runValidators: true
  }).populate({
    path: 'category',
    select: 'name description'
  })
  .populate({
    path: 'relatedCategories',
    select: 'name description'
  });;

  // Populate category
  // if (updatedProduct.category) {
  //   const category = await Category.findById(updatedProduct.category);
  //   if (category) {
  //     updatedProduct.category = {
  //       _id: category._id,
  //       name: category.name,
  //       description: category.description,
  //       pathArray: category.name.split(' > '),
  //       id: category._id
  //     };
  //   }
  // }

  // Populate related categories
  // if (updatedProduct.relatedCategories?.length > 0) {
  //   const populated = [];

  //   for (const id of updatedProduct.relatedCategories) {
  //     const cat = await Category.findById(id);
  //     if (cat) {
  //       populated.push({
  //         _id: cat._id,
  //         name: cat.name,
  //         description: cat.description,
  //         pathArray: cat.name.split(' > '),
  //         id: cat._id
  //       });
  //     }
  //   }

  //   updatedProduct.relatedCategories = populated;
  // }

  res.status(200).json({
    status: 'success',
    data: {
      product: updatedProduct
    }
  });
});

/**
 * Delete a product
 * @route DELETE /api/v1/admin/products/:id
 * @access Private (Admin only)
 */
exports.deleteProduct = catchAsync(async (req, res, next) => {
  const product = await Product.findById(req.params.id);

  if (!product) {
    return next(new AppError('No product found with that ID', 404));
  }

  // Update creator's metrics based on product status
  if (product.status === 'active') {
    await Creator.findByIdAndUpdate(product.creator, {
      $inc: {
        'metrics.activeProducts': -1,
        'metrics.totalProducts': -1
      }
    });
  } else if (product.status === 'pending') {
    await Creator.findByIdAndUpdate(product.creator, {
      $inc: { 'metrics.pendingProducts': -1 }
    });
  } else if (product.status === 'inactive') {
    await Creator.findByIdAndUpdate(product.creator, {
      $inc: { 'metrics.inactiveProducts': -1 }
    });
  }

  // Check if product is part of any active orders
  const hasActiveOrders = await Order.exists({
    'items.product': product._id,
    status: { $in: ['pending', 'processing', 'shipped'] }
  });

  if (hasActiveOrders) {
    return next(new AppError('Cannot delete product with active orders', 400));
  }

  // Delete associated reviews
  await Review.deleteMany({ product: product._id });

  // Delete the product
  await Product.findByIdAndDelete(req.params.id);


  res.status(204).json({
    status: 'success',
    data: null
  });
});

/**
 * Get all reviews for a product
 * @route GET /api/v1/admin/products/:id/reviews
 * @access Private (Admin only)
 */
exports.getProductReviews = catchAsync(async (req, res) => {
  // Build base query
  let query = { product: req.params.id };

  // Search functionality
  if (req.query.search) {
    const searchRegex = new RegExp(req.query.search, 'i');
    query = {
      ...query,
      $or: [
        { title: searchRegex },
        { review: searchRegex }
      ]
    };
  }

  // Filtering
  if (req.query.rating) {
    query.rating = parseInt(req.query.rating);
  }

  if (req.query.verified !== undefined) {
    query.verified = req.query.verified === 'true';
  }

  // Sorting
  let sortBy = '-createdAt'; // Default sort
  if (req.query.sort) {
    sortBy = req.query.sort.split(',').join(' ');
  }

  // Pagination
  const page = req.query.page * 1 || 1;
  const limit = req.query.limit * 1 || 20;
  const skip = (page - 1) * limit;

  // Get total count
  const total = await Review.countDocuments(query);

  const reviews = await Review.find(query)
    .populate({
      path: 'user',
      select: 'name'
    })
    .sort(sortBy)
    .skip(skip)
    .limit(limit);

  res.status(200).json({
    status: 'success',
    results: reviews.length,
    total,
    page,
    limit,
    totalPages: Math.ceil(total / limit),
    data: {
      reviews
    }
  });
});

/**
 * Get all products for a creator
 * @route GET /api/v1/admin/creators/:id/products
 * @access Private (Admin only)
 */
exports.getCreatorProducts = catchAsync(async (req, res, next) => {
  const creator = await Creator.findById(req.params.id);

  if (!creator) {
    return next(new AppError('No creator found with that ID', 404));
  }

  // Build base query
  let query = { creator: req.params.id };

  // Search functionality
  if (req.query.search) {
    const searchRegex = new RegExp(req.query.search, 'i');
    query = {
      ...query,
      $or: [
        { name: searchRegex },
        { description: searchRegex },
        { brand: searchRegex },
        { tags: searchRegex }
      ]
    };
  }

  // Filtering
  if (req.query.status) {
    query.status = req.query.status;
  }

  if (req.query.category) {
    query.category = req.query.category;
  }

  if (req.query.minPrice || req.query.maxPrice) {
    query.basePrice = {};
    if (req.query.minPrice) {
      query.basePrice.$gte = parseFloat(req.query.minPrice);
    }
    if (req.query.maxPrice) {
      query.basePrice.$lte = parseFloat(req.query.maxPrice);
    }
  }

  // Sorting
  let sortBy = '-createdAt'; // Default sort
  if (req.query.sort) {
    switch (req.query.sort) {
      case 'price-asc':
        sortBy = 'basePrice';
        break;
      case 'price-desc':
        sortBy = '-basePrice';
        break;
      case 'name-asc':
        sortBy = 'name';
        break;
      case 'name-desc':
        sortBy = '-name';
        break;
      case 'rating-desc':
        sortBy = '-ratingsAverage';
        break;
      default:
        sortBy = req.query.sort.split(',').join(' ');
    }
  }

  // Pagination
  const page = req.query.page * 1 || 1;
  const limit = req.query.limit * 1 || 20;
  const skip = (page - 1) * limit;

  // Get total count
  const total = await Product.countDocuments(query);

  const products = await Product.find(query)
    .sort(sortBy)
    .skip(skip)
    .limit(limit);

  res.status(200).json({
    status: 'success',
    results: products.length,
    total,
    page,
    limit,
    totalPages: Math.ceil(total / limit),
    data: {
      products
    }
  });
});

/**
 * Update bale by ID (Admin version)
 * @route PATCH /api/v1/admin/bales/:id
 * @access Private (Admin only)
 */
exports.updateBale = catchAsync(async (req, res, next) => {
  const { id } = req.params;

  // Find bale
  const bale = await Product.findOne({ _id: id, type: 'bale' });
  if (!bale) return next(new AppError('No bale found with that ID', 404));

  // Validate basePrice if provided
  if (req.body.basePrice !== undefined && req.body.basePrice < 0) {
    return next(new AppError('Base price cannot be negative', 400));
  }

  // Validate totalItems if provided
  if (req.body.totalItems !== undefined && req.body.totalItems < 1) {
    return next(new AppError('Total items must be at least 1', 400));
  }

  // Validate weight if provided
  if (req.body.weight !== undefined && req.body.weight <= 0) {
    return next(new AppError('Weight must be greater than 0', 400));
  }

  // Validate country if provided
  if (req.body.country && (typeof req.body.country !== 'string' || req.body.country.trim() === '')) {
    return next(new AppError('Please provide a valid country of origin', 400));
  }

  // Validate condition if provided
  if (req.body.condition) {
    const validConditions = ['New', 'Like New', 'Good', 'Fair', 'Poor'];
    if (!validConditions.includes(req.body.condition)) {
      return next(new AppError(`Condition must be one of: ${validConditions.join(', ')}`, 400));
    }
  }

  // Validate category if provided
  if (req.body.category && !mongoose.Types.ObjectId.isValid(req.body.category)) {
    return next(new AppError('Please provide a valid category ID', 400));
  }

  // Validate related categories if provided
  if (req.body.relatedCategories) {
    if (!Array.isArray(req.body.relatedCategories)) {
      return next(new AppError('Related categories must be an array', 400));
    }

    for (const categoryId of req.body.relatedCategories) {
      if (!mongoose.Types.ObjectId.isValid(categoryId)) {
        return next(new AppError('Please provide valid related category IDs', 400));
      }
    }
  }

  // Handle variations update
  const updatedVariations = [];
  if (req.body.variations) {
    if (!Array.isArray(req.body.variations) || req.body.variations.length === 0) {
      return next(new AppError('Please provide at least one bale variation', 400));
    }

    const existingVariationIds = bale.variations.map(v => v._id.toString());

    for (const variation of req.body.variations) {
      if (variation._id) {
        if (!existingVariationIds.includes(variation._id.toString())) {
          return next(new AppError(`Variation with ID ${variation._id} not found in this bale`, 404));
        }

        const existingVariation = bale.variations.id(variation._id);

        Object.keys(variation).forEach(key => {
          if (key !== '_id') {
            if (key === 'quantity' && variation[key] < 0) {
              return next(new AppError('Quantity cannot be negative', 400));
            }
            if (key === 'price' && variation[key] < 0) {
              return next(new AppError('Price cannot be negative', 400));
            }
            existingVariation[key] = variation[key];
          }
        });

        updatedVariations.push(existingVariation);
      } else {
        const requiredFields = ['size', 'quantity', 'price'];
        for (const field of requiredFields) {
          if (!variation[field] && variation[field] !== 0) {
            return next(new AppError(`Please provide ${field} for each new variation`, 400));
          }
        }

        // Validate new variation fields
        if (variation.quantity < 0) {
          return next(new AppError('Quantity cannot be negative', 400));
        }

        if (variation.price < 0) {
          return next(new AppError('Price cannot be negative', 400));
        }

        if (variation.salePrice && variation.salePrice >= variation.price) {
          return next(new AppError('Sale price must be lower than regular price', 400));
        }

        if (variation.salePrice && (!variation.saleStartDate || !variation.saleEndDate)) {
          return next(new AppError('Sale start date and end date are required when sale price is provided', 400));
        }

        if (variation.salePrice) {
          const startDate = new Date(variation.saleStartDate);
          const endDate = new Date(variation.saleEndDate);

          if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
            return next(new AppError('Invalid sale dates', 400));
          }

          if (startDate >= endDate) {
            return next(new AppError('Sale end date must be after sale start date', 400));
          }
        }

        updatedVariations.push(variation);
      }
    }

    // Keep existing variations that weren't updated
    const variationsToKeep = bale.variations.filter(
      v => !req.body.variations.some(updateV => updateV._id && updateV._id.toString() === v._id.toString())
    );

    req.body.variations = [...variationsToKeep, ...updatedVariations];
  }

  // Validate products in the bale if provided
  if (req.body.products && req.body.products.length > 0) {
    // Check if all products have product and quantity
    for (const item of req.body.products) {
      if (!item.product || !item.quantity) {
        return next(new AppError('Each product in the bale must have a product ID and quantity', 400));
      }
      if (item.quantity < 1) {
        return next(new AppError('Product quantity must be at least 1', 400));
      }
      if (!mongoose.Types.ObjectId.isValid(item.product)) {
        return next(new AppError('Please provide valid product IDs', 400));
      }
    }

    // Check if products exist
    const productIds = req.body.products.map(item => item.product);
    const products = await Product.find({ _id: { $in: productIds } });

    if (products.length !== productIds.length) {
      return next(new AppError('Some products do not exist', 400));
    }
  }

  // Create a filtered body to prevent unwanted fields
  const filteredBody = {};
  const allowedFields = [
    'name', 'description', 'basePrice', 'category', 'relatedCategories',
    'tags', 'products', 'country', 'totalItems', 'weight', 'dimensions',
    'condition', 'variations', 'highlights', 'images', 'seo', 'status'
  ];

  Object.keys(req.body).forEach(key => {
    if (allowedFields.includes(key)) {
      filteredBody[key] = req.body[key];
    }
  });

  // Handle status changes and update creator metrics
  if (req.body.status && req.body.status !== bale.status) {
    const oldStatus = bale.status;
    const newStatus = req.body.status;

    // Update creator metrics based on status change
    const updates = {};

    if (oldStatus === 'pending') updates['metrics.pendingBales'] = -1;
    if (oldStatus === 'active') updates['metrics.activeBales'] = -1;
    if (oldStatus === 'inactive') updates['metrics.inactiveBales'] = -1;

    if (newStatus === 'pending') updates['metrics.pendingBales'] = 1;
    if (newStatus === 'active') updates['metrics.activeBales'] = 1;
    if (newStatus === 'inactive') updates['metrics.inactiveBales'] = 1;

    if (Object.keys(updates).length > 0) {
      await Creator.findByIdAndUpdate(bale.creator, { $inc: updates });
    }
  }

  // Ensure ObjectId conversions
  if (filteredBody.category && typeof filteredBody.category === 'string') {
    filteredBody.category = new mongoose.Types.ObjectId(filteredBody.category);
  }

  if (filteredBody.relatedCategories && Array.isArray(filteredBody.relatedCategories)) {
    filteredBody.relatedCategories = filteredBody.relatedCategories.map(id => {
      if (typeof id === 'string') {
        return new mongoose.Types.ObjectId(id);
      }
      return id;
    });
  }

  if (filteredBody.products && Array.isArray(filteredBody.products)) {
    filteredBody.products = filteredBody.products.map(item => ({
      ...item,
      product: typeof item.product === 'string' ? new mongoose.Types.ObjectId(item.product) : item.product
    }));
  }

  // Update the bale
  const updatedBale = await Product.findByIdAndUpdate(id, filteredBody, {
    new: true,
    runValidators: true
  });


  // Populate category and relatedCategories
  if (updatedBale.category) {
    const category = await Category.findById(updatedBale.category);
    if (category) {
      const pathArray = category.name.split(' > ');
      const actualCategoryName = pathArray[pathArray.length - 1];

      updatedBale.category = {
        _id: category._id,
        name: actualCategoryName,
        description: category.description,
        pathArray: pathArray,
        id: category._id
      };
    }
  }

  if (updatedBale.relatedCategories?.length > 0) {
    const populated = [];

    for (const id of updatedBale.relatedCategories) {
      const cat = await Category.findById(id);
      if (cat) {
        const pathArray = cat.name.split(' > ');
        const actualCategoryName = pathArray[pathArray.length - 1];

        populated.push({
          _id: cat._id,
          name: actualCategoryName,
          description: cat.description,
          pathArray: pathArray,
          id: cat._id
        });
      }
    }

    updatedBale.relatedCategories = populated;
  }

  res.status(200).json({
    status: 'success',
    data: {
      bale: updatedBale
    }
  });
});

/**
 * Delete a bale (Admin version)
 * @route DELETE /api/v1/admin/bales/:id
 * @access Private (Admin only)
 */
exports.deleteBale = catchAsync(async (req, res, next) => {
  // Find bale without creator restriction
  const bale = await Product.findOne({ _id: req.params.id, type: 'bale' });

  if (!bale) {
    return next(new AppError('No bale found with that ID', 404));
  }

  // Admin can delete bales in any status
  // But update metrics only for relevant statuses
  if (bale.status === 'active') {
    await Creator.findByIdAndUpdate(bale.creator, {
      $inc: {
        'metrics.activeBales': -1,
        'metrics.totalBales': -1
      }
    });
  } else if (bale.status === 'pending') {
    await Creator.findByIdAndUpdate(bale.creator, {
      $inc: { 'metrics.pendingBales': -1 }
    });
  } else if (bale.status === 'inactive') {
    await Creator.findByIdAndUpdate(bale.creator, {
      $inc: { 'metrics.inactiveBales': -1 }
    });
  }

  // Check if bale is part of any active orders
  const hasActiveOrders = await Order.exists({
    'items.product': bale._id,
    status: { $in: ['pending', 'processing', 'shipped'] }
  });

  if (hasActiveOrders) {
    return next(new AppError('Cannot delete bale with active orders', 400));
  }

  // Delete associated reviews
  await Review.deleteMany({ product: bale._id });

  // Delete the bale
  await Product.findByIdAndDelete(req.params.id);


  res.status(204).json({
    status: 'success',
    data: null
  });
});

/**
 * Get all reviews for a bale
 * @route GET /api/v1/admin/bales/:id/reviews
 * @access Private (Admin only)
 */
exports.getBaleReviews = catchAsync(async (req, res) => {
  // Build base query
  let query = { bale: req.params.id };

  // Search functionality
  if (req.query.search) {
    const searchRegex = new RegExp(req.query.search, 'i');
    query = {
      ...query,
      $or: [
        { title: searchRegex },
        { review: searchRegex }
      ]
    };
  }

  // Filtering
  if (req.query.rating) {
    query.rating = parseInt(req.query.rating);
  }

  if (req.query.verified !== undefined) {
    query.verified = req.query.verified === 'true';
  }

  if (req.query.hidden !== undefined) {
    query.hidden = req.query.hidden === 'true';
  }

  // Sorting
  let sortBy = '-createdAt'; // Default sort
  if (req.query.sort) {
    sortBy = req.query.sort.split(',').join(' ');
  }

  // Pagination
  const page = req.query.page * 1 || 1;
  const limit = req.query.limit * 1 || 20;
  const skip = (page - 1) * limit;

  // Get total count
  const total = await Review.countDocuments(query);

  const reviews = await Review.find(query)
    .populate({
      path: 'user',
      select: 'name photo'
    })
    .select('+hidden')
    .sort(sortBy)
    .skip(skip)
    .limit(limit);

  res.status(200).json({
    status: 'success',
    results: reviews.length,
    total,
    page,
    limit,
    totalPages: Math.ceil(total / limit),
    data: {
      reviews
    }
  });
});

/**
 * Get all bales for a creator
 * @route GET /api/v1/admin/creators/:id/bales
 * @access Private (Admin only)
 */
exports.getCreatorBales = catchAsync(async (req, res, next) => {
  const creator = await Creator.findById(req.params.id);

  if (!creator) {
    return next(new AppError('No creator found with that ID', 404));
  }

  // Build base query
  let query = { creator: req.params.id };

  // Search functionality
  if (req.query.search) {
    const searchRegex = new RegExp(req.query.search, 'i');
    query = {
      ...query,
      $or: [
        { name: searchRegex },
        { description: searchRegex },
        { country: searchRegex },
        { tags: searchRegex }
      ]
    };
  }

  // Filtering
  if (req.query.status) {
    query.status = req.query.status;
  }

  if (req.query.category) {
    query.category = req.query.category;
  }

  if (req.query.minPrice || req.query.maxPrice) {
    query.basePrice = {};
    if (req.query.minPrice) {
      query.basePrice.$gte = parseFloat(req.query.minPrice);
    }
    if (req.query.maxPrice) {
      query.basePrice.$lte = parseFloat(req.query.maxPrice);
    }
  }

  // Sorting
  let sortBy = '-createdAt'; // Default sort
  if (req.query.sort) {
    switch (req.query.sort) {
      case 'price-asc':
        sortBy = 'basePrice';
        break;
      case 'price-desc':
        sortBy = '-basePrice';
        break;
      case 'name-asc':
        sortBy = 'name';
        break;
      case 'name-desc':
        sortBy = '-name';
        break;
      case 'rating-desc':
        sortBy = '-ratingsAverage';
        break;
      default:
        sortBy = req.query.sort.split(',').join(' ');
    }
  }

  // Pagination
  const page = req.query.page * 1 || 1;
  const limit = req.query.limit * 1 || 20;
  const skip = (page - 1) * limit;

  // Get total count
  const total = await Product.countDocuments({ ...query, type: 'bale' });

  const bales = await Product.find({ ...query, type: 'bale' })
    .populate({
      path: 'category',
      select: 'name description'
    })
    .sort(sortBy)
    .skip(skip)
    .limit(limit);

  // Transform category data to include path array
  for (const bale of bales) {
    if (bale.category) {
      const pathArray = bale.category.name.split(' > ');
      bale.category._doc.pathArray = pathArray;
    }
  }

  res.status(200).json({
    status: 'success',
    results: bales.length,
    total,
    page,
    limit,
    totalPages: Math.ceil(total / limit),
    data: {
      bales
    }
  });
});






