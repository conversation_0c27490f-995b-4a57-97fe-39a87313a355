const mongoose = require('mongoose');
const Order = require('../models/order.model');
const Payment = require('../models/payment.model');
const Payout = require('../models/payout.model');

const FinancialReportService = {
  // Get platform revenue summary
  getPlatformRevenue: async function(startDate, endDate) {
    const match = {
      createdAt: {
        $gte: new Date(startDate),
        $lte: new Date(endDate)
      },
      status: { $in: ['processing', 'shipped', 'delivered'] },
      isPaid: true
    };
    
    const result = await Order.aggregate([
      { $match: match },
      {
        $group: {
          _id: null,
          totalOrders: { $sum: 1 },
          totalOrderValue: { $sum: '$total' },
          totalPlatformFees: { $sum: '$fees.platform.amount' },
          totalProcessingFees: { $sum: '$fees.processing.totalAmount' },
          totalShippingFees: { $sum: '$fees.shipping.totalAmount' },
          totalFees: { $sum: '$fees.total' }
        }
      }
    ]);
    
    return result[0] || {
      totalOrders: 0,
      totalOrderValue: 0,
      totalPlatformFees: 0,
      totalProcessingFees: 0,
      totalShippingFees: 0,
      totalFees: 0
    };
  },
  
  // Get revenue breakdown by day/week/month
  getRevenueBreakdown: async function(startDate, endDate, interval = 'day') {
    const match = {
      createdAt: {
        $gte: new Date(startDate),
        $lte: new Date(endDate)
      },
      status: { $in: ['processing', 'shipped', 'delivered'] },
      isPaid: true
    };
    
    let dateFormat;
    if (interval === 'day') {
      dateFormat = { $dateToString: { format: '%Y-%m-%d', date: '$createdAt' } };
    } else if (interval === 'week') {
      dateFormat = { 
        $dateToString: { 
          format: '%Y-W%U', 
          date: '$createdAt' 
        } 
      };
    } else if (interval === 'month') {
      dateFormat = { $dateToString: { format: '%Y-%m', date: '$createdAt' } };
    }
    
    return await Order.aggregate([
      { $match: match },
      {
        $group: {
          _id: dateFormat,
          totalOrders: { $sum: 1 },
          totalOrderValue: { $sum: '$total' },
          totalPlatformFees: { $sum: '$fees.platform.amount' },
          totalProcessingFees: { $sum: '$fees.processing.totalAmount' },
          totalShippingFees: { $sum: '$fees.shipping.totalAmount' }
        }
      },
      { $sort: { _id: 1 } }
    ]);
  },
  
  // Get creator earnings summary
  getCreatorEarnings: async function(startDate, endDate, creatorId = null) {
    const match = {
      createdAt: {
        $gte: new Date(startDate),
        $lte: new Date(endDate)
      },
      status: { $in: ['processing', 'shipped', 'delivered'] },
      isPaid: true
    };
    
    if (creatorId) {
      match['items.creator'] = mongoose.Types.ObjectId(creatorId);
    }
    
    // First unwind items to calculate per creator
    const result = await Order.aggregate([
      { $match: match },
      { $unwind: '$items' },
      {
        $group: {
          _id: '$items.creator',
          totalOrders: { $sum: 1 },
          totalSales: { $sum: { $multiply: ['$items.price', '$items.quantity'] } },
          totalItems: { $sum: '$items.quantity' },
          // Estimate platform fees (assuming proportional to item value)
          estimatedPlatformFees: { 
            $sum: { 
              $multiply: [
                { $divide: [{ $multiply: ['$items.price', '$items.quantity'] }, '$subtotal'] },
                '$fees.platform.amount'
              ] 
            } 
          }
        }
      },
      {
        $lookup: {
          from: 'users',
          localField: '_id',
          foreignField: '_id',
          as: 'creatorDetails'
        }
      },
      {
        $addFields: {
          creatorName: { $arrayElemAt: ['$creatorDetails.name', 0] },
          netEarnings: { $subtract: ['$totalSales', '$estimatedPlatformFees'] }
        }
      },
      {
        $project: {
          creatorDetails: 0
        }
      },
      { $sort: { totalSales: -1 } }
    ]);
    
    return result;
  },
  
  // Get shipping fee statistics
  getShippingStats: async function(startDate, endDate) {
    const match = {
      createdAt: {
        $gte: new Date(startDate),
        $lte: new Date(endDate)
      },
      status: { $in: ['processing', 'shipped', 'delivered'] },
      isPaid: true
    };
    
    // Group by shipping address country and region
    return await Order.aggregate([
      { $match: match },
      {
        $group: {
          _id: {
            country: '$shippingAddress.country',
            region: '$shippingAddress.region'
          },
          totalOrders: { $sum: 1 },
          totalShippingFees: { $sum: '$fees.shipping.totalAmount' },
          averageShippingFee: { $avg: '$fees.shipping.totalAmount' }
        }
      },
      {
        $project: {
          country: '$_id.country',
          region: '$_id.region',
          totalOrders: 1,
          totalShippingFees: 1,
          averageShippingFee: 1,
          _id: 0
        }
      },
      { $sort: { totalShippingFees: -1 } }
    ]);
  },
  
  // Get payment method statistics
  getPaymentMethodStats: async function(startDate, endDate) {
    const match = {
      createdAt: {
        $gte: new Date(startDate),
        $lte: new Date(endDate)
      },
      status: 'success'
    };
    
    return await Payment.aggregate([
      { $match: match },
      {
        $group: {
          _id: '$channel',
          totalPayments: { $sum: 1 },
          totalAmount: { $sum: '$amount' },
          totalProcessingFees: { $sum: '$fees.processingFee.totalAmount' }
        }
      },
      {
        $project: {
          paymentMethod: '$_id',
          totalPayments: 1,
          totalAmount: 1,
          totalProcessingFees: 1,
          averageProcessingFee: { $divide: ['$totalProcessingFees', '$totalPayments'] },
          feePercentage: { 
            $multiply: [
              { $divide: ['$totalProcessingFees', '$totalAmount'] },
              100
            ]
          },
          _id: 0
        }
      },
      { $sort: { totalAmount: -1 } }
    ]);
  },
  
  // Get payout summary
  getPayoutSummary: async function(startDate, endDate) {
    const match = {
      createdAt: {
        $gte: new Date(startDate),
        $lte: new Date(endDate)
      }
    };
    
    return await Payout.aggregate([
      { $match: match },
      {
        $group: {
          _id: '$status',
          count: { $sum: 1 },
          totalAmount: { $sum: '$amount' },
          totalPlatformFees: { $sum: '$fees.platform.amount' },
          totalProcessingFees: { $sum: '$fees.processing.totalAmount' }
        }
      },
      {
        $project: {
          status: '$_id',
          count: 1,
          totalAmount: 1,
          totalPlatformFees: 1,
          totalProcessingFees: 1,
          _id: 0
        }
      },
      { $sort: { totalAmount: -1 } }
    ]);
  },
  
  // Get overall financial summary
  getFinancialSummary: async function(startDate, endDate) {
    const [revenue, payouts, paymentStats, shippingStats] = await Promise.all([
      this.getPlatformRevenue(startDate, endDate),
      this.getPayoutSummary(startDate, endDate),
      this.getPaymentMethodStats(startDate, endDate),
      this.getShippingStats(startDate, endDate)
    ]);
    
    // Calculate payout totals
    const paidPayouts = payouts.find(p => p.status === 'paid') || { totalAmount: 0 };
    const pendingPayouts = payouts.find(p => p.status === 'pending') || { totalAmount: 0 };
    
    // Calculate total processing fees by payment method
    const processingFeesByMethod = paymentStats.reduce((acc, method) => {
      acc[method.paymentMethod] = method.totalProcessingFees;
      return acc;
    }, {});
    
    // Calculate total shipping fees by country
    const shippingFeesByCountry = shippingStats.reduce((acc, region) => {
      const country = region.country || 'Unknown';
      if (!acc[country]) acc[country] = 0;
      acc[country] += region.totalShippingFees;
      return acc;
    }, {});
    
    return {
      period: {
        startDate,
        endDate
      },
      revenue: {
        totalOrders: revenue.totalOrders,
        totalOrderValue: revenue.totalOrderValue,
        totalPlatformFees: revenue.totalPlatformFees,
        totalProcessingFees: revenue.totalProcessingFees,
        totalShippingFees: revenue.totalShippingFees
      },
      payouts: {
        paid: paidPayouts.totalAmount,
        pending: pendingPayouts.totalAmount
      },
      processingFees: {
        total: revenue.totalProcessingFees,
        byMethod: processingFeesByMethod
      },
      shippingFees: {
        total: revenue.totalShippingFees,
        byCountry: shippingFeesByCountry
      },
      profitability: {
        grossRevenue: revenue.totalPlatformFees,
        processingCosts: revenue.totalProcessingFees,
        netRevenue: revenue.totalPlatformFees - revenue.totalProcessingFees
      }
    };
  }
};

module.exports = FinancialReportService;
