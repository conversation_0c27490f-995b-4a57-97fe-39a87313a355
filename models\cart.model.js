const mongoose = require('mongoose');
const Product = require('./product.model');

const cartSchema = new mongoose.Schema(
  {
    user: {
      type: mongoose.Schema.ObjectId,
      ref: 'User',
      required: [true, 'Cart must belong to a user']
    },
    products: [
      {
        product: {
          type: mongoose.Schema.ObjectId,
          ref: 'Product',
          required: [true, 'Product ID is required']
        },
        variationId: String,
        quantity: {
          type: Number,
          default: 1,
          min: [1, 'Quantity must be at least 1']
        },
        addedAt: {
          type: Date,
          default: Date.now
        },
        creator: {
          type: mongoose.Schema.ObjectId,
          ref: 'User'
        }
      }
    ],
    subtotal: {
      type: Number,
      default: 0
    },
    total: {
      type: Number,
      default: 0
    }
  },
  {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true }
  }
);

// Indexes
cartSchema.index({ user: 1 });

// Query middleware
cartSchema.pre(/^find/, function(next) {
  this.populate({
    path: 'products.product',
    select: 'name images variations basePrice formattedPriceRange status'
  }).populate({
    path: 'bales.bale',
    select: 'name images variations basePrice formattedPriceRange status '
  }).populate({
    path: 'products.creator bales.creator',
    select: 'shopInfo.name shopInfo.logo businessInfo.businessName name photo verificationStatus metrics.qualityScore'
  });

  next();
});

// Virtual to get variation details for each product item
cartSchema.virtual('itemsWithVariationDetails').get(function() {
  const items = [];
  
  // Process products with variation details
  for (const item of this.products) {
    if (!item.product) continue;
    
    const variation = item.product.variations.id(item.variationId);
    if (!variation) continue;
    
    items.push({
      _id: item._id,
      type: 'product',
      product: {
        _id: item.product._id,
        name: item.product.name,
        images: item.product.images,
        status: item.product.status
      },
      variation: {
        _id: variation._id,
        color: variation.color,
        size: variation.size,
        price: variation.price,
        currentPrice: variation.currentPrice,
        hasDiscount: variation.hasDiscount,
        discountPercentage: variation.discountPercentage,
        discountSource: variation.discountSource,
        discountEndDate: variation.discountEndDate,
        quantity: variation.quantity,
        images: variation.images || item.product.images
      },
      creator: item.creator,
      quantity: item.quantity,
      addedAt: item.addedAt,
      itemTotal: variation.currentPrice * item.quantity
    });
  }
  
  // Process bales with variation details
  for (const item of this.bales) {
    if (!item.bale) continue;
    
    const variation = item.bale.variations.id(item.variationId);
    if (!variation) continue;
    
    items.push({
      _id: item._id,
      type: 'bale',
      bale: {
        _id: item.bale._id,
        name: item.bale.name,
        images: item.bale.images,
        status: item.bale.status
      },
      variation: {
        _id: variation._id,
        size: variation.size,
        price: variation.price,
        currentPrice: variation.currentPrice || variation.onSale ? variation.salePrice : variation.price,
        hasDiscount: variation.onSale,
        discountPercentage: variation.onSale ? Math.round(((variation.price - variation.salePrice) / variation.price) * 100) : 0,
        discountSource: variation.onSale ? 'sale' : null,
        discountEndDate: variation.onSale ? variation.saleEndDate : null,
        quantity: variation.quantity
      },
      creator: item.creator,
      quantity: item.quantity,
      addedAt: item.addedAt,
      itemTotal: (variation.currentPrice || (variation.onSale ? variation.salePrice : variation.price)) * item.quantity
    });
  }
  
  return items;
});

// Method to calculate cart totals
cartSchema.methods.calculateTotals = async function() {
  let subtotal = 0;
  
  // Calculate from products
  for (const item of this.products) {
    const product = await Product.findById(item.product);
    
    if (product && product.variations) {
      const variation = product.variations.id(item.variationId);
      if (variation) {
        // Use the currentPrice virtual property
        subtotal += variation.currentPrice * item.quantity;
      }
    }
  }
  
  // Calculate from bales
  for (const item of this.bales) {
    const bale = await Bale.findById(item.bale);
    
    if (bale && bale.variations) {
      const variation = bale.variations.id(item.variationId);
      if (variation) {
        // Use the currentPrice virtual property
        subtotal += variation.currentPrice * item.quantity;
      }
    }
  }
  
  this.subtotal = subtotal;
  this.total = subtotal;
  
  
  return this.save();
};

// Method to add product to cart
cartSchema.methods.addProduct = async function(productData) {
  const { productId, variationId, quantity } = productData;
  
  // Ensure productId is a string for consistent comparison
  const productIdStr = productId.toString();
  
  // Check if product already exists in cart with same variation
  const existingProductIndex = this.products.findIndex(
    item => {
      const itemProductId = item.product._id ? item.product._id.toString() : item.product.toString();
      return itemProductId === productIdStr && item.variationId?.toString() === variationId?.toString();
    }
  );
  
  if (existingProductIndex > -1) {
    // Update existing product quantity
    this.products[existingProductIndex].quantity += quantity;
  } else {
    // Add new product
    const newProduct = {
      product: productId,
      variationId,
      quantity,
      addedAt: Date.now()
    };
    
    // Get creator from product
    const product = await Product.findById(productId);
    if (product) {
      newProduct.creator = product.creator;
    }
    
    this.products.push(newProduct);
  }
  
  return this.calculateTotals();
};

// Note: Bale functionality is now handled through addProduct method
// since bales are now products with type: 'bale'

// Method to remove product from cart
cartSchema.methods.removeProduct = function(productId, variationId) {
  const productIdStr = productId.toString();
  
  this.products = this.products.filter(item => {
    const itemProductId = item.product._id ? item.product._id.toString() : item.product.toString();
    return !(itemProductId === productIdStr && item.variationId?.toString() === variationId?.toString());
  });
  
  return this.calculateTotals();
};

// Note: Bale removal is now handled through removeProduct method
// since bales are now products with type: 'bale'

// Method to update product quantity
cartSchema.methods.updateProductQuantity = function(productId, variationId, quantity) {
  const productIdStr = productId.toString();
  
  const product = this.products.find(item => {
    const itemProductId = item.product._id ? item.product._id.toString() : item.product.toString();
    return itemProductId === productIdStr && item.variationId?.toString() === variationId?.toString();
  });
  
  if (product) {
    product.quantity = quantity;
  }
  
  return this.calculateTotals();
};

// Note: Bale quantity updates are now handled through updateProductQuantity method
// since bales are now products with type: 'bale'

// Method to clear cart
cartSchema.methods.clearCart = function() {
  this.products = [];
  this.appliedPromotion = null;
  this.subtotal = 0;
  this.total = 0;

  return this.save();
};

const Cart = mongoose.model('Cart', cartSchema);

module.exports = Cart;



