const WalletService = require('../../services/wallet.service');
const AppError = require('../../utils/appError');
const catchAsync = require('../../utils/catchAsync');

/**
 * Get buyer wallet balance
 * @route GET /api/v1/buyers/wallet/balance
 * @access Private (Buyer only)
 */
exports.getWalletBalance = catchAsync(async (req, res, next) => {
  // Get wallet balance
  const balance = await WalletService.getWalletBalance(req.user.id, 'buyer');

  res.status(200).json({
    status: 'success',
    data: {
      balance
    }
  });
});

/**
 * Get buyer wallet transactions
 * @route GET /api/v1/buyers/wallet/transactions
 * @access Private (Buyer only)
 */
exports.getWalletTransactions = catchAsync(async (req, res, next) => {
  // Parse query parameters
  const options = {
    limit: req.query.limit ? parseInt(req.query.limit) : 10,
    skip: req.query.page ? (parseInt(req.query.page) - 1) * (req.query.limit ? parseInt(req.query.limit) : 10) : 0,
    type: req.query.type,
    status: req.query.status,
    startDate: req.query.startDate,
    endDate: req.query.endDate
  };

  // Get wallet transactions
  const transactions = await WalletService.getWalletTransactions(
    req.user.id,
    'buyer',
    options
  );

  res.status(200).json({
    status: 'success',
    results: transactions.length,
    data: {
      transactions
    }
  });
});

/**
 * Get buyer wallet transaction summary
 * @route GET /api/v1/buyers/wallet/summary
 * @access Private (Buyer only)
 */
exports.getWalletTransactionSummary = catchAsync(async (req, res, next) => {
  // Get wallet transaction summary
  const summary = await WalletService.getWalletTransactionSummary(
    req.user.id,
    'buyer'
  );

  res.status(200).json({
    status: 'success',
    data: {
      summary
    }
  });
});
