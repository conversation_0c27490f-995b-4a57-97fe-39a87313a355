const express = require('express');
const buyerController = require('../../controllers/admin/buyer.controller');
const authMiddleware = require('../../middleware/auth.middleware');

const router = express.Router();

// Protect all routes
router.use(authMiddleware.protect);
router.use(authMiddleware.restrictTo('admin'));

// Buyer routes
router.get('/', buyerController.getAllBuyers);
router.get('/:id', buyerController.getBuyer);
router.patch('/:id/details', buyerController.updateBuyerDetails);
router.patch('/:id/status', buyerController.updateBuyerStatus);
router.patch('/:id/preferences', buyerController.updateBuyerPreferences);
router.post('/:id/reset-password', buyerController.resetBuyerPassword);
router.delete('/:id', buyerController.deleteBuyer);



module.exports = router;
