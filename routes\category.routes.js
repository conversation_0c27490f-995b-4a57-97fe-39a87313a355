const express = require('express');
const categoryController = require('../controllers/public/category.controller');

const router = express.Router();

// Public category routes
router.get('/search', categoryController.searchCategories);
router.get('/featured', categoryController.getFeaturedCategories);
router.get('/hierarchy', categoryController.getCategoriesHierarchy);
router.get('/tree', categoryController.getCategoryTree);
router.get('/slug/:slug', categoryController.getCategoryBySlug);
router.get('/:identifier/with-children', categoryController.getCategoryWithChildren);
router.get('/', categoryController.getAllCategories);
router.get('/:identifier', categoryController.getCategory);

module.exports = router;
