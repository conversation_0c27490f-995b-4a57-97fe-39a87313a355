{"info": {"_postman_id": "public-categories-2024", "name": "Public Categories API", "description": "Test collection for public category endpoints - no authentication required", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "variable": [{"key": "base_url", "value": "http://localhost:3000/api/v1", "type": "string"}, {"key": "category_id", "value": "", "type": "string"}, {"key": "category_slug", "value": "", "type": "string"}], "event": [{"listen": "prerequest", "script": {"exec": ["// Global pre-request script for public endpoints", "console.log('Testing public category endpoint:', pm.request.url);"], "type": "text/javascript"}}, {"listen": "test", "script": {"exec": ["// Global test script", "pm.test('Response time is less than 2000ms', function () {", "    pm.expect(pm.response.responseTime).to.be.below(2000);", "});", "", "pm.test('Response has correct content type', function () {", "    pm.expect(pm.response.headers.get('Content-Type')).to.include('application/json');", "});", "", "pm.test('Response has success status', function () {", "    const response = pm.response.json();", "    pm.expect(response.status).to.eql('success');", "});"], "type": "text/javascript"}}], "item": [{"name": "Get All Categories", "event": [{"listen": "test", "script": {"exec": ["pm.test('Categories retrieved successfully', function () {", "    pm.response.to.have.status(200);", "    const response = pm.response.json();", "    pm.expect(response.data.categories).to.be.an('array');", "    pm.expect(response.results).to.be.a('number');", "    ", "    // Store first category for subsequent tests", "    if (response.data.categories.length > 0) {", "        const firstCategory = response.data.categories[0];", "        pm.environment.set('category_id', firstCategory._id);", "        pm.environment.set('category_slug', firstCategory.slug);", "    }", "});", "", "pm.test('Categories have required fields', function () {", "    const response = pm.response.json();", "    if (response.data.categories.length > 0) {", "        const category = response.data.categories[0];", "        pm.expect(category).to.have.property('_id');", "        pm.expect(category).to.have.property('name');", "        pm.expect(category).to.have.property('slug');", "    }", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/categories", "host": ["{{base_url}}"], "path": ["categories"]}}, "response": []}, {"name": "Get All Categories - With Products", "event": [{"listen": "test", "script": {"exec": ["pm.test('Categories with products retrieved', function () {", "    pm.response.to.have.status(200);", "    const response = pm.response.json();", "    pm.expect(response.data.categories).to.be.an('array');", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/categories?withProducts=true", "host": ["{{base_url}}"], "path": ["categories"], "query": [{"key": "withProducts", "value": "true"}]}}, "response": []}, {"name": "Get All Categories - Featured Only", "event": [{"listen": "test", "script": {"exec": ["pm.test('Featured categories retrieved', function () {", "    pm.response.to.have.status(200);", "    const response = pm.response.json();", "    pm.expect(response.data.categories).to.be.an('array');", "    ", "    // Check if all returned categories are featured", "    response.data.categories.forEach(category => {", "        if (category.featured !== undefined) {", "            pm.expect(category.featured).to.be.true;", "        }", "    });", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/categories?featured=true", "host": ["{{base_url}}"], "path": ["categories"], "query": [{"key": "featured", "value": "true"}]}}, "response": []}, {"name": "Get Featured Categories", "event": [{"listen": "test", "script": {"exec": ["pm.test('Featured categories endpoint works', function () {", "    pm.response.to.have.status(200);", "    const response = pm.response.json();", "    pm.expect(response.data.categories).to.be.an('array');", "    ", "    // Check if categories have count information", "    response.data.categories.forEach(category => {", "        pm.expect(category).to.have.property('productCount');", "        pm.expect(category).to.have.property('baleCount');", "        pm.expect(category).to.have.property('totalCount');", "    });", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/categories/featured?limit=5", "host": ["{{base_url}}"], "path": ["categories", "featured"], "query": [{"key": "limit", "value": "5"}]}}, "response": []}, {"name": "Get Category Tree", "event": [{"listen": "test", "script": {"exec": ["pm.test('Category tree retrieved', function () {", "    pm.response.to.have.status(200);", "    const response = pm.response.json();", "    pm.expect(response.data.categories).to.be.an('array');", "    ", "    // Check if categories have breadcrumb and level", "    response.data.categories.forEach(category => {", "        pm.expect(category).to.have.property('breadcrumb');", "        pm.expect(category).to.have.property('level');", "        pm.expect(category.breadcrumb).to.be.an('array');", "        pm.expect(category.level).to.be.a('number');", "    });", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/categories/tree", "host": ["{{base_url}}"], "path": ["categories", "tree"]}}, "response": []}, {"name": "Get Category by ID", "event": [{"listen": "test", "script": {"exec": ["pm.test('Category retrieved by ID', function () {", "    pm.response.to.have.status(200);", "    const response = pm.response.json();", "    pm.expect(response.data.category).to.be.an('object');", "    pm.expect(response.data.category).to.have.property('_id');", "    pm.expect(response.data.category).to.have.property('name');", "    pm.expect(response.data.category).to.have.property('productCount');", "    pm.expect(response.data.category).to.have.property('baleCount');", "    pm.expect(response.data.category).to.have.property('totalCount');", "    pm.expect(response.data.category).to.have.property('subcategories');", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/categories/{{category_id}}", "host": ["{{base_url}}"], "path": ["categories", "{{category_id}}"]}}, "response": []}, {"name": "Get Category by Slug", "event": [{"listen": "test", "script": {"exec": ["pm.test('Category retrieved by slug', function () {", "    pm.response.to.have.status(200);", "    const response = pm.response.json();", "    pm.expect(response.data.category).to.be.an('object');", "    pm.expect(response.data.category).to.have.property('slug');", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/categories/{{category_slug}}", "host": ["{{base_url}}"], "path": ["categories", "{{category_slug}}"]}}, "response": []}, {"name": "Search Categories", "event": [{"listen": "test", "script": {"exec": ["pm.test('Category search works', function () {", "    pm.response.to.have.status(200);", "    const response = pm.response.json();", "    pm.expect(response.data.categories).to.be.an('array');", "    pm.expect(response.data.query).to.be.a('string');", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/categories/search?q=clothing&limit=5", "host": ["{{base_url}}"], "path": ["categories", "search"], "query": [{"key": "q", "value": "clothing"}, {"key": "limit", "value": "5"}]}}, "response": []}]}