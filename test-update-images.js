const axios = require('axios');
const FormData = require('form-data');
const fs = require('fs');

/**
 * Test script to verify the updateImages endpoint
 * This tests the new approach where frontend sends desired final state
 */

const BASE_URL = 'http://localhost:3000/api/v1';

// Test cases for different image update scenarios
const testCases = [
  {
    name: "Keep some existing images only",
    data: {
      images: [
        "https://res.cloudinary.com/demo/image/upload/existing1.jpg",
        "https://res.cloudinary.com/demo/image/upload/existing2.jpg"
      ]
    }
  },
  {
    name: "Remove all existing images (upload only - simulated)",
    data: {
      // No images array = remove all existing, only keep uploaded
    },
    note: "This test would need actual file upload in Postman"
  },
  {
    name: "Keep all current images",
    data: {
      images: [
        "https://res.cloudinary.com/demo/image/upload/current1.jpg",
        "https://res.cloudinary.com/demo/image/upload/current2.jpg",
        "https://res.cloudinary.com/demo/image/upload/current3.jpg"
      ]
    }
  },
  {
    name: "Keep some + upload new (simulated)",
    data: {
      images: [
        "https://res.cloudinary.com/demo/image/upload/keep1.jpg"
      ]
    },
    note: "In real usage, this would also include file uploads"
  }
];

// Error test cases
const errorTestCases = [
  {
    name: "Error: No images provided (empty request)",
    data: {},
    expectedError: "Product must have at least one image"
  },
  {
    name: "Error: Images not an array",
    data: { images: "not-an-array" },
    expectedError: "Images must be an array"
  },
  {
    name: "Error: Invalid existing images",
    data: {
      images: [
        "https://res.cloudinary.com/demo/image/upload/not-current-image.jpg"
      ]
    },
    expectedError: "Some provided images are not current product images"
  },
  {
    name: "Error: Too many images (11)",
    data: {
      images: Array.from({length: 11}, (_, i) =>
        `https://res.cloudinary.com/demo/image/upload/sample${i + 1}.jpg`
      )
    },
    expectedError: "Maximum 10 images allowed per product"
  }
];

async function testUpdateImages() {
  console.log('🧪 Testing updateImages Endpoint\n');

  // You'll need to get a valid JWT token for a creator and a product ID
  const authToken = 'YOUR_JWT_TOKEN_HERE'; // Replace with actual token
  const productId = 'YOUR_PRODUCT_ID_HERE'; // Replace with actual product ID
  
  if (authToken === 'YOUR_JWT_TOKEN_HERE' || productId === 'YOUR_PRODUCT_ID_HERE') {
    console.log('❌ Please update the authToken and productId variables with real values');
    console.log('   You can get these from:');
    console.log('   1. Login as a creator to get the JWT token');
    console.log('   2. Create or get an existing product ID');
    return;
  }

  const headers = {
    'Authorization': `Bearer ${authToken}`,
    'Content-Type': 'application/json'
  };

  console.log('📝 Running Success Test Cases...\n');

  // Test successful updates
  for (const testCase of testCases) {
    try {
      console.log(`Testing: ${testCase.name}`);
      
      const response = await axios.patch(
        `${BASE_URL}/creators/products/${productId}/images`,
        testCase.data,
        { headers }
      );

      if (response.status === 200) {
        console.log(`✅ ${testCase.name} - SUCCESS`);
        console.log(`   Updated images count: ${response.data.data.product.images.length}`);
        console.log(`   Message: ${response.data.message}\n`);
      } else {
        console.log(`❌ ${testCase.name} - Unexpected status: ${response.status}\n`);
      }
    } catch (error) {
      console.log(`❌ ${testCase.name} - ERROR:`);
      console.log(`   ${error.response?.data?.message || error.message}\n`);
    }
  }

  console.log('📝 Running Error Test Cases...\n');

  // Test error cases
  for (const testCase of errorTestCases) {
    try {
      console.log(`Testing: ${testCase.name}`);
      
      const response = await axios.patch(
        `${BASE_URL}/creators/products/${productId}/images`,
        testCase.data,
        { headers }
      );

      console.log(`❌ ${testCase.name} - Should have failed but got status: ${response.status}\n`);
    } catch (error) {
      if (error.response?.status === 400) {
        const errorMessage = error.response.data.message;
        if (errorMessage.includes(testCase.expectedError)) {
          console.log(`✅ ${testCase.name} - Correctly returned error`);
          console.log(`   Error: ${errorMessage}\n`);
        } else {
          console.log(`❌ ${testCase.name} - Wrong error message`);
          console.log(`   Expected: ${testCase.expectedError}`);
          console.log(`   Got: ${errorMessage}\n`);
        }
      } else {
        console.log(`❌ ${testCase.name} - Unexpected error:`);
        console.log(`   ${error.response?.data?.message || error.message}\n`);
      }
    }
  }

  console.log('🎯 Test Summary:');
  console.log('   - Test both success and error scenarios');
  console.log('   - Verify image count limits (max 10)');
  console.log('   - Check proper error messages');
  console.log('   - Ensure old images are cleaned up automatically');
  console.log('\n📋 Manual Testing Notes:');
  console.log('   1. Test file uploads using Postman with actual image files');
  console.log('   2. Verify Cloudinary cleanup of old images');
  console.log('   3. Test mixed uploads (files + URLs) using form-data');
  console.log('   4. Check that only the product owner can update images');
}

// Run the tests
if (require.main === module) {
  testUpdateImages().catch(console.error);
}

module.exports = { testUpdateImages };
