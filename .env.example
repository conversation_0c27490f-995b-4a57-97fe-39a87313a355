# Database Configuration
DATABASE_URI=mongodb://localhost:27017/everyfash
DATABASE_PASSWORD=your_database_password

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-here
JWT_EXPIRES_IN=7d
JWT_COOKIE_EXPIRES_IN=7

# Frontend URLs for Social Auth Redirects and Password Reset
FRONTEND_BUYER_URL=http://localhost:3000
FRONTEND_CREATOR_URL=http://localhost:3001
FRONTEND_ADMIN_URL=http://localhost:3002

# Google OAuth Configuration
GOOGLE_CLIENT_ID=your_google_client_id
GOOGLE_CLIENT_SECRET=your_google_client_secret
GOOGLE_CALLBACK_BASE_URL=http://localhost:5000/api/v1/auth/google

# Facebook OAuth Configuration
FACEBOOK_CLIENT_ID=your_facebook_client_id
FACEBOOK_CLIENT_SECRET=your_facebook_client_secret
FACEBOOK_CALLBACK_BASE_URL=http://localhost:5000/api/v1/auth/facebook

# Cloudinary Configuration
CLOUDINARY_CLOUD_NAME=your_cloudinary_cloud_name
CLOUDINARY_API_KEY=your_cloudinary_api_key
CLOUDINARY_API_SECRET=your_cloudinary_api_secret

# Email Configuration
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_SECURE=false
EMAIL_USER=<EMAIL>
EMAIL_PASS=your_email_password

# Environment
NODE_ENV=development
PORT=5000

# CORS Configuration
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:3001,http://localhost:3002
