
const mongoose = require('mongoose');

const deviceTokenSchema = new mongoose.Schema(
  {
    user: {
      type: mongoose.Schema.ObjectId,
      ref: 'User',
      required: [true, 'Device token must belong to a user']
    },
    token: {
      type: String,
      required: [true, 'Device token is required'],
      unique: true
    },
    deviceType: {
      type: String,
      enum: ['ios', 'android', 'web'],
      required: [true, 'Device type is required']
    },
    deviceInfo: {
      model: String,
      os: String,
      osVersion: String,
      appVersion: String
    },
    isActive: {
      type: Boolean,
      default: true
    },
    lastUsed: {
      type: Date,
      default: Date.now
    }
  },
  {
    timestamps: true
  }
);

// Index for faster queries
deviceTokenSchema.index({ user: 1, isActive: 1 });
// Note: token field already has unique: true in schema definition (line 14), so no need for explicit index

// Static method to register a device token
deviceTokenSchema.statics.registerToken = async function(userId, token, deviceType, deviceInfo = {}) {
  try {
    // Check if token already exists
    const existingToken = await this.findOne({ token });
    
    if (existingToken) {
      // Update existing token
      existingToken.user = userId;
      existingToken.deviceType = deviceType;
      existingToken.deviceInfo = deviceInfo;
      existingToken.isActive = true;
      existingToken.lastUsed = new Date();
      
      return await existingToken.save();
    } else {
      // Create new token
      return await this.create({
        user: userId,
        token,
        deviceType,
        deviceInfo,
        isActive: true,
        lastUsed: new Date()
      });
    }
  } catch (error) {
    console.error('Error registering device token:', error);
    throw error;
  }
};

// Static method to deactivate a device token
deviceTokenSchema.statics.deactivateToken = async function(token) {
  try {
    const result = await this.updateOne(
      { token },
      { isActive: false }
    );
    
    return result.modifiedCount > 0;
  } catch (error) {
    console.error('Error deactivating device token:', error);
    throw error;
  }
};

// Static method to get active tokens for a user
deviceTokenSchema.statics.getActiveTokens = async function(userId) {
  try {
    return await this.find({
      user: userId,
      isActive: true
    });
  } catch (error) {
    console.error('Error getting active tokens:', error);
    throw error;
  }
};

// Static method to deactivate all tokens for a user
deviceTokenSchema.statics.deactivateAllTokensForUser = async function(userId) {
  try {
    const result = await this.updateMany(
      { user: userId, isActive: true },
      { isActive: false }
    );
    
    return result.nModified;
  } catch (error) {
    console.error('Error deactivating all tokens for user:', error);
    throw error;
  }
};

// Static method to clean up old tokens
deviceTokenSchema.statics.cleanupOldTokens = async function(daysOld = 90) {
  const cutoffDate = new Date();
  cutoffDate.setDate(cutoffDate.getDate() - daysOld);
  
  try {
    const result = await this.updateMany(
      { lastUsed: { $lt: cutoffDate } },
      { isActive: false }
    );
    
    return result.nModified;
  } catch (error) {
    console.error('Error cleaning up old tokens:', error);
    throw error;
  }
};

const DeviceToken = mongoose.model('DeviceToken', deviceTokenSchema);

module.exports = DeviceToken;



