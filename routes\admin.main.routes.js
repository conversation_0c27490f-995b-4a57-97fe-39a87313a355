const express = require('express');
const authMiddleware = require('../middleware/auth.middleware');

// Import admin sub-routes
const dashboardRoutes = require('./admin/dashboard.routes');
const productRoutes = require('./admin/product.routes');
const creatorRoutes = require('./admin/creator.routes');
const orderRoutes = require('./admin/order.routes');
const reviewRoutes = require('./admin/review.routes');
const promotionRoutes = require('./admin/promotion.routes');
const buyerRoutes = require('./admin/buyer.routes');
const feeRoutes = require('./admin/fees.routes');
const reportRoutes = require('./admin/reports.routes');
const exchangeRoutes = require('./admin/exchange.routes');
const imageCleanupRoutes = require('./admin/imageCleanup.routes');


const router = express.Router();

// Protect all admin routes
router.use(authMiddleware.protect);
router.use(authMiddleware.restrictTo('admin'));

// Use admin sub-routes
router.use('/creators', creatorRoutes); //✅
router.use('/images', imageCleanupRoutes); //✅ Image cleanup and management
// router.use('/products', productRoutes); //✅ (now handles both products and bales)
// router.use('/buyers', buyerRoutes); // For buyers management


// router.use('/dashboard', dashboardRoutes);
// router.use('/orders', orderRoutes);
// router.use('/reviews', reviewRoutes);
// router.use('/promotions', promotionRoutes);
// router.use('/fees', feeRoutes);
// router.use('/reports', reportRoutes);
// router.use('/exchange', exchangeRoutes);



module.exports = router;
