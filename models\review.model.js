const mongoose = require('mongoose');
const Product = require('./product.model');
const User = require('./user.model');

const reviewSchema = new mongoose.Schema(
  {
    title: {
      type: String,
      required: [true, 'Review title cannot be empty'],
      trim: true,
      maxlength: [100, 'Review title cannot be longer than 100 characters']
    },
    review: {
      type: String,
      required: [true, 'Review details cannot be empty'],
      trim: true,
      maxlength: [1000, 'Review details cannot be longer than 1000 characters']
    },
    rating: {
      type: Number,
      min: 1,
      max: 5,
      required: [true, 'Review must have a rating']
    },
    product: {
      type: mongoose.Schema.ObjectId,
      ref: 'Product',
      required: [true, 'Review must be for a product']
    },
    creator: {
      type: mongoose.Schema.ObjectId,
      ref: 'User'
    },
    user: {
      type: mongoose.Schema.ObjectId,
      ref: 'User',
      required: [true, 'Review must belong to a user']
    },
    order: {
      type: mongoose.Schema.ObjectId,
      ref: 'Order'
    },
    verified: {
      type: Boolean,
      default: false
    },
    hidden: {
      type: Boolean,
      default: false,
      select: false // Hide this field from regular queries
    }
  },
  {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true }
  }
);

// Virtual to determine what type of item is being reviewed
reviewSchema.virtual('reviewType').get(function() {
  if (this.product) {
    return this.product.type || 'product';
  }
  return null;
});

// Virtual to get formatted date
reviewSchema.virtual('formattedDate').get(function() {
  return new Date(this.createdAt).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });
});

// Virtual to get time since review was posted
reviewSchema.virtual('timeAgo').get(function() {
  const now = new Date();
  const createdAt = new Date(this.createdAt);
  const diffTime = Math.abs(now - createdAt);
  const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));
  
  if (diffDays === 0) {
    const diffHours = Math.floor(diffTime / (1000 * 60 * 60));
    if (diffHours === 0) {
      const diffMinutes = Math.floor(diffTime / (1000 * 60));
      return `${diffMinutes} minute${diffMinutes !== 1 ? 's' : ''} ago`;
    }
    return `${diffHours} hour${diffHours !== 1 ? 's' : ''} ago`;
  }
  if (diffDays < 7) {
    return `${diffDays} day${diffDays !== 1 ? 's' : ''} ago`;
  }
  if (diffDays < 30) {
    const diffWeeks = Math.floor(diffDays / 7);
    return `${diffWeeks} week${diffWeeks !== 1 ? 's' : ''} ago`;
  }
  if (diffDays < 365) {
    const diffMonths = Math.floor(diffDays / 30);
    return `${diffMonths} month${diffMonths !== 1 ? 's' : ''} ago`;
  }
  const diffYears = Math.floor(diffDays / 365);
  return `${diffYears} year${diffYears !== 1 ? 's' : ''} ago`;
});

// Virtual to get a truncated version of the review
reviewSchema.virtual('shortReview').get(function() {
  if (!this.review) return '';
  return this.review.length > 150 
    ? `${this.review.substring(0, 150)}...` 
    : this.review;
});

// Virtual to check if review is verified purchase
reviewSchema.virtual('isVerifiedPurchase').get(function() {
  return this.verified;
});

// Virtual to check if review is recent (less than 30 days old)
reviewSchema.virtual('isRecent').get(function() {
  const now = new Date();
  const createdAt = new Date(this.createdAt);
  const diffTime = Math.abs(now - createdAt);
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  return diffDays <= 30;
});

// Pre-save middleware to set creator from product
reviewSchema.pre('save', function(next) {
  // If product is set, ensure creator is set to the product's creator
  if (this.product && !this.isNew) {
    return next();
  }

  if (this.product) {
    // Find the product and set the creator
    Product.findById(this.product)
      .then(product => {
        if (!product) {
          return next(new Error('Product not found'));
        }
        this.creator = product.creator;
        next();
      })
      .catch(err => next(err));
  } else {
    next();
  }
});

// Query middleware to populate related fields
reviewSchema.pre(/^find/, function(next) {
  // Populate user who wrote the review
  this.populate({
    path: 'user',
    select: 'name'
  });
  
  // Populate product details (includes both products and bales)
  this.populate({
    path: 'product',
    select: 'name type formattedPriceRange basePrice condition'
  });
  
  // Populate creator (seller) details
  this.populate({
    path: 'creator',
    select: 'name photo shopInfo businessInfo.businessName'
  });
  
  // Populate order details
  this.populate({
    path: 'order',
    select: 'orderNumber purchaseDate'
  });
  
  // Filter out hidden reviews for non-admin users
  const user = this.getOptions().user;
  if (!user || user.role !== 'admin') {
    this.find({ hidden: { $ne: true } });
  } else {
    // For admin users, explicitly include the hidden field
    this.select('+hidden');
  }
  
  next();
});

// Static method to calculate average ratings for products
reviewSchema.statics.calcProductRatings = async function(productId) {
  const stats = await this.aggregate([
    {
      $match: { product: mongoose.Types.ObjectId(productId) }
    },
    {
      $group: {
        _id: '$product',
        nRating: { $sum: 1 },
        avgRating: { $avg: '$rating' }
      }
    }
  ]);
  
  if (stats.length > 0) {
    await Product.findByIdAndUpdate(productId, {
      ratingsQuantity: stats[0].nRating,
      ratingsAverage: Math.round(stats[0].avgRating * 10) / 10
    });
  } else {
    await Product.findByIdAndUpdate(productId, {
      ratingsQuantity: 0,
      ratingsAverage: 0
    });
  }
};

// Note: Bale ratings are now handled by the same calcProductRatings method
// since bales are now products with type: 'bale'

// Static method to calculate average ratings for creators
reviewSchema.statics.calcCreatorRatings = async function(creatorId) {
  const stats = await this.aggregate([
    {
      $match: { creator: mongoose.Types.ObjectId(creatorId) }
    },
    {
      $group: {
        _id: '$creator',
        nRating: { $sum: 1 },
        avgRating: { $avg: '$rating' }
      }
    }
  ]);
  
  if (stats.length > 0) {
    await User.findByIdAndUpdate(creatorId, {
      'metrics.ratingsQuantity': stats[0].nRating,
      'metrics.averageRating': Math.round(stats[0].avgRating * 10) / 10
    });
  } else {
    await User.findByIdAndUpdate(creatorId, {
      'metrics.ratingsQuantity': 0,
      'metrics.averageRating': 0
    });
  }
};

const Review = mongoose.model('Review', reviewSchema);

module.exports = Review;

