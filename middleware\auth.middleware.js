const { promisify } = require('util');
const jwt = require('jsonwebtoken');
const { BaseUser } = require('../models/user.model');
const AppError = require('../utils/appError');
const catchAsync = require('../utils/catchAsync');
const authConfig = require('../config/auth.config');

/**
 * Middleware to protect routes - checks if user is authenticated
 */
exports.protect = catchAsync(async (req, res, next) => {
  // 1) Get token from header or cookie
  let token;
  if (
    req.headers.authorization &&
    req.headers.authorization.startsWith('Bearer')
  ) {
    token = req.headers.authorization.split(' ')[1];
  } else if (req.cookies.jwt) {
    token = req.cookies.jwt;
  }

  if (!token) {
    return next(
      new AppError('You are not logged in! Please log in to get access.', 401)
    );
  }

  // 2) Verify token
  const decoded = await promisify(jwt.verify)(token, authConfig.jwtSecret);

  // 3) Check if user still exists and is active
  const currentUser = await BaseUser.findById(decoded.id).select('+active');

  if (!currentUser) {
    return next(
      new AppError('The user belonging to this token no longer exists.', 401)
    );
  }

  // 4) Check if user is active
  if (!currentUser.active) {
    return next(
      new AppError('Your account has been deactivated. Please contact support.', 401)
    );
  }

  // 4) Check if user changed password after the token was issued
  if (currentUser.changedPasswordAfter(decoded.iat)) {
    return next(
      new AppError('User recently changed password! Please log in again.', 401)
    );
  }

  // GRANT ACCESS TO PROTECTED ROUTE
  req.user = currentUser;
  res.locals.user = currentUser;
  next();
});

/**
 * Middleware to restrict access to certain roles
 */
exports.restrictTo = (...roles) => {
  return (req, res, next) => {
    if (!roles.includes(req.user.role)) {
      return next(
        new AppError('You do not have permission to perform this action', 403)
      );
    }
    next();
  };
};


/**
 * Middleware to restrict access to certain admin levels
 */
exports.restrictToAdminLevel = (...levels) => {
  return (req, res, next) => {
    if (req.user.role !== 'admin' || !levels.includes(req.user.adminLevel)) {
      return next(
        new AppError('You do not have permission to perform this action', 403)
      );
    }
    next();
  };
};
