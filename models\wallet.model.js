const mongoose = require('mongoose');

const walletSchema = new mongoose.Schema({
  user: {
    type: mongoose.Schema.ObjectId,
    ref: 'User'
  },

  userType: {
    type: String,
    enum: ['buyer', 'creator', 'platform', 'delivery'],
    required: true
  },

  // Simple balance tracking
  balance: {
    type: Number,
    default: 0
  },

  currency: {
    type: String,
    default: 'GHS'
  },

  status: {
    type: String,
    enum: ['active', 'suspended', 'closed'],
    default: 'active'
  }
}, {
  timestamps: true
});

// Unique wallet per user
walletSchema.index({ user: 1, userType: 1 }, { unique: true });

// Method to update balance
walletSchema.methods.updateBalance = async function() {
  const Transaction = mongoose.model('Transaction');

  // Calculate balance from all completed transactions
  const result = await Transaction.aggregate([
    {
      $match: {
        user: this.user,
        status: 'completed'
      }
    },
    {
      $group: {
        _id: null,
        totalBalance: { $sum: '$amount' }
      }
    }
  ]);

  this.balance = result.length > 0 ? result[0].totalBalance : 0;
  return this.save();
};

// Get user balance
walletSchema.statics.getUserBalance = async function(userId, userType = 'creator') {
  const wallet = await this.findOne({ user: userId, userType });
  if (!wallet) {
    // Create wallet if it doesn't exist
    const newWallet = await this.create({ user: userId, userType });
    return newWallet.balance;
  }

  // Update balance from transactions
  await wallet.updateBalance();
  return wallet.balance;
}

// Indexes for common query patterns
// Note: user + userType unique index already defined above (line 36)
walletSchema.index({ 'transactions.type': 1 });
walletSchema.index({ 'transactions.status': 1 });
walletSchema.index({ 'transactions.createdAt': -1 });
walletSchema.index({ 'transactions.reference.id': 1 });

// Virtual for available balance (balance - onHoldBalance)
walletSchema.virtual('availableBalance').get(function() {
  return this.balance - this.onHoldBalance;
});

// Virtual for formatted balance with currency
walletSchema.virtual('formattedBalance').get(function() {
  const currencySymbols = {
    'NGN': '₦',
    'USD': '$',
    'GBP': '£',
    'EUR': '€',
    'GHS': '₵',
    'ZAR': 'R',
    'KES': 'KSh'
  };

  const symbol = currencySymbols[this.currency] || this.currency;
  return `${symbol}${this.balance.toFixed(2)}`;
});

// Virtual for formatted available balance
walletSchema.virtual('formattedAvailableBalance').get(function() {
  const currencySymbols = {
    'NGN': '₦',
    'USD': '$',
    'GBP': '£',
    'EUR': '€',
    'GHS': '₵',
    'ZAR': 'R',
    'KES': 'KSh'
  };

  const symbol = currencySymbols[this.currency] || this.currency;
  return `${symbol}${this.availableBalance.toFixed(2)}`;
});

/**
 * Add a transaction to the wallet and update balances
 * @param {Object} transactionData - Transaction data
 * @returns {Promise<Object>} - Updated wallet
 */
walletSchema.methods.addTransaction = async function(transactionData) {
  // Create the transaction
  const transaction = {
    type: transactionData.type,
    amount: transactionData.amount,
    currency: transactionData.currency || this.currency,
    status: transactionData.status || 'completed',
    description: transactionData.description,
    reference: transactionData.reference,
    initiatedBy: transactionData.initiatedBy
  };

  // Set completion date if transaction is completed
  if (transaction.status === 'completed') {
    transaction.completedAt = new Date();
  }

  // Add transaction to the wallet
  this.transactions.push(transaction);

  // Update balances based on transaction type and status
  if (transaction.status === 'completed') {
    // Update main balance for completed transactions
    this.balance += transaction.amount;
  } else if (transaction.status === 'pending') {
    // Update pending balance for pending transactions
    this.pendingBalance += transaction.amount;
  }

  // Update last updated timestamp
  this.lastUpdated = new Date();

  // Save the wallet
  return this.save();
};

/**
 * Update a transaction status and adjust balances accordingly
 * @param {String} transactionId - ID of the transaction to update
 * @param {String} newStatus - New status for the transaction
 * @param {Object} options - Additional options
 * @returns {Promise<Object>} - Updated wallet
 */
walletSchema.methods.updateTransactionStatus = async function(transactionId, newStatus, options = {}) {
  // Find the transaction
  const transaction = this.transactions.id(transactionId);
  if (!transaction) {
    throw new Error('Transaction not found');
  }

  const oldStatus = transaction.status;

  // Skip if status hasn't changed
  if (oldStatus === newStatus) {
    return this;
  }

  // Update transaction status
  transaction.status = newStatus;

  // Handle balance adjustments based on status change
  if (oldStatus === 'pending' && newStatus === 'completed') {
    // Move from pending to main balance
    this.pendingBalance -= transaction.amount;
    this.balance += transaction.amount;
    transaction.completedAt = new Date();
  } else if (oldStatus === 'pending' && newStatus === 'failed') {
    // Remove from pending balance
    this.pendingBalance -= transaction.amount;
  } else if (oldStatus === 'completed' && newStatus === 'reversed') {
    // Reverse the transaction
    this.balance -= transaction.amount;
    transaction.reversedAt = new Date();
    transaction.reversalReason = options.reversalReason;

    // Create a reversal transaction if requested
    if (options.createReversal) {
      await this.addTransaction({
        type: 'reversal',
        amount: -transaction.amount,
        description: `Reversal of transaction ${transactionId}: ${options.reversalReason || 'No reason provided'}`,
        reference: {
          type: transaction.reference.type,
          id: transaction.reference.id,
          meta: {
            originalTransactionId: transactionId
          }
        },
        initiatedBy: options.initiatedBy
      });
    }
  }

  // Update last updated timestamp
  this.lastUpdated = new Date();

  // Save the wallet
  return this.save();
};

/**
 * Place a hold on a specific amount in the wallet
 * @param {Number} amount - Amount to place on hold
 * @param {String} reason - Reason for the hold
 * @returns {Promise<Object>} - Updated wallet
 */
walletSchema.methods.placeHold = async function(amount, reason) {
  // Ensure there's enough available balance
  if (this.availableBalance < amount) {
    throw new Error('Insufficient available balance for hold');
  }

  // Update hold balance
  this.onHoldBalance += amount;

  // Update last updated timestamp
  this.lastUpdated = new Date();

  // Save the wallet
  return this.save();
};

/**
 * Release a hold on a specific amount in the wallet
 * @param {Number} amount - Amount to release from hold
 * @param {String} reason - Reason for releasing the hold
 * @returns {Promise<Object>} - Updated wallet
 */
walletSchema.methods.releaseHold = async function(amount, reason) {
  // Ensure there's enough on-hold balance
  if (this.onHoldBalance < amount) {
    throw new Error('Insufficient on-hold balance for release');
  }

  // Update hold balance
  this.onHoldBalance -= amount;

  // Update last updated timestamp
  this.lastUpdated = new Date();

  // Save the wallet
  return this.save();
};

/**
 * Get transactions by type
 * @param {String} type - Transaction type
 * @param {Object} options - Additional options (limit, skip, etc.)
 * @returns {Array} - Filtered transactions
 */
walletSchema.methods.getTransactionsByType = function(type, options = {}) {
  const { limit = 10, skip = 0, startDate, endDate, status } = options;

  let filteredTransactions = this.transactions.filter(transaction =>
    transaction.type === type &&
    (!status || transaction.status === status) &&
    (!startDate || transaction.createdAt >= new Date(startDate)) &&
    (!endDate || transaction.createdAt <= new Date(endDate))
  );

  return filteredTransactions
    .sort((a, b) => b.createdAt - a.createdAt)
    .slice(skip, skip + limit);
};

/**
 * Get transactions by reference
 * @param {String} referenceType - Reference type
 * @param {String} referenceId - Reference ID
 * @param {Object} options - Additional options (limit, skip, etc.)
 * @returns {Array} - Filtered transactions
 */
walletSchema.methods.getTransactionsByReference = function(referenceType, referenceId, options = {}) {
  const { limit = 10, skip = 0, status } = options;

  let filteredTransactions = this.transactions.filter(
    transaction =>
      transaction.reference &&
      transaction.reference.type === referenceType &&
      transaction.reference.id.toString() === referenceId.toString() &&
      (!status || transaction.status === status)
  );

  return filteredTransactions
    .sort((a, b) => b.createdAt - a.createdAt)
    .slice(skip, skip + limit);
};

/**
 * Get transaction summary by type
 * @param {Object} options - Filter options (startDate, endDate, status)
 * @returns {Object} - Summary of transactions by type
 */
walletSchema.methods.getTransactionSummary = function(options = {}) {
  const { startDate, endDate, status = 'completed' } = options;
  const summary = {};

  // Group transactions by type
  this.transactions.forEach(transaction => {
    if (
      transaction.status === status &&
      (!startDate || transaction.createdAt >= new Date(startDate)) &&
      (!endDate || transaction.createdAt <= new Date(endDate))
    ) {
      if (!summary[transaction.type]) {
        summary[transaction.type] = 0;
      }
      summary[transaction.type] += transaction.amount;
    }
  });

  return summary;
};

/**
 * Static method to find or create a wallet
 * @param {String} userId - User ID (null for platform wallet)
 * @param {String} userType - User type
 * @returns {Promise<Object>} - Wallet
 */
walletSchema.statics.findOrCreate = async function(userId, userType) {
  let wallet = await this.findOne({ user: userId, userType });

  if (!wallet) {
    wallet = await this.create({
      user: userId,
      userType
    });
  }

  return wallet;
};

/**
 * Static method to get platform wallet
 * @returns {Promise<Object>} - Platform wallet
 */
walletSchema.statics.getPlatformWallet = async function() {
  return this.findOrCreate(null, 'platform');
};

/**
 * Static method to get creator wallet
 * @param {String} creatorId - Creator ID
 * @returns {Promise<Object>} - Creator wallet
 */
walletSchema.statics.getCreatorWallet = async function(creatorId) {
  return this.findOrCreate(creatorId, 'creator');
};

/**
 * Static method to get buyer wallet
 * @param {String} buyerId - Buyer ID
 * @returns {Promise<Object>} - Buyer wallet
 */
walletSchema.statics.getBuyerWallet = async function(buyerId) {
  return this.findOrCreate(buyerId, 'buyer');
};

/**
 * Static method to process an order's financial transactions
 * @param {Object} order - Order object
 * @returns {Promise<Object>} - Result of processing
 */
walletSchema.statics.processOrderTransactions = async function(order) {
  // Get platform wallet
  const platformWallet = await this.getPlatformWallet();

  // Ensure fees are calculated
  if (!order.fees || !order.fees.platform || !order.fees.platform.amount) {
    await order.calculateFees();
  }

  // Process each item in the order
  for (const item of order.items) {
    if (!item.creator) continue;

    // Get creator wallet
    const creatorWallet = await this.getCreatorWallet(item.creator);

    // Calculate item total
    const itemTotal = item.price * item.quantity;

    // Calculate platform fee for this item (proportional to item value)
    const itemProportion = itemTotal / order.subtotal;
    const platformFeeAmount = parseFloat((order.fees.platform.amount * itemProportion).toFixed(2));

    // Add earnings to creator wallet
    await creatorWallet.addTransaction({
      type: 'order_earning',
      amount: itemTotal - platformFeeAmount,
      description: `Earnings from order #${order.orderNumber} - ${item.quantity}x ${item.name || 'Product'}`,
      reference: {
        type: 'order',
        id: order._id,
        meta: {
          itemId: item._id
        }
      }
    });

    // Add platform fee to platform wallet
    await platformWallet.addTransaction({
      type: 'platform_fee',
      amount: platformFeeAmount,
      description: `Platform fee from order #${order.orderNumber} - ${item.quantity}x ${item.name || 'Product'}`,
      reference: {
        type: 'order',
        id: order._id,
        meta: {
          itemId: item._id,
          creatorId: item.creator
        }
      }
    });
  }

  // Add processing fee to platform wallet (already calculated for the whole order)
  if (order.fees.processing && order.fees.processing.totalAmount) {
    await platformWallet.addTransaction({
      type: 'processing_fee',
      amount: order.fees.processing.totalAmount,
      description: `Processing fee from order #${order.orderNumber}`,
      reference: {
        type: 'order',
        id: order._id
      }
    });
  }

  return {
    success: true,
    message: `Processed financial transactions for order #${order.orderNumber}`
  };
};

/**
 * Static method to process a payout
 * @param {Object} payout - Payout object
 * @returns {Promise<Object>} - Result of processing
 */
walletSchema.statics.processPayout = async function(payout) {
  // Get creator wallet
  const creatorWallet = await this.getCreatorWallet(payout.recipient);

  // Add payout transaction to creator wallet
  await creatorWallet.addTransaction({
    type: 'payout',
    amount: -payout.amount, // Negative amount for outgoing funds
    description: `Payout #${payout.reference}`,
    reference: {
      type: 'payout',
      id: payout._id
    }
  });

  return {
    success: true,
    message: `Processed payout #${payout.reference}`
  };
};

/**
 * Static method to process an exchange
 * @param {Object} order - Order object
 * @param {Object} exchangeData - Exchange data
 * @returns {Promise<Object>} - Result of processing
 */
walletSchema.statics.processExchange = async function(order, exchangeData) {
  // Get platform wallet
  const platformWallet = await this.getPlatformWallet();

  // Get buyer wallet
  const buyerWallet = await this.getBuyerWallet(order.user);

  // Calculate price difference
  const priceDifference = exchangeData.priceDifference;

  // Check if this is an exchange between items from the same creator
  const sameCreator = exchangeData.sameCreator ||
    (exchangeData.oldItem.creator && exchangeData.newItem.creator &&
     exchangeData.oldItem.creator.toString() === exchangeData.newItem.creator.toString());

  // Check if this is an exchange between variations of the same product
  const sameProductVariation = exchangeData.sameProductVariation || false;

  // If it's the same product variation with the same price, no financial transactions needed
  if (sameProductVariation && priceDifference === 0) {
    return {
      success: true,
      message: `No financial transactions needed for variation exchange in order #${order.orderNumber}`
    };
  }

  // If new item costs more than old item, charge the buyer
  if (priceDifference > 0) {
    // Add charge to buyer wallet
    await buyerWallet.addTransaction({
      type: 'exchange_charge',
      amount: -priceDifference, // Negative amount for outgoing funds
      description: `Additional charge for exchange in order #${order.orderNumber}`,
      reference: {
        type: 'order',
        id: order._id,
        meta: {
          oldItemId: exchangeData.oldItem._id,
          newItemId: exchangeData.newItem._id
        }
      }
    });

    // Process creator earnings and platform fees for the price difference
    if (exchangeData.newItem.creator) {
      const creatorWallet = await this.getCreatorWallet(exchangeData.newItem.creator);

      // Calculate platform fee for the price difference
      const platformFeePercentage = order.fees.platform.percentage;
      const platformFeeAmount = (priceDifference * platformFeePercentage) / 100;

      // Add additional earnings to creator wallet
      await creatorWallet.addTransaction({
        type: 'exchange_earning',
        amount: priceDifference - platformFeeAmount,
        description: `Additional earnings from exchange in order #${order.orderNumber}`,
        reference: {
          type: 'order',
          id: order._id,
          meta: {
            oldItemId: exchangeData.oldItem._id,
            newItemId: exchangeData.newItem._id,
            sameCreator
          }
        }
      });

      // Add additional platform fee to platform wallet
      await platformWallet.addTransaction({
        type: 'exchange_fee',
        amount: platformFeeAmount,
        description: `Additional platform fee from exchange in order #${order.orderNumber}`,
        reference: {
          type: 'order',
          id: order._id,
          meta: {
            oldItemId: exchangeData.oldItem._id,
            newItemId: exchangeData.newItem._id,
            creatorId: exchangeData.newItem.creator,
            sameCreator
          }
        }
      });
    }
  }
  // If new item costs less than old item, refund the buyer
  else if (priceDifference < 0) {
    const refundAmount = Math.abs(priceDifference);

    // Add refund to buyer wallet
    await buyerWallet.addTransaction({
      type: 'exchange_refund',
      amount: refundAmount,
      description: `Refund for exchange in order #${order.orderNumber}`,
      reference: {
        type: 'order',
        id: order._id,
        meta: {
          oldItemId: exchangeData.oldItem._id,
          newItemId: exchangeData.newItem._id
        }
      }
    });

    // Process creator earnings and platform fees for the price difference
    if (exchangeData.oldItem.creator) {
      const creatorWallet = await this.getCreatorWallet(exchangeData.oldItem.creator);

      // Calculate platform fee for the price difference
      const platformFeePercentage = order.fees.platform.percentage;
      const platformFeeAmount = (refundAmount * platformFeePercentage) / 100;

      // Deduct earnings from creator wallet
      await creatorWallet.addTransaction({
        type: 'exchange_deduction',
        amount: -(refundAmount - platformFeeAmount),
        description: `Earnings deduction from exchange in order #${order.orderNumber}`,
        reference: {
          type: 'order',
          id: order._id,
          meta: {
            oldItemId: exchangeData.oldItem._id,
            newItemId: exchangeData.newItem._id,
            sameCreator
          }
        }
      });

      // Deduct platform fee from platform wallet
      await platformWallet.addTransaction({
        type: 'exchange_fee_refund',
        amount: -platformFeeAmount,
        description: `Platform fee refund from exchange in order #${order.orderNumber}`,
        reference: {
          type: 'order',
          id: order._id,
          meta: {
            oldItemId: exchangeData.oldItem._id,
            newItemId: exchangeData.newItem._id,
            creatorId: exchangeData.oldItem.creator,
            sameCreator
          }
        }
      });
    }
  }
  // If different creators but same price, handle the transfer of earnings
  else if (!sameCreator && priceDifference === 0) {
    // Get both creator wallets
    const oldCreatorWallet = await this.getCreatorWallet(exchangeData.oldItem.creator);
    const newCreatorWallet = await this.getCreatorWallet(exchangeData.newItem.creator);

    // Calculate the item total and platform fee
    const itemTotal = exchangeData.oldItem.price * exchangeData.oldItem.quantity;
    const platformFeePercentage = order.fees.platform.percentage;
    const platformFeeAmount = (itemTotal * platformFeePercentage) / 100;
    const creatorEarnings = itemTotal - platformFeeAmount;

    // Deduct earnings from old creator
    await oldCreatorWallet.addTransaction({
      type: 'exchange_transfer_out',
      amount: -creatorEarnings,
      description: `Earnings transferred due to exchange in order #${order.orderNumber}`,
      reference: {
        type: 'order',
        id: order._id,
        meta: {
          oldItemId: exchangeData.oldItem._id,
          newItemId: exchangeData.newItem._id,
          sameCreator
        }
      }
    });

    // Add earnings to new creator
    await newCreatorWallet.addTransaction({
      type: 'exchange_transfer_in',
      amount: creatorEarnings,
      description: `Earnings received due to exchange in order #${order.orderNumber}`,
      reference: {
        type: 'order',
        id: order._id,
        meta: {
          oldItemId: exchangeData.oldItem._id,
          newItemId: exchangeData.newItem._id,
          sameCreator
        }
      }
    });
  }

  return {
    success: true,
    message: `Processed financial transactions for exchange in order #${order.orderNumber}`
  };
};

/**
 * Static method to process a refund
 * @param {Object} order - Order object
 * @param {Object} refundData - Refund data
 * @returns {Promise<Object>} - Result of processing
 */
walletSchema.statics.processRefund = async function(order, refundData) {
  // Get platform wallet
  const platformWallet = await this.getPlatformWallet();

  // Get buyer wallet
  const buyerWallet = await this.getBuyerWallet(order.user);

  // Add refund to buyer wallet
  await buyerWallet.addTransaction({
    type: 'refund',
    amount: refundData.amount,
    description: `Refund for order #${order.orderNumber}: ${refundData.reason}`,
    reference: {
      type: 'order',
      id: order._id,
      meta: {
        refundReason: refundData.reason
      }
    }
  });

  // If refunding a specific item, adjust creator's balance
  if (refundData.itemId) {
    const item = order.items.id(refundData.itemId);
    if (item && item.creator) {
      const creatorWallet = await this.getCreatorWallet(item.creator);

      // Calculate item total and platform fee
      const itemTotal = item.price * (refundData.quantity || item.quantity);
      const platformFeePercentage = order.fees.platform.percentage;
      const platformFeeAmount = (itemTotal * platformFeePercentage) / 100;

      // Deduct earnings from creator wallet
      await creatorWallet.addTransaction({
        type: 'refund',
        amount: -(itemTotal - platformFeeAmount),
        description: `Refund deduction for order #${order.orderNumber}: ${refundData.reason}`,
        reference: {
          type: 'order',
          id: order._id,
          meta: {
            itemId: item._id,
            refundReason: refundData.reason
          }
        }
      });

      // Deduct platform fee from platform wallet
      await platformWallet.addTransaction({
        type: 'refund',
        amount: -platformFeeAmount,
        description: `Platform fee refund for order #${order.orderNumber}: ${refundData.reason}`,
        reference: {
          type: 'order',
          id: order._id,
          meta: {
            itemId: item._id,
            creatorId: item.creator,
            refundReason: refundData.reason
          }
        }
      });
    }
  }

  return {
    success: true,
    message: `Processed refund for order #${order.orderNumber}`
  };
};

const Wallet = mongoose.model('Wallet', walletSchema);

module.exports = Wallet;

