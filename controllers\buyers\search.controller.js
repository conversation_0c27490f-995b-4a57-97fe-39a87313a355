const Product = require('../../models/product.model');
const { Creator } = require('../../models/user.model');
const Category = require('../../models/category.model');
const AppError = require('../../utils/appError');
const catchAsync = require('../../utils/catchAsync');

/**
 * Search products and bales
 * @route GET /api/v1/search
 * @access Public
 */
exports.search = catchAsync(async (req, res, next) => {
  // Get search query
  const query = req.query.q;
  if (!query) {
    return next(new AppError('Please provide a search query', 400));
  }

  // Get search type (default to 'all')
  const type = req.query.type || 'all';
  
  // Get search filters
  const filters = {};
  
  // Add category filter if provided
  if (req.query.category) {
    filters.category = req.query.category;
  }
  
  // Add price range filter if provided
  if (req.query.minPrice || req.query.maxPrice) {
    filters.basePrice = {};
    if (req.query.minPrice) {
      filters.basePrice.$gte = parseFloat(req.query.minPrice);
    }
    if (req.query.maxPrice) {
      filters.basePrice.$lte = parseFloat(req.query.maxPrice);
    }
  }
  
  // Add creator filter if provided
  if (req.query.creator) {
    filters.creator = req.query.creator;
  }
  
  // Add gender filter if provided
  if (req.query.gender) {
    filters.gender = req.query.gender;
  }
  
  // Add country filter for bales if provided
  const baleFilters = { ...filters };
  if (req.query.country) {
    baleFilters.country = req.query.country;
  }
  
  // Create search regex
  const searchRegex = new RegExp(query, 'i');
  
  // Set pagination parameters
  const page = parseInt(req.query.page) || 1;
  const limit = parseInt(req.query.limit) || 20;
  const skip = (page - 1) * limit;
  
  // Set sort parameter (default to newest)
  let sortBy = '-createdAt';
  if (req.query.sort) {
    switch (req.query.sort) {
      case 'price-asc':
        sortBy = 'basePrice';
        break;
      case 'price-desc':
        sortBy = '-basePrice';
        break;
      case 'name-asc':
        sortBy = 'name';
        break;
      case 'name-desc':
        sortBy = '-name';
        break;
      case 'rating-desc':
        sortBy = '-ratingsAverage';
        break;
      default:
        sortBy = '-createdAt';
    }
  }
  
  // Initialize results
  let products = [];
  let bales = [];
  let creators = [];
  let total = 0;
  
  // Search products
  if (type === 'all' || type === 'products') {
    const productQuery = {
      $or: [
        { name: searchRegex },
        { description: searchRegex },
        { 'specifications.mainMaterial': searchRegex },
        { brand: searchRegex },
        { tags: searchRegex }
      ],
      status: 'active',
      ...filters
    };
    
    products = await Product.find(productQuery)
      .sort(sortBy)
      .skip(skip)
      .limit(type === 'all' ? Math.floor(limit / 2) : limit)
      .select('name brand basePrice images ratingsAverage gender variations');
    
    if (type === 'products') {
      total = await Product.countDocuments(productQuery);
    }
  }
  
  // Search bales
  if (type === 'all' || type === 'bales') {
    const baleQuery = {
      $or: [
        { name: searchRegex },
        { description: searchRegex },
        { country: searchRegex },
        { tags: searchRegex }
      ],
      status: 'active',
      type: 'bale',
      ...baleFilters
    };

    bales = await Product.find(baleQuery)
      .sort(sortBy)
      .skip(skip)
      .limit(type === 'all' ? Math.floor(limit / 2) : limit)
      .select('name basePrice images ratingsAverage country variations');

    if (type === 'bales') {
      total = await Product.countDocuments(baleQuery);
    }
  }
  
  // Search creators
  if (type === 'all' || type === 'creators') {
    const creatorQuery = {
      $or: [
        { name: searchRegex },
        { 'shopInfo.name': searchRegex },
        { 'businessInfo.businessName': searchRegex }
      ],
      verificationStatus: 'verified',
      onboardingStatus: 'completed'
    };
    
    creators = await Creator.find(creatorQuery)
      .sort('-metrics.totalProducts')
      .skip(skip)
      .limit(type === 'all' ? Math.floor(limit / 3) : limit)
      .select('name photo shopInfo metrics');
    
    if (type === 'creators') {
      total = await Creator.countDocuments(creatorQuery);
    }
  }
  
  // Calculate total for 'all' type
  if (type === 'all') {
    const productCount = await Product.countDocuments({
      $or: [
        { name: searchRegex },
        { description: searchRegex },
        { 'specifications.mainMaterial': searchRegex },
        { brand: searchRegex },
        { tags: searchRegex }
      ],
      status: 'active',
      ...filters
    });
    
    const baleCount = await Bale.countDocuments({
      $or: [
        { name: searchRegex },
        { description: searchRegex },
        { country: searchRegex },
        { tags: searchRegex }
      ],
      status: 'active',
      ...baleFilters
    });
    
    const creatorCount = await Creator.countDocuments({
      $or: [
        { name: searchRegex },
        { 'shopInfo.name': searchRegex },
        { 'businessInfo.businessName': searchRegex }
      ],
      verificationStatus: 'verified',
      onboardingStatus: 'completed'
    });
    
    total = productCount + baleCount + creatorCount;
  }
  
  res.status(200).json({
    status: 'success',
    results: products.length + bales.length + creators.length,
    total,
    page,
    limit,
    data: {
      products,
      bales,
      creators
    }
  });
});

/**
 * Get search filters (categories, price ranges, etc.)
 * @route GET /api/v1/search/filters
 * @access Public
 */
exports.getSearchFilters = catchAsync(async (req, res, next) => {
  // Get categories
  const categories = await Category.find()
    .select('name')
    .sort('name');
  
  // Get price ranges for products
  const productPriceStats = await Product.aggregate([
    {
      $match: { status: 'active' }
    },
    {
      $group: {
        _id: null,
        minPrice: { $min: '$basePrice' },
        maxPrice: { $max: '$basePrice' },
        avgPrice: { $avg: '$basePrice' }
      }
    }
  ]);
  
  // Get price ranges for bales
  const balePriceStats = await Bale.aggregate([
    {
      $match: { status: 'active' }
    },
    {
      $group: {
        _id: null,
        minPrice: { $min: '$basePrice' },
        maxPrice: { $max: '$basePrice' },
        avgPrice: { $avg: '$basePrice' }
      }
    }
  ]);
  
  // Get countries for bales
  const countries = await Bale.aggregate([
    {
      $match: { status: 'active' }
    },
    {
      $group: {
        _id: '$country'
      }
    },
    {
      $sort: { _id: 1 }
    }
  ]);
  
  // Get top creators
  const topCreators = await Creator.find({
    verificationStatus: 'verified',
    onboardingStatus: 'completed'
  })
    .sort('-metrics.totalProducts')
    .limit(10)
    .select('name photo shopInfo');
  
  res.status(200).json({
    status: 'success',
    data: {
      categories,
      priceStats: {
        products: productPriceStats[0] || { minPrice: 0, maxPrice: 0, avgPrice: 0 },
        bales: balePriceStats[0] || { minPrice: 0, maxPrice: 0, avgPrice: 0 }
      },
      countries: countries.map(country => country._id),
      topCreators
    }
  });
});
