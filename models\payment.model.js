const mongoose = require('mongoose');

const paymentSchema = new mongoose.Schema({
  reference: {
    type: String,
    required: true,
    unique: true
  },
  currency: {
    type: String,
    default: 'GHS'
  },
  order: {
    type: mongoose.Schema.ObjectId,
    ref: 'Order',
    required: true
  },
  user: {
    type: mongoose.Schema.ObjectId,
    ref: 'User',
    required: true
  },
  amount: {
    type: Number,
    required: true
  },
  status: {
    type: String,
    enum: ['pending', 'success', 'failed'],
    default: 'pending'
  },
  gateway: {
    type: String,
    default: 'paystack'
  },
  channel: String, // card, mobile_money, etc.
  paidAt: Date,
  gatewayResponse: String,
  meta: mongoose.Schema.Types.Mixed
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes for common query patterns
// Note: reference field already has unique: true in schema definition, so no need for explicit index
paymentSchema.index({ order: 1 }, { unique: true });
paymentSchema.index({ user: 1 });
paymentSchema.index({ status: 1 });
paymentSchema.index({ createdAt: -1 });

// Virtual for formatted amount with currency
paymentSchema.virtual('formattedAmount').get(function() {
  const currencySymbols = {
    'NGN': '₦',
    'USD': '$',
    'GBP': '£',
    'EUR': '€',
    'GHS': '₵',
    'ZAR': 'R',
    'KES': 'KSh'
  };

  const symbol = currencySymbols[this.currency] || this.currency;
  return `${symbol}${this.amount.toFixed(2)}`;
});

// Virtual for payment age (time since payment)
paymentSchema.virtual('age').get(function() {
  const now = new Date();
  const created = new Date(this.createdAt);
  const diffTime = Math.abs(now - created);
  const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));

  if (diffDays === 0) {
    const diffHours = Math.floor(diffTime / (1000 * 60 * 60));
    if (diffHours === 0) {
      const diffMinutes = Math.floor(diffTime / (1000 * 60));
      return `${diffMinutes} minute${diffMinutes !== 1 ? 's' : ''} ago`;
    }
    return `${diffHours} hour${diffHours !== 1 ? 's' : ''} ago`;
  } else if (diffDays < 7) {
    return `${diffDays} day${diffDays !== 1 ? 's' : ''} ago`;
  } else if (diffDays < 30) {
    const diffWeeks = Math.floor(diffDays / 7);
    return `${diffWeeks} week${diffWeeks !== 1 ? 's' : ''} ago`;
  } else if (diffDays < 365) {
    const diffMonths = Math.floor(diffDays / 30);
    return `${diffMonths} month${diffMonths !== 1 ? 's' : ''} ago`;
  } else {
    const diffYears = Math.floor(diffDays / 365);
    return `${diffYears} year${diffYears !== 1 ? 's' : ''} ago`;
  }
});

// Virtual for status color (for UI)
paymentSchema.virtual('statusColor').get(function() {
  const statusColors = {
    'pending': 'yellow',
    'success': 'green',
    'failed': 'red'
  };

  return statusColors[this.status] || 'gray';
});

// Virtual for payment receipt URL
paymentSchema.virtual('receiptUrl').get(function() {
  if (this.gateway === 'paystack' && this.reference) {
    return `https://dashboard.paystack.com/#/transactions/${this.reference}`;
  }
  return null;
});


// Method to mark payment as successful
paymentSchema.methods.markAsSuccessful = async function(paymentDetails = {}) {
  this.status = 'success';
  this.paidAt = paymentDetails.paidAt || Date.now();
  this.gatewayResponse = paymentDetails.gatewayResponse || 'Approved';

  if (paymentDetails.meta) {
    this.meta = paymentDetails.meta;
  }

  // Update the associated order if it exists
  if (this.order) {
    try {
      const Order = mongoose.model('Order');
      const order = await Order.findById(this.order);

      if (order) {
        // Pass payment result to the order
        await order.markAsPaid({
          reference: this.reference,
          status: 'success',
          channel: this.channel,
          paidAt: this.paidAt,
          gatewayResponse: this.gatewayResponse
        });

        // Send payment received notification
        try {
          const NotificationService = require('../services/notification.service');

          // Send notification to buyer
          await NotificationService.createPaymentNotification({
            payment: this,
            type: 'payment_received',
            recipient: order.user
          });
        } catch (notifyErr) {
          // Log error but don't stop the payment from being marked as successful
          console.error('Error sending payment notification:', notifyErr);
        }
      }
    } catch (err) {
      // Log error but don't stop the payment from being marked as successful
      console.error('Error updating order:', err);
    }
  }

  return this.save();
};

// Method to mark payment as failed
paymentSchema.methods.markAsFailed = async function(failureDetails = {}) {
  this.status = 'failed';
  this.gatewayResponse = failureDetails.gatewayResponse || 'Payment failed';

  // Save the payment first
  await this.save();

  // Send payment failed notification
  try {
    if (this.order) {
      const Order = mongoose.model('Order');
      const order = await Order.findById(this.order);

      if (order) {
        const NotificationService = require('../services/notification.service');

        // Send notification to buyer
        await NotificationService.createPaymentNotification({
          payment: this,
          type: 'payment_failed',
          recipient: order.user
        });
      }
    }
  } catch (error) {
    // Log error but don't stop the payment from being marked as failed
    console.error('Error sending payment failed notification:', error);
  }

  return this;
};

// Static method to create a new payment (simplified)
paymentSchema.statics.createPayment = async function(orderData) {
  const reference = orderData.reference || `psk_${Date.now()}_${Math.floor(Math.random() * 1000000)}`;

  // Create payment object
  const payment = await this.create({
    reference,
    order: orderData.orderId,
    user: orderData.userId,
    amount: orderData.amount,
    currency: orderData.currency || 'GHS',
    channel: orderData.channel || 'card',
    gateway: orderData.gateway || 'paystack',
    status: 'pending',
    meta: orderData.meta || {}
  });

  return payment;
};

// Static method to find payments by date range
paymentSchema.statics.findByDateRange = async function(startDate, endDate, filters = {}) {
  const query = {
    createdAt: {
      $gte: new Date(startDate),
      $lte: new Date(endDate)
    },
    ...filters
  };

  return this.find(query).sort({ createdAt: -1 });
};

// Static method to get payment statistics (simplified)
paymentSchema.statics.getStatistics = async function(startDate = null, endDate = null) {
  const matchStage = {};

  if (startDate || endDate) {
    matchStage.createdAt = {};
    if (startDate) matchStage.createdAt.$gte = new Date(startDate);
    if (endDate) matchStage.createdAt.$lte = new Date(endDate);
  }

  const stats = await this.aggregate([
    { $match: matchStage },
    {
      $group: {
        _id: null,
        totalPayments: { $sum: 1 },
        totalAmount: { $sum: '$amount' },
        averageAmount: { $avg: '$amount' },
        successfulPayments: {
          $sum: { $cond: [{ $eq: ['$status', 'success'] }, 1, 0] }
        },
        failedPayments: {
          $sum: { $cond: [{ $eq: ['$status', 'failed'] }, 1, 0] }
        },
        pendingPayments: {
          $sum: { $cond: [{ $eq: ['$status', 'pending'] }, 1, 0] }
        }
      }
    },
    {
      $project: {
        _id: 0,
        totalPayments: 1,
        totalAmount: 1,
        averageAmount: 1,
        successfulPayments: 1,
        failedPayments: 1,
        pendingPayments: 1,
        successRate: {
          $multiply: [
            { $divide: ['$successfulPayments', { $max: ['$totalPayments', 1] }] },
            100
          ]
        },
        failureRate: {
          $multiply: [
            { $divide: ['$failedPayments', { $max: ['$totalPayments', 1] }] },
            100
          ]
        }
      }
    }
  ]);

  return stats.length > 0 ? stats[0] : {
    totalPayments: 0,
    totalAmount: 0,
    averageAmount: 0,
    successfulPayments: 0,
    failedPayments: 0,
    pendingPayments: 0,
    successRate: 0,
    failureRate: 0
  };
};

const Payment = mongoose.model('Payment', paymentSchema);

module.exports = Payment;

