const mongoose = require('mongoose');

const notificationSchema = new mongoose.Schema(
  {
    recipient: {
      type: mongoose.Schema.ObjectId,
      ref: 'User',
      required: [true, 'Notification must have a recipient']
    },
    sender: {
      type: mongoose.Schema.ObjectId,
      ref: 'User'
    },
    type: {
      type: String,
      enum: [
        // Order related
        'order_placed',
        'order_status_update',
        'order_shipped',
        'order_delivered',
        'order_cancelled',
        'order_refunded',

        // Payment related
        'payment_received',
        'payment_failed',
        'payment_refunded',

        // Product related
        'product_review',
        'product_question',
        'product_answer',
        'product_approved',
        'product_rejected',
        'low_stock',
        'out_of_stock',
        'price_drop',
        'product_featured',

        // Bale related
        'bale_approved',
        'bale_rejected',
        'bale_low_stock',
        'bale_out_of_stock',

        // Creator related
        'creator_application',
        'creator_verified',
        'creator_rejected',
        'creator_featured',

        // Payout related
        'payout_processed',
        'payout_failed',
        'payout_initiated',

        // Wallet related
        'wallet_transaction',
        'wallet_low_balance',
        'wallet_adjustment',

        // Promotion related
        'promotion',
        'promotion_ending',
        'flash_sale_started',
        'flash_sale_ending',

        // System related
        'account_update',
        'system_message',
        'new_feature',
        'maintenance',

        // Social related
        'new_follower',
        'message_received'
      ],
      required: [true, 'Notification must have a type']
    },
    title: {
      type: String,
      required: [true, 'Notification must have a title']
    },
    message: {
      type: String,
      required: [true, 'Notification must have a message']
    },
    read: {
      type: Boolean,
      default: false
    },
    readAt: Date,
    // Delivery channels - which channels this notification has been sent through
    deliveryChannels: {
      inApp: {
        type: Boolean,
        default: true
      },
      email: {
        type: Boolean,
        default: false,
        sent: {
          type: Boolean,
          default: false
        },
        sentAt: Date
      },
      push: {
        type: Boolean,
        default: false,
        sent: {
          type: Boolean,
          default: false
        },
        sentAt: Date
      },
      sms: {
        type: Boolean,
        default: false,
        sent: {
          type: Boolean,
          default: false
        },
        sentAt: Date
      }
    },
    // Related data for the notification
    data: {
      order: {
        type: mongoose.Schema.ObjectId,
        ref: 'Order'
      },
      product: {
        type: mongoose.Schema.ObjectId,
        ref: 'Product'
      },
      bale: {
        type: mongoose.Schema.ObjectId,
        ref: 'Bale'
      },
      review: {
        type: mongoose.Schema.ObjectId,
        ref: 'Review'
      },
      promotion: {
        type: mongoose.Schema.ObjectId,
        ref: 'Promotion'
      },
      payment: {
        type: mongoose.Schema.ObjectId,
        ref: 'Payment'
      },
      payout: {
        type: mongoose.Schema.ObjectId,
        ref: 'Payout'
      },
      wallet: {
        type: mongoose.Schema.ObjectId,
        ref: 'Wallet'
      },
      transaction: {
        type: mongoose.Schema.ObjectId,
        ref: 'Transaction'
      },
      creator: {
        type: mongoose.Schema.ObjectId,
        ref: 'User'
      },
      buyer: {
        type: mongoose.Schema.ObjectId,
        ref: 'User'
      },
      url: String,
      additionalData: Object
    },
    priority: {
      type: String,
      enum: ['low', 'medium', 'high'],
      default: 'medium'
    },
    expiresAt: Date
  },
  {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true }
  }
);

// Indexes
notificationSchema.index({ recipient: 1, read: 1 });
notificationSchema.index({ createdAt: -1 });
notificationSchema.index({ expiresAt: 1 }, { expireAfterSeconds: 0 });

// Query middleware
notificationSchema.pre(/^find/, function(next) {
  this.populate({
    path: 'sender',
    select: 'name photo'
  });

  if (this._mongooseOptions.populate) {
    // Populate order data if requested
    if (this._mongooseOptions.populate['data.order']) {
      this.populate({
        path: 'data.order',
        select: 'total status orderNumber items'
      });
    }

    // Populate product data if requested
    if (this._mongooseOptions.populate['data.product']) {
      this.populate({
        path: 'data.product',
        select: 'name images discount'
      });
    }

    // Populate bale data if requested
    if (this._mongooseOptions.populate['data.bale']) {
      this.populate({
        path: 'data.bale',
        select: 'name images price discount'
      });
    }

    // Populate review data if requested
    if (this._mongooseOptions.populate['data.review']) {
      this.populate({
        path: 'data.review',
        select: 'rating comment title'
      });
    }

    // Populate promotion data if requested
    if (this._mongooseOptions.populate['data.promotion']) {
      this.populate({
        path: 'data.promotion',
        select: 'name description discountPercentage startDate endDate'
      });
    }

    // Populate payment data if requested
    if (this._mongooseOptions.populate['data.payment']) {
      this.populate({
        path: 'data.payment',
        select: 'amount status reference'
      });
    }

    // Populate payout data if requested
    if (this._mongooseOptions.populate['data.payout']) {
      this.populate({
        path: 'data.payout',
        select: 'amount status reference'
      });
    }

    // Populate wallet data if requested
    if (this._mongooseOptions.populate['data.wallet']) {
      this.populate({
        path: 'data.wallet',
        select: 'balance availableBalance'
      });
    }

    // Populate creator data if requested
    if (this._mongooseOptions.populate['data.creator']) {
      this.populate({
        path: 'data.creator',
        select: 'name photo'
      });
    }

    // Populate buyer data if requested
    if (this._mongooseOptions.populate['data.buyer']) {
      this.populate({
        path: 'data.buyer',
        select: 'name photo'
      });
    }
  }

  next();
});

// Mark notification as read
notificationSchema.methods.markAsRead = function() {
  this.read = true;
  this.readAt = Date.now();
  return this.save();
};

// Mark notification as sent via email
notificationSchema.methods.markAsEmailSent = function() {
  if (this.deliveryChannels && this.deliveryChannels.email) {
    this.deliveryChannels.email.sent = true;
    this.deliveryChannels.email.sentAt = Date.now();
  }
  return this.save();
};

// Mark notification as sent via push
notificationSchema.methods.markAsPushSent = function() {
  if (this.deliveryChannels && this.deliveryChannels.push) {
    this.deliveryChannels.push.sent = true;
    this.deliveryChannels.push.sentAt = Date.now();
  }
  return this.save();
};

// Mark notification as sent via SMS
notificationSchema.methods.markAsSMSSent = function() {
  if (this.deliveryChannels && this.deliveryChannels.sms) {
    this.deliveryChannels.sms.sent = true;
    this.deliveryChannels.sms.sentAt = Date.now();
  }
  return this.save();
};

// Get unread notifications count for a user
notificationSchema.statics.getUnreadCount = async function(userId) {
  return this.countDocuments({ recipient: userId, read: false });
};

// Get notifications for a user with pagination
notificationSchema.statics.getForUser = async function(userId, options = {}) {
  const { page = 1, limit = 10, read, type, priority, populate = [] } = options;

  const query = { recipient: userId };

  if (read !== undefined) {
    query.read = read;
  }

  if (type) {
    query.type = type;
  }

  if (priority) {
    query.priority = priority;
  }

  const populateOptions = {};
  if (populate.length > 0) {
    populate.forEach(field => {
      populateOptions[`data.${field}`] = true;
    });
  }

  const notifications = await this.find(query)
    .sort({ createdAt: -1 })
    .skip((page - 1) * limit)
    .limit(limit)
    .setOptions({ populate: populateOptions });

  const total = await this.countDocuments(query);

  return {
    notifications,
    pagination: {
      total,
      page,
      limit,
      pages: Math.ceil(total / limit)
    }
  };
};

// Mark all notifications as read for a user
notificationSchema.statics.markAllAsRead = async function(userId) {
  const result = await this.updateMany(
    { recipient: userId, read: false },
    { read: true, readAt: Date.now() }
  );

  return result.nModified;
};

const Notification = mongoose.model('Notification', notificationSchema);

module.exports = Notification;
