const express = require('express');
const router = express.Router();
const feeConfigController = require('../../controllers/admin/feeConfig.controller');
const shippingFeeController = require('../../controllers/admin/shippingFee.controller');
const { protect, restrictTo } = require('../../middleware/auth.middleware');

// Protect all fee routes
router.use(protect);
router.use(restrictTo('admin'));

// ===== Platform & Processing Fee Routes =====
router.route('/config/current')
  .get(feeConfigController.getCurrentFeeConfig);

router.route('/config')
  .get(feeConfigController.getAllFeeConfigs)
  .post(feeConfigController.createFeeConfig);

router.route('/config/:id')
  .get(feeConfigController.getFeeConfig)
  .patch(feeConfigController.updateFeeConfig)
  .delete(feeConfigController.deleteFeeConfig);

// ===== Shipping Fee Routes =====
router.route('/shipping/calculate')
  .post(shippingFeeController.calculateShippingFee);

router.route('/shipping')
  .get(shippingFeeController.getAllShippingFees)
  .post(shippingFeeController.createShippingFee);

router.route('/shipping/:id')
  .get(shippingFeeController.getShippingFee)
  .patch(shippingFeeController.updateShippingFee)
  .delete(shippingFeeController.deleteShippingFee);

module.exports = router;
