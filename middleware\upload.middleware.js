const multer = require('multer');
const path = require('path');
const fs = require('fs');
const AppError = require('../utils/appError');

// Set storage engine
const storage = multer.diskStorage({
  destination: function(req, file, cb) {
    // Set the destination based on file type
    let uploadPath = 'public/uploads/';

    if (file.fieldname.includes('verification')) {
      uploadPath += 'verification/';
    } else if (file.fieldname.includes('product')) {
      uploadPath += 'products/';
    } else if (file.fieldname.includes('bale')) {
      uploadPath += 'bales/';
    } else if (file.fieldname.includes('profile')) {
      uploadPath += 'profiles/';
    } else {
      uploadPath += 'others/';
    }

    cb(null, uploadPath);
  },
  filename: function(req, file, cb) {
    // Create a unique filename with timestamp and original extension
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    const ext = path.extname(file.originalname);
    cb(null, file.fieldname + '-' + uniqueSuffix + ext);
  }
});

// Check file type
const fileFilter = (req, file, cb) => {
  // Allowed file extensions
  const fileTypes = /jpeg|jpg|png|gif|pdf/;
  // Check extension
  const extname = fileTypes.test(path.extname(file.originalname).toLowerCase());
  // Check mime type
  const mimetype = fileTypes.test(file.mimetype);

  if (mimetype && extname) {
    return cb(null, true);
  } else {
    cb(new AppError('Error: Images and PDFs only!', 400));
  }
};

// Initialize upload
const upload = multer({
  storage: storage,
  limits: { fileSize: 10 * 1024 * 1024 }, // 10MB max file size
  fileFilter: fileFilter
});

// Middleware for verification document uploads
exports.uploadVerificationDocuments = upload.array('verificationDocuments', 5); // Max 5 files

// Middleware for product images
exports.uploadProductImages = upload.array('productImages', 10); // Max 10 files

// Middleware for bale images
exports.uploadBaleImages = upload.array('baleImages', 10); // Max 10 files

// Middleware for profile photo
exports.uploadProfilePhoto = upload.single('profilePhoto');

// Middleware for shop logo and banner
exports.uploadShopMedia = upload.fields([
  { name: 'logo', maxCount: 1 },
  { name: 'banner', maxCount: 1 }
]);

// Middleware for shipping info form data
exports.uploadShippingInfo = upload.none();

// Process uploaded files and add file paths to req.body
exports.processUploadedFiles = (req, res, next) => {
  if (!req.files && !req.file) return next();

  // For multiple files (arrays)
  if (req.files) {
    // Check if it's an array of files
    if (Array.isArray(req.files) && req.files.length > 0) {
      // Get the field name from the first file
      const fieldName = req.files[0].fieldname;

      // Create an array of file paths
      const filePaths = req.files.map(file => {
        // Convert backslashes to forward slashes for consistency
        return file.path.replace(/\\/g, '/');
      });

      // Add file paths to req.body based on field name
      if (fieldName === 'verificationDocuments') {
        req.body.verificationDocuments = filePaths;
      } else if (fieldName === 'productImages') {
        req.body.images = filePaths;
      } else if (fieldName === 'baleImages') {
        req.body.images = filePaths;
      }
    }
    // Check if it's an object with file fields (for fields())
    else if (typeof req.files === 'object') {
      // Process each field
      Object.keys(req.files).forEach(fieldName => {
        if (req.files[fieldName] && req.files[fieldName].length > 0) {
          // Convert backslashes to forward slashes for consistency
          const filePath = req.files[fieldName][0].path.replace(/\\/g, '/');

          // Add file path to req.body based on field name
          req.body[fieldName] = filePath;
        }
      });
    }
  }

  // For single file
  if (req.file) {
    // Convert backslashes to forward slashes for consistency
    const filePath = req.file.path.replace(/\\/g, '/');

    if (req.file.fieldname === 'profilePhoto') {
      req.body.photo = filePath;
    }
  }

  next();
};

// Error handler for multer
exports.handleMulterError = (err, req, res, next) => {
  if (err instanceof multer.MulterError) {
    if (err.code === 'LIMIT_FILE_SIZE') {
      return next(new AppError('File too large. Maximum size is 10MB', 400));
    }
    return next(new AppError(`Upload error: ${err.message}`, 400));
  }

  next(err);
};

// Middleware for processing form data with nested fields
exports.processNestedFormData = (req, res, next) => {
  if (!req.body || Object.keys(req.body).length === 0) {
    return next();
  }



  // Process form data for nested objects
  const processedBody = {};

  // Process each field in the request body
  Object.keys(req.body).forEach(key => {
    // Check if the key contains brackets (indicating a nested field)
    if (key.includes('[') && key.includes(']')) {
      // Extract the parent key and child key
      const matches = key.match(/([^\[]+)\[([^\]]+)\]/);
      if (matches && matches.length === 3) {
        const parentKey = matches[1];
        const childKey = matches[2];

        // Initialize the parent object if it doesn't exist
        if (!processedBody[parentKey]) {
          processedBody[parentKey] = {};
        }

        // Set the child property
        processedBody[parentKey][childKey] = req.body[key];
      }
    } else {
      // For non-nested fields, copy as is
      processedBody[key] = req.body[key];
    }
  });

  // Special handling for arrays
  // Process array fields (fields ending with [])
  const arrayFields = {};
  Object.keys(req.body).forEach(key => {
    if (key.endsWith('[]')) {
      const fieldName = key.slice(0, -2); // Remove the [] suffix
      if (!arrayFields[fieldName]) {
        arrayFields[fieldName] = [];
      }
      arrayFields[fieldName].push(req.body[key]);
    }
  });

  // Add processed arrays to the processed body
  Object.keys(arrayFields).forEach(key => {
    processedBody[key] = arrayFields[key];
  });

  // Handle shipping methods as array if it's a string
  if (processedBody.methods && typeof processedBody.methods === 'string') {
    try {
      processedBody.methods = JSON.parse(processedBody.methods);
    } catch (err) {
      // Error parsing shipping methods - continue with original value
    }
  }

  // Handle sales channels as array if it's a comma-separated string
  if (processedBody.salesChannels && typeof processedBody.salesChannels === 'string') {
    processedBody.salesChannels = processedBody.salesChannels.split(',').map(item => item.trim());
  }

  // Handle specialties as array if it's a comma-separated string
  if (processedBody.sellerProfile && processedBody.sellerProfile.specialties &&
      typeof processedBody.sellerProfile.specialties === 'string') {
    processedBody.sellerProfile.specialties = processedBody.sellerProfile.specialties
      .split(',').map(item => item.trim());
  }

  // Handle product-specific arrays
  ['highlights', 'tags', 'relatedCategories'].forEach(field => {
    if (processedBody[field] && typeof processedBody[field] === 'string') {
      try {
        processedBody[field] = JSON.parse(processedBody[field]);
      } catch (err) {
        // Error parsing field - try comma-separated format instead
        // If parsing fails, try treating it as a comma-separated string
        processedBody[field] = processedBody[field].split(',').map(item => item.trim());
      }
    }
  });

  // Convert sellsOffline to boolean if it's a string
  if (processedBody.sellsOffline === 'true') {
    processedBody.sellsOffline = true;
  } else if (processedBody.sellsOffline === 'false') {
    processedBody.sellsOffline = false;
  }

  // Convert returnAddress.useSameAddress to boolean if it's a string
  if (processedBody.returnAddress && processedBody.returnAddress.useSameAddress === 'true') {
    processedBody.returnAddress.useSameAddress = true;
  } else if (processedBody.returnAddress && processedBody.returnAddress.useSameAddress === 'false') {
    processedBody.returnAddress.useSameAddress = false;
  }

  // Replace the original req.body with the processed body
  req.body = processedBody;

  next();
};
