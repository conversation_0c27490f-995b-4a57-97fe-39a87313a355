<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Order Confirmation #<%= orderNumber %></title>
  <style>
    body {
      font-family: Arial, sans-serif;
      line-height: 1.6;
      color: #333;
      margin: 0;
      padding: 0;
    }
    .container {
      max-width: 600px;
      margin: 0 auto;
      padding: 20px;
    }
    .header {
      background-color: #f8f9fa;
      padding: 20px;
      text-align: center;
      border-bottom: 1px solid #ddd;
    }
    .logo {
      max-width: 150px;
      height: auto;
    }
    .content {
      padding: 20px;
    }
    .footer {
      background-color: #f8f9fa;
      padding: 20px;
      text-align: center;
      font-size: 12px;
      color: #777;
      border-top: 1px solid #ddd;
    }
    .button {
      display: inline-block;
      background-color: #007bff;
      color: white;
      text-decoration: none;
      padding: 10px 20px;
      border-radius: 4px;
      margin-top: 20px;
    }
    .order-box {
      background-color: #f8f9fa;
      border-left: 4px solid #28a745;
      padding: 15px;
      margin: 20px 0;
    }
    table {
      width: 100%;
      border-collapse: collapse;
      margin: 20px 0;
    }
    th, td {
      padding: 10px;
      text-align: left;
      border-bottom: 1px solid #ddd;
    }
    th {
      background-color: #f8f9fa;
    }
    .address-box {
      background-color: #f8f9fa;
      padding: 15px;
      margin: 20px 0;
      border-radius: 4px;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <img src="https://via.placeholder.com/150x50?text=Everyfash" alt="Everyfash Logo" class="logo">
    </div>
    
    <div class="content">
      <h2>Order Confirmation</h2>
      
      <div class="order-box">
        <p>Thank you for your order, <%= name %>!</p>
        <p>We've received your order and are working on it right away.</p>
        <p><strong>Order Number:</strong> <%= orderNumber %></p>
        <p><strong>Order Date:</strong> <%= orderDate %></p>
      </div>
      
      <h3>Order Summary</h3>
      
      <table>
        <thead>
          <tr>
            <th>Item</th>
            <th>Quantity</th>
            <th>Price</th>
          </tr>
        </thead>
        <tbody>
          <% orderItems.forEach(item => { %>
            <tr>
              <td>
                <%= item.name %>
                <% if (item.color && item.size) { %>
                  <br><small><%= item.color %>, <%= item.size %></small>
                <% } %>
              </td>
              <td><%= item.quantity %></td>
              <td><%= item.price.toFixed(2) %> GHS</td>
            </tr>
          <% }); %>
        </tbody>
        <tfoot>
          <tr>
            <td colspan="2"><strong>Subtotal</strong></td>
            <td><%= (orderTotal - shippingAddress.shippingCost).toFixed(2) %> GHS</td>
          </tr>
          <tr>
            <td colspan="2"><strong>Shipping</strong></td>
            <td><%= shippingAddress.shippingCost.toFixed(2) %> GHS</td>
          </tr>
          <tr>
            <td colspan="2"><strong>Total</strong></td>
            <td><strong><%= orderTotal.toFixed(2) %> GHS</strong></td>
          </tr>
        </tfoot>
      </table>
      
      <h3>Shipping Information</h3>
      
      <div class="address-box">
        <p><strong><%= shippingAddress.fullName %></strong></p>
        <p><%= shippingAddress.address %></p>
        <% if (shippingAddress.address2) { %>
          <p><%= shippingAddress.address2 %></p>
        <% } %>
        <p><%= shippingAddress.city %>, <%= shippingAddress.region %></p>
        <p><%= shippingAddress.country %></p>
        <p><strong>Phone:</strong> <%= shippingAddress.phone %></p>
      </div>
      
      <h3>Payment Method</h3>
      <p><%= paymentMethod %></p>
      
      <a href="<%= actionUrl %>" class="button"><%= actionText %></a>
      
      <p>If you have any questions about your order, please contact our customer support.</p>
    </div>
    
    <div class="footer">
      <p>&copy; <%= new Date().getFullYear() %> Everyfash. All rights reserved.</p>
      <p>
        <a href="#">Unsubscribe</a> | 
        <a href="#">Privacy Policy</a> | 
        <a href="#">Terms of Service</a>
      </p>
    </div>
  </div>
</body>
</html>
